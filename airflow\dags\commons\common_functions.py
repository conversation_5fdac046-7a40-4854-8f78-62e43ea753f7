# In-built imports...
from datetime import datetime
import time, csv, os
from io import String<PERSON>
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from base64 import b64encode
import os, sys, string, random
from airflow.utils.db import provide_session
from airflow.models import XCom
# Custom imports...
# from commons.configs import DKE<PERSON>, STANDARD_IV, TMML_CO_CODE, TMML_CO_CODE_0, VALID_CSV_FORMATS, COLUMN_COUNT, REQUIRED_COLUMNS, MANDATORY_AD_FIELDS, COMMA, AD_ATTRIBUTES
from commons.configs import DKEY, STANDARD_IV, VALID_CSV_FORMATS, COLUMN_COUNT, REQUIRED_COLUMNS, MANDATORY_AD_FIELDS, COMMA, AD_ATTRIBUTES, TMML_CO_CODE, TMML_CO_CODE_0, TMML_CO_DEP_CODE, TMML_CO_DEP_CODE_0
from commons.messages import DO<PERSON>_IS_EMPTY, DOJ_IS_EMPTY, FA<PERSON><PERSON>, NO_DATA_FOUND, EMPTY_FILE, INCORRE<PERSON>_COLUMN_COUNT, COLUMN_NAME_DID_NOT_MATCHED, SKIP
from commons.notifications import send_failed_notification
# from commons.models import FileLogs, FileRowLogs, KeycloakESubGroup, SapCompanyMaster, TTLTempTable, ManualTempTable, ESubGroup
from commons.models_v2 import ProcessLogs, ProcessRowLogs, KeycloakESubGroup, SapCompanyMaster, TTLTempTable, ManualTempTable, ESubGroup


@provide_session
def cleanup_xcom(session=None, **context):
    dag = context["dag"]
    dag_id = dag._dag_id 
    # delete all xcom of the dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()

def generate_password(length=10):
    uppercase_letters = string.ascii_uppercase
    lowercase_letters = string.ascii_lowercase
    special_characters = "!#$@"
    password = [
        random.choice(uppercase_letters),
        random.choice(lowercase_letters),
        random.choice(special_characters)
    ]
    remaining_length = length - len(password)
    all_characters = uppercase_letters + lowercase_letters + string.digits + special_characters
    password += [random.choice(all_characters) for _ in range(remaining_length)]
    random.shuffle(password)
    password_str = ''.join(password)

    return password_str

def print_error(func_name, error = ""):
    try:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb:
            file_name = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            error = f'Error - {error} \n Exception - {exc_type} \n File Name - {file_name} \n Function Name - {func_name} \n Line No - {exc_tb.tb_lineno}'
            print(error)
            return error
        else:
            error = f'{str(func_name)} : {str(error)}'
            print(error)
            return error
    except Exception as error:
        print(f'print_error(): {error}')

def encrypt_content(data):
    try:
        key = DKEY 
        iv = STANDARD_IV
        key = key.encode('utf-8') #16 char for AES128
        iv =  iv.encode('utf-8') #16 char for AES128
        data = pad(data.encode(),16)
        cipher = AES.new(key,AES.MODE_CBC,iv)
        cipher_data = cipher.encrypt(data)
        cipher_data = b64encode(cipher_data)
        cipher_data = cipher_data.decode("utf-8")
        return cipher_data
    except Exception as error:
        print(f'encrypt_content(): {error}')
        return None

def cleaner(param):
    try:
        if param == None or param == 'None':
            param = ""
        param = param.replace('.','')
        if type(param) == str:
            param = param.strip()
        if param.isnumeric():
            param = str(int(param))
        if param == "":
            param = None
        return param
    except Exception as error:
        print(f'cleaner(): {error}')
        return None
    
def cleaner_perno(param):
    try:
        if param == None or param == 'None':
            param = ""
        param = param.replace('.','')
        if type(param) == str:
            param = param.strip()
        if param.isnumeric():
            param = str(int(param))
        if (len(param) >= 6) and (len(param) <= 8):
            param = param
        if param == "":
            param = None
        return param
    except Exception as error:
        print(f'cleaner_perno(): {error}')
        return None

def name_cleaner(param):
    try:
        if param == None:
            param = ""
        param = param.strip()
        return cleaner(param)
    except Exception as error:
        print("name_cleaner(): ", error)
        return None

def add_cleaner(param):
    try: 
        if param == None or param == 'None':
            param = ""
        if type(param) == str:
            param = param.strip()
        if param == "":
            param = None
        return param    
    except Exception as error:
        print(f'add_cleaner(): {error}')
        return None

def format_date(date, date_failed_reason=None, column = ''):
    try:
        try:
            if date == 'None' or date == '' or date == None:
                return '0000-00-00', date_failed_reason
            elif date == '0000-00-00' or date == '00-00-0000':
                return str(date), date_failed_reason
            date = datetime.strptime(date[:10], '%Y-%m-%d')
        except:
            date = datetime.strptime(date[:10], '%d-%m-%Y')
        return str(date), date_failed_reason
    except Exception as error:
        print(f'format_date : {error}')
        return date, f'Invalid date format in {column} - {date}'

def create_file_log(session, file_log_object):
    file_log = ProcessLogs()
    file_log.file_id = "FLG"+str(int(time.time_ns() * 10))
    file_log.job_name = file_log_object['job_name'] if "job_name" in file_log_object else  ""
    file_log.file_name = file_log_object["file_name"] if "file_name" in file_log_object else ""
    file_log.trigger_type = file_log_object["trigger_type"] if "trigger_type" in file_log_object else ""
    file_log.file_format = file_log_object["file_format"] if "file_format" in file_log_object else ""
    file_log.status = file_log_object["status"] if "status" in file_log_object else ""
    file_log.row_count = file_log_object["row_count"] if "row_count" in file_log_object else ""
    file_log.column_count = file_log_object["column_count"] if "column_count" in file_log_object else ""
    file_log.started_at = datetime.now()
    file_log.created_by = file_log_object["trigger_by"] if "trigger_by" in file_log_object else "System"
    if "row_count" in file_log_object and not file_log_object["row_count"] > 0:
        file_log.status = FAILED
        file_log.reason = NO_DATA_FOUND
        session.add(file_log)
        session.commit()
        send_failed_notification(file_log)
        return file_log
    session.add(file_log)
    session.commit()
    return file_log

def update_file_log(session, file_log=None, job_name=None, file_name=None, file_format=None, row_count=None, column_count=None, status=None, reason=None, trigger_type=None, commit=None):
    is_create = False
    if file_log is None:
        file_log = ProcessLogs()
        is_create = True
        file_log.file_id = "FLG"+str(int(time.time_ns() * 10))
    if file_name is not None:
        file_log.file_name = file_name
    if job_name is not None:
        file_log.job_name = job_name
    if file_format is not None:
        file_log.file_format = file_format
    if row_count is not None:
        file_log.row_count = row_count
    if column_count is not None:
        file_log.column_count = column_count
    if status is not None:
        file_log.status = status
    if reason is not None:
        file_log.reason = reason
    if trigger_type is not None:
        file_log.trigger_type = trigger_type
    if is_create:
        file_log.started_at = datetime.now()
        file_log.ended_at = datetime.now()
        session.add(file_log)
    if commit is not None:
        file_log.ended_at = datetime.now()
        session.commit()
    return file_log


def update_file_row_log(
    session,
    file_row_log=None,
    perno=None,
    comp_code=None,
    row_count=None,
    ad_operation=None,
    aws_cet_operation=None,
    azure_cet_operation=None,
    edp_operation=None,
    nextgen_operation=None,
    keycloak_operation=None,
    ad_status=None,
    aws_cet_status=None,
    azure_cet_status=None,
    edp_status=None,
    nextgen_status=None,
    keycloak_status = None,
    ad_reason=None,
    aws_cet_reason=None,
    azure_cet_reason=None,
    edp_reason=None,
    nextgen_reason=None,
    keycloak_reason=None,
    resolved_file_id=None,
    resolved_file_row_id=None,
    is_upn_update=None,
    file_id=None,
    commit=True,
):
    is_create = False
    if file_row_log is None:
        file_row_log = ProcessRowLogs()
        is_create = True
        file_row_log.file_row_id = "ROW" + str(int(time.time_ns() * 10))
    if perno is not None:
        file_row_log.perno = perno
    if comp_code is not None:
        file_row_log.comp_code = comp_code
    if ad_operation is not None and comp_code not in ['TTL']:
        file_row_log.ad_operation = ad_operation
    if aws_cet_operation is not None:
        file_row_log.aws_cet_operation = aws_cet_operation
    if azure_cet_operation is not None:
        file_row_log.azure_cet_operation = azure_cet_operation
    if edp_operation is not None and comp_code not in ['TTL', TMML_CO_CODE, TMML_CO_CODE_0]:
        file_row_log.edp_operation = edp_operation
    if nextgen_operation is not None and comp_code not in ['TTL', TMML_CO_CODE, TMML_CO_CODE_0]:
        file_row_log.nextgen_operation = nextgen_operation
    if keycloak_operation is not None:
        file_row_log.keycloak_operation = keycloak_operation
    if ad_status is not None and comp_code not in ['TTL']:
        file_row_log.ad_status = ad_status
    if aws_cet_status is not None:
        file_row_log.aws_cet_status = aws_cet_status
    if azure_cet_status is not None:
        file_row_log.azure_cet_status = azure_cet_status
    if edp_status is not None and comp_code not in ['TTL', TMML_CO_CODE, TMML_CO_CODE_0]:
        file_row_log.edp_status = edp_status
    if nextgen_status is not None and comp_code not in ['TTL', TMML_CO_CODE, TMML_CO_CODE_0]:
        file_row_log.nextgen_status = nextgen_status
    if keycloak_status is not None:
        file_row_log.keycloak_status = keycloak_status
    if ad_reason is not None and comp_code not in ['TTL']:
        file_row_log.ad_reason = ad_reason
    if aws_cet_reason is not None:
        file_row_log.aws_cet_reason = aws_cet_reason
    if azure_cet_reason is not None:
        file_row_log.azure_cet_reason = azure_cet_reason
    if edp_reason is not None and comp_code not in ['TTL', TMML_CO_CODE, TMML_CO_CODE_0]:
        file_row_log.edp_reason = edp_reason
    if nextgen_reason is not None and comp_code not in ['TTL', TMML_CO_CODE, TMML_CO_CODE_0]:
        file_row_log.nextgen_reason = nextgen_reason
    if keycloak_reason is not None:
        file_row_log.keycloak_reason = keycloak_reason
    if resolved_file_id is not None:
        file_row_log.resolved_file_id = resolved_file_id
    if resolved_file_row_id is not None:
        file_row_log.resolved_file_row_id = resolved_file_row_id
    if is_upn_update is not None:
        file_row_log.is_upn_update = is_upn_update
    if file_id is not None:
        file_row_log.file_id = file_id
    if is_create:
        session.add(file_row_log)
    if commit is not None:
        session.commit()
    file_row_log.is_processed = True
    file_row_log.processed_at = datetime.now()
    return file_row_log


# Scheduled jobs...

def validate_file(file_log_object, file_data):
    try:
        if file_log_object['file_format'] not in VALID_CSV_FORMATS:
            file_log_object['status'] = FAILED 
        elif not file_data:
            file_log_object['status'] = FAILED
            file_log_object['reason'] = EMPTY_FILE
        else:
            file_data = csv.DictReader(StringIO(file_data))
            columns = file_data.fieldnames
            file_data.fieldnames = [name.lower() for name in file_data.fieldnames]
            file_data = list(file_data)
            file_log_object['row_count'] = len(file_data)
            file_log_object['column_count'] = len(columns)
            if file_log_object['column_count'] != COLUMN_COUNT:
                file_log_object['status'] = FAILED
                file_log_object['reason'] = INCORRECT_COLUMN_COUNT
            elif columns != REQUIRED_COLUMNS:
                file_log_object['status'] = FAILED
                file_log_object['reason'] = COLUMN_NAME_DID_NOT_MATCHED
            else:
                return True, file_log_object, file_data
        return False, file_log_object, file_data     
    except Exception as error:
        file_log_object['status'] = FAILED
        file_log_object['reason'] = str(error)[:60]
        print_error('validate_file',error)
        return False, file_log_object, file_data


def load_data_to_temp(oracle_session, file_data , temp_table):
    try:
        oracle_session.query(temp_table).delete()
        oracle_session.commit()
        object_list = []
        for row in file_data:
            if not row['perno']:
                row['perno'] = '00000000'
            if not row['comp_code']:
                row['comp_code'] = '000'
            if row['perno'].isdigit():
                row['perno'] = int(row['perno'])
            if row['comp_code'].isdigit():
                row['comp_code'] = int(row['comp_code'])
            object_list.append(temp_table(**row))
        oracle_session.add_all(object_list)
        oracle_session.commit() 
        return True, None 
    except Exception as error:
        print_error(f'load_data_to_temp() : {error}')
        return False, error

def load_data_to_temp_ttl(oracle_session, file_data):
    try:
        oracle_session.query(TTLTempTable).delete()
        oracle_session.commit()
        object_list = []
        for row in file_data:
            emp_info = {}
            if not row['perno']:
                row['perno'] = '00000000'
            if not row['compcode']:
                row['compcode'] = '000'
            if row['perno'].isdigit():
                row['perno'] = int(row['perno'])
            if row['compcode'].isdigit():
                row['compcode'] = int(row['compcode'])
            emp_info["perno"] = row.get("Perno".lower())
            emp_info["comp_code"] = row.get("CompCode".lower())
            emp_info["pers_area"] = row.get("PersArea".lower())
            emp_info["patxt"] = row.get("Patxt".lower())
            emp_info["egroup"] = row.get("Egroup".lower())
            emp_info["ptext"] = row.get("Ptext".lower())
            emp_info["esubgroup"] = row.get("Esubgroup".lower())
            emp_info["esgtxt"] = row.get("Esgtxt".lower())
            emp_info["ccodetxt"] = row.get("Ccodetxt".lower())
            emp_info["bus_area"] = row.get("BusArea".lower())
            emp_info["bustxt"] = row.get("Bustxt".lower())
            emp_info["p_subarea"] = row.get("PSubarea".lower())
            emp_info["psatxt"] = row.get("Psatxt".lower())
            emp_info["payarea"] = row.get("Payarea".lower())
            emp_info["paytxt"] = row.get("Paytxt".lower())
            emp_info["costcenter"] = row.get("Costcenter".lower())
            emp_info["cosctrtxt"] = row.get("Cosctrtxt".lower())
            emp_info["position1"] = row.get("Position1".lower())
            emp_info["postxt"] = row.get("Postxt".lower())
            emp_info["job"] = row.get("Job".lower())
            emp_info["jobtxt"] = row.get("Jobtxt".lower())
            emp_info["fkbtx"] = row.get("Fkbtx".lower())
            emp_info["initials"] = row.get("Initials".lower())
            emp_info["comp_name"] = row.get("CompName".lower())
            emp_info["last_name"] = row.get("LastName".lower())
            emp_info["firstname"] = row.get("Firstname".lower())
            emp_info["midnm"] = row.get("Midnm".lower())
            emp_info["title"] = row.get("Title".lower())
            emp_info["off_num"] = row.get("OffNum".lower())
            emp_info["gender"] = row.get("Gender".lower())
            emp_info["konfe"] = row.get("Konfe".lower())
            emp_info["religion"] = row.get("Religion".lower())
            emp_info["famst"] = row.get("Famst".lower())
            emp_info["mar_status"] = row.get("MarStatus".lower())
            emp_info["bloodgroup"] = row.get("Bloodgroup".lower())
            emp_info["imailid"] = row.get("Imailid".lower())
            emp_info["exmailid"] = row.get("Exmailid".lower())
            emp_info["dobsorp"] = row.get("Dobsorp".lower())
            emp_info["doj"] = row.get("Doj".lower())
            emp_info["dob"] = row.get("Dob".lower())
            emp_info["dsvcvp"] = row.get("Dsvcvp".lower())
            emp_info["dlprom"] = row.get("Dlprom".lower())
            emp_info["dosep"] = row.get("Dosep".lower())
            emp_info["seprsn"] = row.get("Seprsn".lower())
            emp_info["challenged"] = row.get("Challenged".lower())
            emp_info["reporting"] = str(int(row.get("Reporting".lower())))
            emp_info["nameofreporting"] = row.get("Nameofreporting".lower())
            emp_info["empstatus"] = row.get("Empstatus".lower())
            emp_info["empstattxt"] = row.get("Empstattxt".lower())
            emp_info["orgeh"] = row.get("Orgeh".lower())
            emp_info["orgtx"] = row.get("Orgtx".lower())
            emp_info["office_mobile"] = row.get("OfficeMobile".lower())
            emp_info["pers_mobile"] = row.get("PersMobile".lower())
            emp_info["emrg_mobile"] = row.get("EmrgMobile".lower())
            emp_info["transport_code"] = row.get("TransportCode".lower())
            emp_info["off_addr"] = row.get("OffAddr".lower())
            emp_info["sepcode"] = row.get("Sepcode".lower())
            emp_info["fun"] = row.get("Fun".lower())
            emp_info["funt"] = row.get("Funt".lower())
            emp_info["mgtxt"] = row.get("Mgtxt".lower())
            emp_info["flag1"] = row.get("Flag1".lower())
            emp_info["last_prom1"] = row.get("LastProm1".lower())
            emp_info["last_prom2"] = row.get("LastProm2".lower())
            emp_info["last_prom3"] = row.get("LastProm3".lower())
            emp_info["dummy1"] = row.get("Dummy1".lower())
            emp_info["dummy2"] = row.get("Dummy2".lower())
            emp_info["dummy3"] = row.get("Dummy3".lower())
            emp_info["dummy4"] = row.get("Dummy4".lower())
            emp_info["dummy5"] = row.get("Dummy5".lower())
            emp_info["bhr_perno"] = row.get("BhrPerno".lower())
            emp_info["mat1_mngr"] = row.get("Mat1Mngr".lower())
            emp_info["mat2_mngr"] = row.get("Mat2Mngr".lower())
            emp_info["mat3_mngr"] = row.get("Mat3Mngr".lower())
            emp_info["mat4_mngr"] = row.get("Mat4Mngr".lower())
            emp_info["mat5_mngr"] = row.get("Mat5Mngr".lower())
            emp_info["head_hr_perno"] = row.get("HeadHrPerno".lower())
            emp_info["er_hr_perno"] = row.get("ErHrPerno".lower())
            emp_info["ou_short_txt"] = row.get("OuShortTxt".lower())
            emp_info["reg_cod"] = row.get("RegCod".lower())
            emp_info["reg_cod_txt"] = row.get("RegCodTxt".lower())
            emp_info["jobtxt_short"] = row.get("JobtxtShort".lower())
            emp_info["ou_level1"] = row.get("OuLevel1".lower())
            emp_info["ou_level1_short_txt"] = row.get("OuLevel1ShortTxt".lower())
            emp_info["ou_level1_long_txt"] = row.get("OuLevel1LongTxt".lower())
            emp_info["ou_level2"] = row.get("OuLevel2".lower())
            emp_info["ou_level2_short_txt"] = row.get("OuLevel2ShortTxt".lower())
            emp_info["ou_level2_long_txt"] = row.get("OuLevel2LongTxt".lower())
            emp_info["ou_level3"] = row.get("OuLevel3".lower())
            emp_info["ou_level3_short_txt"] = row.get("OuLevel3ShortTxt".lower())
            emp_info["ou_level3_long_txt"] = row.get("OuLevel3LongTxt".lower())
            emp_info["func_id01"] = row.get("FuncId01".lower())
            emp_info["function_text"] = row.get("FunctionText".lower())
            emp_info["subfunction1_id"] = row.get("Subfunction1Id".lower())
            emp_info["subfunction1_tex"] = row.get("Subfunction1Tex".lower())
            emp_info["subfunction2_id"] = row.get("Subfunction2Id".lower())
            emp_info["subfunction2_tex"] = row.get("Subfunction2Tex".lower())
            emp_info["loc_cod"] = row.get("LocCod".lower())
            emp_info["loc_desc"] = row.get("LocDesc".lower())
            emp_info["add1"] = row.get("Add1".lower())
            emp_info["add2"] = row.get("Add2".lower())
            emp_info["add3"] = row.get("Add3".lower())
            emp_info["add4"] = row.get("Add4".lower())
            emp_info["city_town"] = row.get("CityTown".lower())
            emp_info["state"] = row.get("State".lower())
            emp_info["pin_code"] = row.get("PinCode".lower())
            emp_info["country"] = row.get("Country".lower())
            emp_info["a962_perno"] = row.get("A962Perno".lower())
            emp_info["a962_cname"] = row.get("A962Cname".lower())
            object_list.append(TTLTempTable(**emp_info))
        oracle_session.add_all(object_list)
        oracle_session.commit() 
        return True, None 
    except Exception as error:
        print_error(f'load_data_to_temp_ttl() : {error}')
        return False, error

def validate_fields(session, row):
    date_failed_reason = None
    row.perno = cleaner_perno(row.perno)
    row.comp_code = cleaner(row.comp_code)
    row.ccodetxt = row.ccodetxt if row.comp_code in ['1200'] else cleaner(row.ccodetxt)
    row.pers_area = cleaner(row.pers_area)
    row.patxt = cleaner(row.patxt)
    row.psatxt = cleaner(row.psatxt)
    row.orgtx = cleaner(row.orgtx)
    row.p_subarea = cleaner(row.p_subarea)
    row.firstname = name_cleaner(row.firstname)
    row.last_name = name_cleaner(row.last_name)
    row.midnm = cleaner(row.midnm)
    row.reporting = cleaner(row.reporting)
    row.city_town = cleaner(row.city_town)
    row.dobsorp, date_failed_reason = format_date(row.dobsorp, date_failed_reason, "dobsorp") 
    row.doj, date_failed_reason = format_date(row.doj, date_failed_reason, "doj")
    row.dob, date_failed_reason = format_date(row.dob, date_failed_reason, "dob")
    row.dsvcvp, date_failed_reason = format_date(row.dsvcvp, date_failed_reason, "dsvcvp")
    row.dlprom, date_failed_reason = format_date(row.dlprom, date_failed_reason, "dlprom")
    row.dosep, date_failed_reason = format_date(row.dosep, date_failed_reason, "dosep")
    row.last_prom1, date_failed_reason = format_date(row.last_prom1, date_failed_reason, "last_prom1")
    row.last_prom2, date_failed_reason = format_date(row.last_prom2, date_failed_reason, "last_prom2")
    row.last_prom3, date_failed_reason = format_date(row.last_prom3, date_failed_reason, "last_prom3")
    row.imailid = row.imailid.lower() if row.imailid else None
    row.add1 = add_cleaner(row.add1)
    row.add2 = add_cleaner(row.add2)
    row.add3 = add_cleaner(row.add3)
    row.add4 = add_cleaner(row.add4)
    if not row.comp_code == 'TTL':
        if ((len(str(row.perno)) != 6) and (len(str(row.perno)) != 8)) or (not row.perno.isnumeric()):
            return False, f"invalid perno - {row.perno}"
    if not row.firstname:
        return False, f"firstname is empty"
    if not session.query(SapCompanyMaster).filter(SapCompanyMaster.comp_code==row.comp_code).first():
        return False, f"invalid company_code - {row.comp_code}"
    if row.dob == '0000-00-00' or row.dob == '00-00-0000':
        return False, DOB_IS_EMPTY
    if row.doj == '0000-00-00' or row.doj == '00-00-0000':
        return False, DOJ_IS_EMPTY
    if date_failed_reason != None:
        return False, date_failed_reason
    
    if row.comp_code in [TMML_CO_CODE,TMML_CO_CODE_0,TMML_CO_DEP_CODE,TMML_CO_DEP_CODE_0]:
        if row.comp_code in [TMML_CO_DEP_CODE,TMML_CO_DEP_CODE_0]:
            print("TMML Deployed Employees")
            return False, SKIP
        if row.comp_code in [TMML_CO_CODE,TMML_CO_CODE_0]:
            print("TMML Employees")
            if row.pers_area.lower().startswith('de'):
                print("TMML Deployed Employees with person area start with DE")
                return False, SKIP
            if (740000 <= int(row.perno) and int(row.perno) <= 769499 ) or (770000 <= int(row.perno) and int(row.perno) <= 774999) or row.perno in ["700024","700029","700037","700138","700339","700360","700374","700378","700381","700389","700392","700023"]:
                print("TMML Employees person number ranges between 740000 and 769499")
                return False, SKIP
            if row.pers_area == 'LKNW':
                row.patxt = 'TMML Lucknow Works'
                if row.p_subarea == 'LKFI':
                    row.psatxt = 'LKO Finance'
                if row.p_subarea == 'LKHR':
                    row.psatxt = 'LKO HR & Admin'
                if row.p_subarea == 'LKMF':
                    row.psatxt = 'LKO Mfg.'

    return True, row


def create_ad_request_body(data):
    ad_request = {}
    if all(key in data and data[key] for key in MANDATORY_AD_FIELDS):
        ad_request = {
            "samAccountName": data["samAccountName"],
            "userPrincipalName": data["userPrincipalName"]
        }
        # LastName
        if 'sn' in data and data['sn']:
            ad_request['sn'] = data['sn'].upper()
        # FirstName
        if 'givenName' in data and data['givenName']:
            ad_request['givenName'] = data['givenName'].upper()
        # MiddleName
        if 'initials' in data and data['initials']:
            ad_request['initials'] = data['initials'].upper()
        # Employee ID
        if 'EmployeeID' in data and data['EmployeeID']:
            ad_request['EmployeeID'] = data['EmployeeID']
        # manager perno
        if 'manager' in data and data['manager']:
            ad_request['manager'] = data['manager']
        # Department
        if 'department' in data and data['department']:
            ad_request['department'] = data['department']
        # physicalDeliveryOfficeName
        if 'physicalDeliveryOfficeName' in data and data['physicalDeliveryOfficeName']:
            ad_request['physicalDeliveryOfficeName'] = data['physicalDeliveryOfficeName']
        # facsimileTelephoneNumber
        if 'facsimileTelephoneNumber' in data and data['facsimileTelephoneNumber']:
            ad_request['facsimileTelephoneNumber'] = data['facsimileTelephoneNumber']
        # pager
        if 'pager' in data and data['pager']:
            ad_request['pager'] = data['pager']
        # otherTelePhone
        if 'otherTelePhone' in data and data['otherTelePhone']:
            ad_request['otherTelePhone'] = data['otherTelePhone']
        # msExchAssistantName
        if 'msExchAssistantName' in data and data['msExchAssistantName']:
            ad_request['msExchAssistantName'] = data['msExchAssistantName']
        # telePhoneAssistant
        if 'telePhoneAssistant' in data and data['telePhoneAssistant']:
            ad_request['telePhoneAssistant'] = data['telePhoneAssistant']
        # homePhone
        if 'homePhone' in data and data['homePhone']:
            ad_request['homePhone'] = data['homePhone']
        # Notes
        if 'info' in data and data['info']:
            ad_request['info'] = data['info']
        # Position
        if 'title' in data and data['title']:
            ad_request['title'] = data['title']
        # city
        if 'l' in data and data['l']:
            ad_request['l'] = data['l']
        # Company
        if 'company' in data and data['company']:
            ad_request['company'] = data['company']
        # displayName
        if 'displayName' in data and data['displayName']:
            ad_request['displayName'] = data['displayName']
        # division
        if 'division' in data and data['division']:
            ad_request['division'] = data['division']
            
        # ====================== Set Region details : Start ============================ #
        # Country
        if 'co' in data and data['co']:
            ad_request['co'] = data['co']
        # State
        if 'st' in data and data['st']:
            ad_request['st'] = data['st']
        # Zipcode
        if 'postalCode' in data and data['postalCode']:
            ad_request['postalCode'] = data['postalCode']
        # Address
        if 'streetAddress' in data and data['streetAddress']:
            ad_request['streetAddress'] = data['streetAddress']
        # ====================== Set Region details : End ============================ #

        # ====================== Phone Update : Start ================================= #
        # telephoneNumber
        if 'telephoneNumber' in data and data['telephoneNumber']:
            ad_request['telephoneNumber'] = data['telephoneNumber'].split(COMMA)[0]
            
        # mobile
        if 'mobile' in data and data['mobile']:
            ad_request['mobile'] = data['mobile'].split(COMMA)[0]
        # ====================== Phone Update : End ================================= #    
        
        # ====================== Extension Attribute : Start ================================= #    
        # Employee ID
        if 'extensionAttribute1' in data and data['extensionAttribute1']:
            ad_request['extensionAttribute1'] = data['extensionAttribute1']
        # Employee Subgeroup - Grade
        if 'extensionAttribute2' in data and data['extensionAttribute2']:
            ad_request['extensionAttribute2'] = data['extensionAttribute2']
        # BCNumber
        if 'extensionAttribute3' in data and data['extensionAttribute3']:
            ad_request['extensionAttribute3'] = data['extensionAttribute3']        
        # Employee ID
        if 'extensionAttribute5' in data and data['extensionAttribute5']:
            ad_request['extensionAttribute5'] = data['extensionAttribute5']
        # Gender
        if 'extensionAttribute9' in data and data['extensionAttribute9']:
            ad_request['extensionAttribute9'] = data['extensionAttribute9']
        # Salutation
        if 'extensionAttribute10' in data and data['extensionAttribute10']:
            ad_request['extensionAttribute10'] = data['extensionAttribute10']
        # personnelarea
        if 'extensionAttribute11' in data and data['extensionAttribute11']:
            ad_request['extensionAttribute11'] = data['extensionAttribute11']
        # PersonnelSubarea
        if 'extensionAttribute12' in data and data['extensionAttribute12']:
            ad_request['extensionAttribute12'] = data['extensionAttribute12']
        # Loacation description - Site
        if 'extensionAttribute13' in data and data['extensionAttribute13']:
            ad_request['extensionAttribute13'] = data['extensionAttribute13']

        # ====================== Extension Attribute : End ================================= #    
    else:
        # Missing fields...
        print("Mandatory Fields are missing...")
    return ad_request

def get_changed_attributes_for_update(new_data, old_data):

    for attribute in AD_ATTRIBUTES:
        if attribute not in new_data:
            new_data[attribute] = ""

    if (
        "EmployeeID" in new_data and "EmployeeID" in old_data and 
        new_data["EmployeeID"] == old_data["EmployeeID"]
    ):
        change_ad_attributes = {
            "samAccountName"     : old_data["samAccountName"],
            "userPrincipalName"  : old_data["userPrincipalName"],
            "userAccountControl" : 544
        }

        for attribute in new_data:
            changed = False
            if new_data[attribute] != "":
                if attribute not in old_data:
                    changed = True # insert
                    print(f'insert - {attribute} - {new_data[attribute]}')
                elif attribute in old_data and old_data[attribute] != new_data[attribute]:
                    changed = True # update
                    print(f'update - {attribute} - {old_data[attribute]} to {new_data[attribute]}')
            else:
                if attribute in old_data:
                    changed = True # delete
                    print(f'delete - {attribute} - {old_data[attribute]}')

            if changed:            
                change_ad_attributes[attribute] = new_data[attribute]
        
        change_ad_attributes = {k: v for k, v in change_ad_attributes.items() if v not in [None, ""]}
        return change_ad_attributes
    
    print("EmployeeID doesn't matched")
    return False

def comp_code(comp_code):
    if comp_code == '14':
        comp_code = comp_code.rjust(3,'0')
    elif comp_code != 'TTL':
        comp_code = comp_code.rjust(4,'0') if comp_code != '300' else '0100'
    return comp_code


def validate_user_esg(oracle_session, esubgroup, egroup):
    try:
        is_valid = (
            oracle_session.query(KeycloakESubGroup)
            .filter(
                KeycloakESubGroup.esubgroup_code == esubgroup,
                KeycloakESubGroup.esubgroup_type == False,
                KeycloakESubGroup.egroup_code == egroup
            )
            .first()
        )
        if is_valid is not None:
            return True
        return False
    except Exception as error:
        print_error("validate_user_esg", error)
def print_error(func_name, error=""):
    try:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb:
            file_name = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            error = f"Error - {error} \n Exception - {exc_type} \n File Name - {file_name} \n Function Name - {func_name} \n Line No - {exc_tb.tb_lineno}"
            print(error)
            return error
        else:
            error = f"{str(func_name)} : {str(error)}"
            print(error)
            return error
    except Exception as error:
        print(f"print_error(): {error}")


def get_total_count(table, session):
    try:
        count = session.query(table).count()
        return count
    except Exception as error:
        print_error("get_total_count", error)
