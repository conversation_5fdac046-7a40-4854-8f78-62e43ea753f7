# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from scripts.id_separation import update_employee_separation
from commons.configs import DAG16_ID_SEPARATION_AUTOSYNC_SCHEDULER, DAG16_ID_SEPARATION_SCHEDULE_JOB, ID_SEPARATION 


# DAG initialization...
with DAG(
    dag_id = DAG16_ID_SEPARATION_SCHEDULE_JOB,
    start_date = datetime(2024, 3, 1),
    schedule_interval = DAG16_ID_SEPARATION_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    id_separation_ = PythonOperator(
        task_id = ID_SEPARATION,
        python_callable = update_employee_separation
    )
    
    id_separation_