# In-built imports...
from requests import post
from json import loads, dumps
# Custom imports...
from commons.configs import REQUEST_BODY, SEND_DAILY_MANUAL_JOB_DETAILS, HEADER, PROD_EMAIL_TO 

def trigger_email():
    body = REQUEST_BODY
    body['to_email'] = PROD_EMAIL_TO
    response = post(url = SEND_DAILY_MANUAL_JOB_DETAILS, headers = HEADER, data = dumps(body))
    response = loads(response.content)
    print("trigger_email(): ", response)
