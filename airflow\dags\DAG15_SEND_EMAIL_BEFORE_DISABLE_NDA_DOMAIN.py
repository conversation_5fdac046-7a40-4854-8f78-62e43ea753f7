# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from scripts.nda_delete_disable_job import nda_send_email_before_disable_domain_id_to_employee, nda_send_email_before_disable_domain_id_v2, sync_before_send_email
from commons.configs import DAG15_SEND_EMAIL_BEFORE_DISABLE_NDA_AUTOSYNC_SCHEDULER, DAG15_SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN_ID_JOBS, SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN, NDA_SYNC_JOB, SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN_TO_EMPLOYEE


# DAG initialization...
with DAG(
    dag_id = DAG15_SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN_ID_JOBS,
    start_date = datetime(2024, 3, 1),
    schedule_interval = DAG15_SEND_EMAIL_BEFORE_DISABLE_NDA_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    sync_before_disable_nda_domain = PythonOperator(
        task_id = NDA_SYNC_JOB,
        python_callable = sync_before_send_email
    )
    
    # Task 1 Initialization...
    send_email_before_disable_nda_domain = PythonOperator(
        task_id = SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN,
        python_callable = nda_send_email_before_disable_domain_id_v2,
        do_xcom_push = True    
    )
    
    # Task 2 
    send_email_before_disable_domain_id_to_employee = PythonOperator(
        task_id = SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN_TO_EMPLOYEE,
        python_callable = nda_send_email_before_disable_domain_id_to_employee,
        do_xcom_push = True    
    )

    # Task sequencing & call...
    sync_before_disable_nda_domain >> send_email_before_disable_nda_domain >> send_email_before_disable_domain_id_to_employee