import requests, json
from sqlalchemy.sql import func
from scripts.scheduled_job import sync_data_from_temp_table
from commons.db_connections import create_new_oracle_session, create_oracle_session
# from commons.models import ManualTempTable, SyncExclusionLogs, ADMetaData
from commons.models_v2 import ManualTempTable, SyncExclusionLog, ADMetaData
from commons.messages import SYNC_EXCLUSION, SUC<PERSON>SS, FAILED, RUNNING
from commons.common_functions import create_file_log, update_file_log, print_error
from commons.mains import perform_operations
from commons.notifications import send_failed_notification
from commons.configs import AD, AZUR<PERSON>, BLUECOLLAR, EDP, NEXTGEN, SAP, SAP_USER_DETAILS_BY_PERSON_NO
from commons.configs import REQUEST_BODY, HEADER
from sqlalchemy.orm import joinedload


def emp_mapping_function(employee_details):
    try:
        temp_table = ManualTempTable()
        if 'comp_code' in employee_details:
            temp_table.perno = employee_details.get('perno', None)
            temp_table.comp_code = employee_details.get('comp_code', None)
            temp_table.pers_area = employee_details.get('pers_area', None)
            temp_table.patxt = employee_details.get('patxt', None)
            temp_table.egroup = employee_details.get('egroup', None)
            temp_table.ptext = employee_details.get('ptext', None)
            temp_table.esubgroup = employee_details.get('esubgroup', None)
            temp_table.esgtxt = employee_details.get('esgtxt', None)
            temp_table.ccodetxt = employee_details.get('ccodetxt', None)
            temp_table.bus_area = employee_details.get('bus_area', None)
            temp_table.bustxt = employee_details.get('bustxt', None)
            temp_table.p_subarea = employee_details.get('p_subarea', None)
            temp_table.psatxt = employee_details.get('psatxt', None)
            temp_table.payarea = employee_details.get('payarea', None)
            temp_table.paytxt = employee_details.get('paytxt', None)
            temp_table.costcenter = employee_details.get('costcenter', None)
            temp_table.cosctrtxt = employee_details.get('cosctrtxt', None)
            temp_table.position1 = employee_details.get('position1', None)
            temp_table.postxt = employee_details.get('postxt', None)
            temp_table.job = employee_details.get('job', None)
            temp_table.jobtxt = employee_details.get('jobtxt', None)
            temp_table.fkbtx = employee_details.get('fkbtx', None)
            temp_table.initials = employee_details.get('initials', None)
            temp_table.comp_name = employee_details.get('comp_name', None)
            temp_table.last_name = employee_details.get('last_name', None)
            temp_table.firstname = employee_details.get('firstname', None)
            temp_table.midnm = employee_details.get('midnm', None)
            temp_table.title = employee_details.get('title', None)
            temp_table.off_num = employee_details.get('off_num', None)
            temp_table.gender = employee_details.get('gender', None)
            temp_table.konfe = employee_details.get('konfe', None)
            temp_table.religion = employee_details.get('religion', None)
            temp_table.famst = employee_details.get('famst', None)
            temp_table.mar_status = employee_details.get('mar_status', None)
            temp_table.bloodgroup = employee_details.get('bloodgroup', None)
            temp_table.imailid = employee_details.get('imailid', None)
            temp_table.exmailid = employee_details.get('exmailid', None)
            temp_table.dobsorp = employee_details.get('dobsorp', None)
            temp_table.doj = employee_details.get('doj', None)
            temp_table.dob = employee_details.get('dob', None)
            temp_table.dsvcvp = employee_details.get('dsvcvp', None)
            temp_table.dlprom = employee_details.get('dlprom', None)
            temp_table.dosep = employee_details.get('dosep', None)
            temp_table.seprsn = employee_details.get('seprsn', None)
            temp_table.challenged = employee_details.get('challenged', None)
            temp_table.reporting = str(int(employee_details.get('reporting', None)))
            temp_table.nameofreporting = employee_details.get('nameofreporting', None)
            temp_table.empstatus = employee_details.get('empstatus', None)
            temp_table.empstattxt = employee_details.get('empstattxt', None)
            temp_table.orgeh = employee_details.get('orgeh', None)
            temp_table.orgtx = employee_details.get('orgtx', None)
            temp_table.office_mobile = employee_details.get('office_mobile', None)
            temp_table.pers_mobile = employee_details.get('pers_mobile', None)
            temp_table.emrg_mobile = employee_details.get('emrg_mobile', None)
            temp_table.transport_code = employee_details.get('transport_code', None)
            temp_table.off_addr = employee_details.get('off_addr', None)
            temp_table.sepcode = employee_details.get('sepcode', None)
            temp_table.fun = employee_details.get('fun', None)
            temp_table.funt = employee_details.get('funt', None)
            temp_table.mgtxt = employee_details.get('mgtxt', None)
            temp_table.flag1 = employee_details.get('flag1', None)
            temp_table.last_prom1 = employee_details.get('last_prom1', None)
            temp_table.last_prom2 = employee_details.get('last_prom2', None)
            temp_table.last_prom3 = employee_details.get('last_prom3', None)
            temp_table.dummy1 = employee_details.get('dummy1', None)
            temp_table.dummy2 = employee_details.get('dummy2', None)
            temp_table.dummy3 = employee_details.get('dummy3', None)
            temp_table.dummy4 = employee_details.get('dummy4', None)
            temp_table.dummy5 = employee_details.get('dummy5', None)
            temp_table.bhr_perno = employee_details.get('bhr_perno', None)
            temp_table.mat1_mngr = employee_details.get('mat1_mngr', None)
            temp_table.mat2_mngr = employee_details.get('mat2_mngr', None)
            temp_table.mat3_mngr = employee_details.get('mat3_mngr', None)
            temp_table.mat4_mngr = employee_details.get('mat4_mngr', None)
            temp_table.mat5_mngr = employee_details.get('mat5_mngr', None)
            temp_table.head_hr_perno = employee_details.get('head_hr_perno', None)
            temp_table.er_hr_perno = employee_details.get('er_hr_perno', None)
            temp_table.ou_short_txt = employee_details.get('ou_short_txt', None)
            temp_table.reg_cod = employee_details.get('reg_cod', None)
            temp_table.reg_cod_txt = employee_details.get('reg_cod_txt', None)
            temp_table.jobtxt_short = employee_details.get('jobtxt_short', None)
            temp_table.ou_level1 = employee_details.get('ou_level1', None)
            temp_table.ou_level1_short_txt = employee_details.get('ou_level1_short_txt', None)
            temp_table.ou_level1_long_txt = employee_details.get('ou_level1_long_txt', None)
            temp_table.ou_level2 = employee_details.get('ou_level2', None)
            temp_table.ou_level2_short_txt = employee_details.get('ou_level2_short_txt', None)
            temp_table.ou_level2_long_txt = employee_details.get('ou_level2_long_txt', None)
            temp_table.ou_level3 = employee_details.get('ou_level3', None)
            temp_table.ou_level3_short_txt = employee_details.get('ou_level3_short_txt', None)
            temp_table.ou_level3_long_txt = employee_details.get('ou_level3_long_txt', None)
            temp_table.func_id01 = employee_details.get('func_id01', None)
            temp_table.function_text = employee_details.get('function_text', None)
            temp_table.subfunction1_id = employee_details.get('subfunction1_id', None)
            temp_table.subfunction1_tex = employee_details.get('subfunction1_tex', None)
            temp_table.subfunction2_id = employee_details.get('subfunction2_id', None)
            temp_table.subfunction2_tex = employee_details.get('subfunction2_tex', None)
            temp_table.loc_cod = employee_details.get('loc_cod', None)
            temp_table.loc_desc = employee_details.get('loc_desc', None)
            temp_table.add1 = employee_details.get('add1', None)
            temp_table.add2 = employee_details.get('add2', None)
            temp_table.add3 = employee_details.get('add3', None)
            temp_table.add4 = employee_details.get('add4', None)
            temp_table.city_town = employee_details.get('city_town', None)
            temp_table.state = employee_details.get('state', None)
            temp_table.pin_code = employee_details.get('pin_code', None)
            temp_table.country = employee_details.get('country', None)
            temp_table.a962_perno = employee_details.get('a962_perno', None)
            temp_table.a962_cname = employee_details.get('a962_cname', None)
        else:
            temp_table.perno = employee_details.get('Perno'.lower(), None)
            temp_table.comp_code = employee_details.get('CompCode'.lower(), None)
            temp_table.pers_area = employee_details.get('PersArea'.lower(), None)
            temp_table.patxt = employee_details.get('Patxt'.lower(), None)
            temp_table.egroup = employee_details.get('Egroup'.lower(), None)
            temp_table.ptext = employee_details.get('Ptext'.lower(), None)
            temp_table.esubgroup = employee_details.get('Esubgroup'.lower(), None)
            temp_table.esgtxt = employee_details.get('Esgtxt'.lower(), None)
            temp_table.ccodetxt = employee_details.get('Ccodetxt'.lower(), None)
            temp_table.bus_area = employee_details.get('BusArea'.lower(), None)
            temp_table.bustxt = employee_details.get('Bustxt'.lower(), None)
            temp_table.p_subarea = employee_details.get('PSubarea'.lower(), None)
            temp_table.psatxt = employee_details.get('Psatxt'.lower(), None)
            temp_table.payarea = employee_details.get('Payarea'.lower(), None)
            temp_table.paytxt = employee_details.get('Paytxt'.lower(), None)
            temp_table.costcenter = employee_details.get('Costcenter'.lower(), None)
            temp_table.cosctrtxt = employee_details.get('Cosctrtxt'.lower(), None)
            temp_table.position1 = employee_details.get('Position1'.lower(), None)
            temp_table.postxt = employee_details.get('Postxt'.lower(), None)
            temp_table.job = employee_details.get('Job'.lower(), None)
            temp_table.jobtxt = employee_details.get('Jobtxt'.lower(), None)
            temp_table.fkbtx = employee_details.get('Fkbtx'.lower(), None)
            temp_table.initials = employee_details.get('Initials'.lower(), None)
            temp_table.comp_name = employee_details.get('CompName'.lower(), None)
            temp_table.last_name = employee_details.get('LastName'.lower(), None)
            temp_table.firstname = employee_details.get('Firstname'.lower(), None)
            temp_table.midnm = employee_details.get('Midnm'.lower(), None)
            temp_table.title = employee_details.get('Title'.lower(), None)
            temp_table.off_num = employee_details.get('OffNum'.lower(), None)
            temp_table.gender = employee_details.get('Gender'.lower(), None)
            temp_table.konfe = employee_details.get('Konfe'.lower(), None)
            temp_table.religion = employee_details.get('Religion'.lower(), None)
            temp_table.famst = employee_details.get('Famst'.lower(), None)
            temp_table.mar_status = employee_details.get('MarStatus'.lower(), None)
            temp_table.bloodgroup = employee_details.get('Bloodgroup'.lower(), None)
            temp_table.imailid = employee_details.get('Imailid'.lower(), None)
            temp_table.exmailid = employee_details.get('Exmailid'.lower(), None)
            temp_table.dobsorp = employee_details.get('Dobsorp'.lower(), None)
            temp_table.doj = employee_details.get('Doj'.lower(), None)
            temp_table.dob = employee_details.get('Dob'.lower(), None)
            temp_table.dsvcvp = employee_details.get('Dsvcvp'.lower(), None)
            temp_table.dlprom = employee_details.get('Dlprom'.lower(), None)
            temp_table.dosep = employee_details.get('Dosep'.lower(), None)
            temp_table.seprsn = employee_details.get('Seprsn'.lower(), None)
            temp_table.challenged = employee_details.get('Challenged'.lower(), None)
            temp_table.reporting = str(int(employee_details.get('Reporting'.lower(), None)))
            temp_table.nameofreporting = employee_details.get('Nameofreporting'.lower(), None)
            temp_table.empstatus = employee_details.get('Empstatus'.lower(), None)
            temp_table.empstattxt = employee_details.get('Empstattxt'.lower(), None)
            temp_table.orgeh = employee_details.get('Orgeh'.lower(), None)
            temp_table.orgtx = employee_details.get('Orgtx'.lower(), None)
            temp_table.office_mobile = employee_details.get('OfficeMobile'.lower(), None)
            temp_table.pers_mobile = employee_details.get('PersMobile'.lower(), None)
            temp_table.emrg_mobile = employee_details.get('EmrgMobile'.lower(), None)
            temp_table.transport_code = employee_details.get('TransportCode'.lower(), None)
            temp_table.off_addr = employee_details.get('OffAddr'.lower(), None)
            temp_table.sepcode = employee_details.get('Sepcode'.lower(), None)
            temp_table.fun = employee_details.get('Fun'.lower(), None)
            temp_table.funt = employee_details.get('Funt'.lower(), None)
            temp_table.mgtxt = employee_details.get('Mgtxt'.lower(), None)
            temp_table.flag1 = employee_details.get('Flag1'.lower(), None)
            temp_table.last_prom1 = employee_details.get('LastProm1'.lower(), None)
            temp_table.last_prom2 = employee_details.get('LastProm2'.lower(), None)
            temp_table.last_prom3 = employee_details.get('LastProm3'.lower(), None)
            temp_table.dummy1 = employee_details.get('Dummy1'.lower(), None)
            temp_table.dummy2 = employee_details.get('Dummy2'.lower(), None)
            temp_table.dummy3 = employee_details.get('Dummy3'.lower(), None)
            temp_table.dummy4 = employee_details.get('Dummy4'.lower(), None)
            temp_table.dummy5 = employee_details.get('Dummy5'.lower(), None)
            temp_table.bhr_perno = employee_details.get('BhrPerno'.lower(), None)
            temp_table.mat1_mngr = employee_details.get('Mat1Mngr'.lower(), None)
            temp_table.mat2_mngr = employee_details.get('Mat2Mngr'.lower(), None)
            temp_table.mat3_mngr = employee_details.get('Mat3Mngr'.lower(), None)
            temp_table.mat4_mngr = employee_details.get('Mat4Mngr'.lower(), None)
            temp_table.mat5_mngr = employee_details.get('Mat5Mngr'.lower(), None)
            temp_table.head_hr_perno = employee_details.get('HeadHrPerno'.lower(), None)
            temp_table.er_hr_perno = employee_details.get('ErHrPerno'.lower(), None)
            temp_table.ou_short_txt = employee_details.get('OuShortTxt'.lower(), None)
            temp_table.reg_cod = employee_details.get('RegCod'.lower(), None)
            temp_table.reg_cod_txt = employee_details.get('RegCodTxt'.lower(), None)
            temp_table.jobtxt_short = employee_details.get('JobtxtShort'.lower(), None)
            temp_table.ou_level1 = employee_details.get('OuLevel1'.lower(), None)
            temp_table.ou_level1_short_txt = employee_details.get('OuLevel1ShortTxt'.lower(), None)
            temp_table.ou_level1_long_txt = employee_details.get('OuLevel1LongTxt'.lower(), None)
            temp_table.ou_level2 = employee_details.get('OuLevel2'.lower(), None)
            temp_table.ou_level2_short_txt = employee_details.get('OuLevel2ShortTxt'.lower(), None)
            temp_table.ou_level2_long_txt = employee_details.get('OuLevel2LongTxt'.lower(), None)
            temp_table.ou_level3 = employee_details.get('OuLevel3'.lower(), None)
            temp_table.ou_level3_short_txt = employee_details.get('OuLevel3ShortTxt'.lower(), None)
            temp_table.ou_level3_long_txt = employee_details.get('OuLevel3LongTxt'.lower(), None)
            temp_table.func_id01 = employee_details.get('FuncId01'.lower(), None)
            temp_table.function_text = employee_details.get('FunctionText'.lower(), None)
            temp_table.subfunction1_id = employee_details.get('Subfunction1Id'.lower(), None)
            temp_table.subfunction1_tex = employee_details.get('Subfunction1Tex'.lower(), None)
            temp_table.subfunction2_id = employee_details.get('Subfunction2Id'.lower(), None)
            temp_table.subfunction2_tex = employee_details.get('Subfunction2Tex'.lower(), None)
            temp_table.loc_cod = employee_details.get('LocCod'.lower(), None)
            temp_table.loc_desc = employee_details.get('LocDesc'.lower(), None)
            temp_table.add1 = employee_details.get('Add1'.lower(), None)
            temp_table.add2 = employee_details.get('Add2'.lower(), None)
            temp_table.add3 = employee_details.get('Add3'.lower(), None)
            temp_table.add4 = employee_details.get('Add4'.lower(), None)
            temp_table.city_town = employee_details.get('CityTown'.lower(), None)
            temp_table.state = employee_details.get('State'.lower(), None)
            temp_table.pin_code = employee_details.get('PinCode'.lower(), None)
            temp_table.country = employee_details.get('Country'.lower(), None)
            temp_table.a962_perno = employee_details.get('A962Perno'.lower(), None)
            temp_table.a962_cname = employee_details.get('A962Cname'.lower(), None)
        return temp_table
    except (Exception) as error:
        print_error("emp_mapping_function() Error: ",error)
        return False

def get_employee_details(perno,comp_code):
    try:
        requests_body = {**REQUEST_BODY}
        requests_body['employee_id'] = perno
        requests_body['company_code']= comp_code
        # if str(comp_code) in ["100","0100"]:
        #     requests_body['company_code']=100
        #     API_URL = SAP_CV_USER_DETAILS_BY_PERSON_NO
        # elif str(comp_code) in ["550","0550"]:
        #     requests_body['company_code']=550
            # API_URL = SAP_PV_USER_DETAILS_BY_PERSON_NO
        # elif str(comp_code) in ["650","0650"]:
        #     requests_body['company_code']=650
            # API_URL = SAP_EV_USER_DETAILS_BY_PERSON_NO
        # elif str(comp_code) in ["300","0300"]:
        #     requests_body['company_code']=300
            # API_URL = SAP_TMBSL_USER_DETAILS_BY_PERSON_NO
        if str(comp_code) in ["100","0100","550","0550","650","0650","300","0300"]:
            API_URL = SAP_USER_DETAILS_BY_PERSON_NO
        else:
            print(f"{str(comp_code)} Company code is invalid")
            return False
        response = requests.get(API_URL,headers=HEADER,params=requests_body)
        if response.status_code == 200:
            response = json.loads(response.content)
            if "result" in response and response['result']:
                data = response['result']
                if comp_code in ['300','0300']:
                    if data['perno'].isdigit():
                        data['perno'] = str(int(data['perno']))
                    if data['compcode'].isdigit():
                        data['compcode'] = str(int(data['compcode']))
                else:
                    if data['perno'].isdigit():
                        data['perno'] = str(int(data['perno']))
                    if data['compcode'].isdigit():
                        data['compcode'] = str(int(data['compcode']))
                for field in data:
                    if data[field] == None:
                        data[field] = ""
                return data
            else:
                print("get_employee_details sap response_object",response)
                return False
        else:
            print('get_employee_details sap response_code',response.status_code)
            return False
    except Exception as error:
        print_error('get_employee_details()', error)
        return False

def sync_id_after_exclusion_ends(temp_table_object_list):
    try:
        # Connect to local DB...
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        file_log_object = {
            "file_name": SYNC_EXCLUSION,
            "trigger_type": None,
            "file_format":None,
            "status":RUNNING,
            "row_count": len(temp_table_object_list),
            "column_count": "108",
            "job_name":"Sync Exclusion"
        }
        
        file_log = create_file_log(new_oracle_session, file_log_object)
        
        # users = new_oracle_session.query(ADMetaData).options(joinedload(ADMetaData.sync_exclusion_logs)).filter(
        #     ADMetaData.person_no.in_([temp.perno for temp in temp_table_object_list])
        # ).all()

        # # Create a mapping from person_no to user
        # user_map = {user.person_no: user for user in users}

        for temp_table_object in temp_table_object_list:
            sync_data_from_temp_table(new_oracle_session, temp_table_object, file_log.file_id, SAP)
            sync_data_from_temp_table(new_oracle_session, temp_table_object, file_log.file_id, AD)
            sync_data_from_temp_table(new_oracle_session, temp_table_object, file_log.file_id, AZURE)
            sync_data_from_temp_table(new_oracle_session, temp_table_object, file_log.file_id, EDP)
            sync_data_from_temp_table(new_oracle_session, temp_table_object, file_log.file_id, NEXTGEN)
            sync_data_from_temp_table(new_oracle_session, temp_table_object, file_log.file_id, BLUECOLLAR)
            # user = user_map.get(temp_table_object.perno)
            
            # if user:
            #     # Update SyncExclusionLog using the relationship
            #     for log in user.sync_exclusion_logs:
            #         if not log.is_deleted:
            #             log.is_deleted = True
            #             log.is_sync_enable = True
                
            #     new_oracle_session.commit()
            #     print(f'{user.user_id} is synced successfully')
            # else:
            #     print(f'{temp_table_object.perno} not found in Metadata')
            user_id = new_oracle_session.query(ADMetaData.user_id).filter(ADMetaData.person_no == temp_table_object.perno).first()
            if user_id:
                new_oracle_session.query(SyncExclusionLog).filter(
                    func.upper(SyncExclusionLog.user_id) == func.upper(user_id[0]),  # Case insensitive comparison
                    SyncExclusionLog.is_deleted == False
                ).update(
                    {
                        SyncExclusionLog.is_deleted: True,
                        SyncExclusionLog.is_sync_enable: True
                    },
                    synchronize_session=False  # Skip session synchronization
                )
                new_oracle_session.commit()
                print(f'{user_id[0]} is synced successfully')
            else:
                print(f'{temp_table_object.perno} not found in Metdata')
        
        update_file_log(new_oracle_session, file_log=file_log, status=SUCCESS, reason=None, commit=True)
    
    except Exception as error:
        print_error('sync_id_after_exclusion_ends()',error)
        update_file_log(new_oracle_session,file_log = file_log, status = FAILED, reason = str(error)[:50], commit = True)
        send_failed_notification(file_log)
        raise Exception(error)