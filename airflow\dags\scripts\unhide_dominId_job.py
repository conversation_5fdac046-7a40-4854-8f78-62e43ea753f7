
from datetime import datetime, timedelta
import json, requests
from sqlalchemy import and_, case, func
from sqlalchemy.orm import aliased

from commons.messages import NO_DATA_FOUND, RECORD_UPDATED_SUCCESSFULLY
from commons.configs import HEADER, REQUEST_BODY, AD_LDAP_USER_URL
from commons.db_connections import create_new_oracle_session, create_oracle_session
# from commons.models import ADMetaData, 
from commons.models_v2 import ADMetaData, SapEmployeeMaster
from commons.common_functions import print_error


def unhide_domainId_from_address_list():
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        date_1_days_after = datetime.now() + timedelta(days=1)
        date_1_days_after = date_1_days_after.date().strftime('%Y-%m-%d')
        sap_emp = aliased(SapEmployeeMaster)
        ad_metadata = aliased(ADMetaData)
        doj_as_date = case(
            [
                (
                    func.regexp_like(sap_emp.doj, r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'),
                    func.to_date(sap_emp.doj, 'YYYY-MM-DD HH24:MI:SS')
                ),
                (
                    func.regexp_like(sap_emp.doj, r'^\d{2}-\d{2}-\d{4}$'),
                    func.to_date(sap_emp.doj, 'DD-MM-YYYY')
                )
            ],
            else_=None
        )
        valid_doj_format = func.regexp_like(SapEmployeeMaster.doj, r'^\d{2}-\d{2}-\d{4}$') | func.regexp_like(SapEmployeeMaster.doj, r'^\d{4}-\d{2}-\d{2}')
        sap_emp = list(set((new_oracle_session.query(sap_emp.person_no).join(ad_metadata, sap_emp.person_no == ad_metadata.person_no).filter( and_(ad_metadata.is_hidden == True, valid_doj_format, doj_as_date.isnot(None), doj_as_date <= func.to_date(date_1_days_after,'YYYY-MM-DD')))).all()))
        for sap_data in sap_emp:
            metadata = new_oracle_session.query(ADMetaData).filter(ADMetaData.person_no==sap_data.person_no).first()
            request_body = {**REQUEST_BODY}
            request_body['username'] = metadata.user_id
            request_body['attributes'] = {
                "msExchHideFromAddressLists" : False
            }
            request = requests.put(AD_LDAP_USER_URL,headers=HEADER,data=json.dumps(request_body))
            if request.status_code == 200:
                if json.loads(request.content)['message'] == RECORD_UPDATED_SUCCESSFULLY:
                    metadata.is_hidden = False
                    new_oracle_session.add(metadata)
                    new_oracle_session.commit()
                else:
                    print("unhide_domainId_from_address_list response_object",json.loads(request.content))
            else:
                print('unhide_domainId_from_address_list response_code',request.status_code)
        if len(sap_emp)==0:
            print('unhide_domainId_from_address_list',NO_DATA_FOUND)
        return True
    except Exception as error:
        print_error('unhide_domainId_from_address_list',error)