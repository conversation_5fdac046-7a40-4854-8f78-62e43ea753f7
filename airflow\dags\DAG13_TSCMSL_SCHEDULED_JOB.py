# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# from airflow.providers.sftp.sensors.sftp import SFTPSensor
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
# Custom imports...
from commons.configs import  DAG13_TSCMSL_SCHEDULED_JOBS, DAG13_TSCMSL_SAP_AUTOSYNC_SCHEDULER, DAG7_CV_SCHEDULED_JOBS, LOAD_TO_TEMP, LOAD_TO_AD_CET, TRIGGER_ID
from scripts.scheduled_job import extract_and_load_TSCMSL, schedule_load_to_cet_tscml


# DAG initialization...
with DAG(
    dag_id = DAG13_TSCMSL_SCHEDULED_JOBS,
    start_date = datetime(2024, 3, 1),
    schedule_interval = DAG13_TSCMSL_SAP_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    load_to_temp_ = PythonOperator(
        task_id = LOAD_TO_TEMP,
        python_callable = extract_and_load_TSCMSL,
        do_xcom_push = True    
    )
    
    # Task 2 Initialization...
    load_to_cet_ = PythonOperator(
        task_id = LOAD_TO_AD_CET,
        python_callable = schedule_load_to_cet_tscml,
        do_xcom_push = True
    )
    
    # # Task 3 Initialization...
    # trigger_cv_job = TriggerDagRunOperator(
    #     task_id = TRIGGER_ID,
    #     trigger_dag_id = DAG7_CV_SCHEDULED_JOBS, 
    #     dag = dag,
    # )

    # Task sequencing & call...
    load_to_temp_>>load_to_cet_