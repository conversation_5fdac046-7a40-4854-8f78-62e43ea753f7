# In-built imports...
from datetime import datetime, timedelta
from telnetlib import Telnet
from requests import post
from json import loads, dumps
# Custom imports...
from commons.models_v2 import ADMetaData
from commons.configs import MAILBOX_PORT, MAILBOX_SERVER_DOMAIN, AUTOFIX_MAILBOX_FOR_CREATED_USERS, HEADER, REQUEST_BODY


def check_telnet_connection():
    try:
        tn = Telnet(MAILBOX_SERVER_DOMAIN, MAILBOX_PORT, timeout = 10)
        tn.close()
        return True
    except Exception as error:
        print("check_telnet_connection(): ", error)
        raise Exception(error)
    

# DAG 6 : Trigger Autofixer Mailbox
def trigger_autofixer_mailbox_API():
    failed_mailbox_user_object = ADMetaData.objects.filter(
        is_deleted=False, 
        is_manual_entry=True, 
        mailbox_status=False, 
        created_at__lt=(datetime.now() - timedelta(minutes=5))
    )
    
    failed_mailbox_user_list = list(failed_mailbox_user_object.values())
    for iter in failed_mailbox_user_list:
        print("iter: ", iter)
        
        comp_code = iter['person_no__company_code']
        if not comp_code:
            print(f'Skipped autofixer {iter["user_id"]}')
            continue
        body = {**REQUEST_BODY}
        body["user_id"] = iter["user_id"]
        body['skip_timer'] = True
        body['comp_code'] = comp_code
        response = post(url = AUTOFIX_MAILBOX_FOR_CREATED_USERS, headers = HEADER, data = dumps(body))
        print("response 1: ",response)
        if response.status_code == 200:
            response = loads(response.content)
            print("response 2: ",response)

