import time
from commons import db_queries


# Delete user Operations...
def get_disable_employee(curr):
    query = db_queries.GET_EXPIRY_EMP_DATA
    curr.execute(query)
    columns = curr.description
    data = [{columns[index][0].lower():column for index, column in enumerate(value)} for value in curr.fetchall()]
    return data

def insert_to_delete_logs(log,curr):
    query = db_queries.INSERT_TO_DELETE_LOGS
    curr.execute(query, [log['ad_delete_log_id'], log['row_count'], log['status'], log['reason']])

def insert_to_delete_row_logs(log,curr):
    row_id = "ROW"+str(int(time.time_ns() * 10))
    query = db_queries.INSERT_TO_DELETE_ROW_LOGS
    curr.execute(query, [row_id, log['user_id'], log['status'], log['reason'], log['ad_delete_log_id']])
    
def move_to_AD_change_log(user_id, delete_log_id, curr):
    query = db_queries.INSERT_TO_AD_CHANGE_LOG
    curr.execute(query, [delete_log_id,user_id])
    delete_from_ad_metadata(user_id, curr)
    
def delete_from_ad_metadata(user_id, curr):
    query = db_queries.DELETE_FROM_AD_METADATA
    curr.execute(query, [user_id])