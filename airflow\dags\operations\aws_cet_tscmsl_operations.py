# In-built imports...
from datetime import datetime
from operations.active_directory_tscmsl_operations import get_ad_data_by_perno_tscmsl
from operations.active_directory_operations import get_ad_data_by_perno
# Custom imports...
from commons import db_connections, common_functions
from commons import messages
# from commons.models import AWSEmployeeData, AwsCetMetaData, AwsCetChangeLogHistory
from commons.models_v2 import AWSEmployeeData, AwsCetMetaData, AwsCetChangeLogHistory
mssql_session = db_connections.create_AWS_mssql_session()
def date_cleaner_tscmsl(objdatetime):
    if objdatetime is not None:
        if type(objdatetime) == datetime:
            return str(objdatetime)[:10]
        elif objdatetime in ['0000-00-00','00-00-0000']:
            return str(objdatetime)[:10]
        else:
            return str(objdatetime)[:10]
    else:
        return None
    
def slice_data (data, l):
    if type(data) == int:
        data = str(data)
    return data[0:l] if data != None and len(data)>l else data

def map_row_to_result_data(result,row):
    result.pers_no = int(row.perno) 
    result.co_code = row.comp_code.rjust(4,'0')
    result.pers_area = slice_data(row.pers_area,4)
    result.empl_subgroup = slice_data(row.esubgroup,2)
    result.pers_subarea = slice_data(row.p_subarea,4)
    result.cost_center = slice_data(row.costcenter,20)
    result.position_code = slice_data(row.position1,40)
    result.position_text = slice_data(row.postxt,100)
    result.job_code = slice_data(row.job,40)
    result.job_text = slice_data(row.jobtxt,40)
    result.ps_group = slice_data(row.bus_area,8)
    result.payroll_area = slice_data(row.payarea,2)
    result.last_name = slice_data(row.last_name,40)
    result.first_name = slice_data(row.firstname,40)
    result.complete_name = slice_data(row.comp_name,60)
    result.middle_name = slice_data(row.midnm,40)
    result.form_addr_key = slice_data(row.title,6)
    result.employment_status_code = slice_data(row.empstatus,1)
    result.email_id = slice_data(row.imailid,70)
    result.code_sex = slice_data(row.gender,1)
    result.code_blood_grp = slice_data(row.bloodgroup,3)
    result.code_marital_sta = slice_data(row.famst,1)
    result.code_relgn = slice_data(row.konfe,3)
    result.date_absorp = date_cleaner_tscmsl(row.dobsorp)
    result.date_birth = date_cleaner_tscmsl(row.dob)
    result.date_joining = date_cleaner_tscmsl(row.doj)
    result.date_last_svpc = date_cleaner_tscmsl(row.dsvcvp)
    result.date_last_prom = date_cleaner_tscmsl(row.dlprom)
    result.date_separation = date_cleaner_tscmsl(row.dosep)
    result.code_del_reason = slice_data(row.sepcode,4)
    result.code_status = slice_data(row.egroup,1)
    result.report_to = slice_data(row.reporting,6)
    result.code_relgn_text = row.religion[:20] if row.religion is not None else None
    result.code_marital_sta_text =slice_data(row.mar_status,6)
    result.code_status_text = slice_data(row.ptext,20)
    result.empl_subgroup_text = slice_data(row.esgtxt,50)
    result.function_grp = slice_data(row.fun,4)
    result.hiring_reason = slice_data(row.mgtxt,100)
    result.region = slice_data(row.reg_cod,120)
    result.location = slice_data(row.loc_desc,50)
    result.off_num = slice_data(row.off_num,50)
    result.ext_email = slice_data(row.exmailid,150)
    result.org_unit = slice_data(row.orgeh,10)
    result.pers_mobile = slice_data(row.pers_mobile,50)
    result.emrg_mobile = slice_data(row.emrg_mobile,50)
    result.transport_code = slice_data(row.transport_code,30)
    result.off_addr = slice_data(row.off_addr,200)
    result.last_prom1 = date_cleaner_tscmsl(row.last_prom1)
    result.last_prom2 = date_cleaner_tscmsl(row.last_prom2)
    result.last_prom3 = date_cleaner_tscmsl(row.last_prom3)
    result.dummy1 = slice_data(row.dummy1,50)
    result.dummy2 = slice_data(row.dummy2,50)
    result.dummy3 = slice_data(row.dummy3,50)
    result.dummy4 = slice_data(row.dummy4,50)
    result.dummy5 = slice_data(row.dummy5,50)
    result.office_mobile = slice_data(row.office_mobile,50)
    result.location_code = slice_data(row.loc_cod,20)
    result.pa_code = slice_data(row.pers_area,20)
    result.psa_code = slice_data(row.p_subarea,20)
    result.employment_status_txt = slice_data(row.empstattxt,50)
    result.ou_l1_id = slice_data(row.ou_level1,9)
    result.ou_l2_id = slice_data(row.ou_level2,9)
    result.ou_l3_id = slice_data(row.ou_level3,9)
    result.subfun1_id = slice_data(row.subfunction1_id,4)
    result.subfun2_id = slice_data(row.subfunction2_id,4)
    
    return result

def update_to_aws_cet_tscmsl(oracle_session,row,file_id):
    operation = None
    status = None
    message =None
    try:
        co_code = row.comp_code.rjust(4,'0')
        result = mssql_session.query(AWSEmployeeData).filter(AWSEmployeeData.pers_no == row.perno, AWSEmployeeData.co_code == co_code).first()
        if result: # check if exists in AWS-CET table
            operation = messages.UPDATED
            metadata_result = oracle_session.query(AwsCetMetaData).filter(AwsCetMetaData.person_no == row.perno).first()
            if not metadata_result: # check if not exists in AWS-CET metadata table
                metadata_result = AwsCetMetaData() # insert in AWS-CET metadata table from AWS-CET
                metadata_result.person_no = result.pers_no
                metadata_result.known_as = result.known_as
                metadata_result.cell_no = result.cell_no
                metadata_result.date_prob_comp = result.date_prob_comp
                metadata_result.smart_card_no = result.smart_card_no
                metadata_result.fax_num = result.fax_num
                metadata_result.upn_id = result.upn_id
                metadata_result.created_at = datetime.now()
                metadata_result.updated_at = datetime.now()
                oracle_session.add(metadata_result)
                oracle_session.commit()
            result = map_row_to_result_data(result,row)
            """--------------Update UPN-------------"""
            if not result.upn_id:
                ad_data = get_ad_data_by_perno_tscmsl(row.perno)
                if ad_data:
                    result.upn_id = ad_data.get('sAMAccountName').lower()

            mssql_session.commit()
            metadata_result = oracle_session.query(AwsCetMetaData).filter(AwsCetMetaData.person_no == row.perno).first()
            if metadata_result:
                aws_change_log = AwsCetChangeLogHistory()
                aws_change_log.person_no = metadata_result.person_no
                aws_change_log.known_as = metadata_result.known_as
                aws_change_log.cell_no = metadata_result.cell_no
                aws_change_log.date_prob_comp = metadata_result.date_prob_comp
                aws_change_log.smart_card_no = metadata_result.smart_card_no
                aws_change_log.fax_num = metadata_result.fax_num
                aws_change_log.upn_id = metadata_result.upn_id
                aws_change_log.created_at = metadata_result.created_at
                aws_change_log.updated_at = metadata_result.updated_at
                aws_change_log.file_id = file_id
                aws_change_log.log_created_at = datetime.now()
                oracle_session.add(aws_change_log)
                oracle_session.commit()

                metadata_result.person_no = result.pers_no
                metadata_result.known_as = result.known_as
                metadata_result.cell_no = result.cell_no
                metadata_result.date_prob_comp = result.date_prob_comp
                metadata_result.smart_card_no = result.smart_card_no
                metadata_result.fax_num = result.fax_num
                metadata_result.upn_id = result.upn_id
                metadata_result.updated_at = datetime.now()
                oracle_session.commit()
            status = messages.SUCCESS
            message = None
        else:
            operation = messages.INSERTED
            result = AWSEmployeeData() # insert in AWS-CET table
            
            result = map_row_to_result_data(result,row)
            result.known_as = None
            result.cell_no = None
            result.date_prob_comp = None
            result.code_handicap = row.challenged
            result.smart_card_no = None
            """--------------Create UPN Details-------------"""
            ad_data = get_ad_data_by_perno_tscmsl(row.perno)
            if ad_data:
                result.upn_id = ad_data.get('sAMAccountName').lower()
                if not row.imailid:
                    ad_data.get('mail').lower()
                    
            mssql_session.add(result)
            mssql_session.commit()
            if not oracle_session.query(AwsCetMetaData).filter(AwsCetMetaData.person_no == row.perno).first():
                metadata_result = AwsCetMetaData()
                metadata_result.person_no = row.perno
                metadata_result.known_as = None
                metadata_result.cell_no = None
                metadata_result.date_prob_comp = None
                metadata_result.smart_card_no = None
                metadata_result.fax_num = None
                if ad_data:
                    metadata_result.upn_id = ad_data.get('sAMAccountName').lower()
                metadata_result.created_at = datetime.now()
                metadata_result.updated_at = datetime.now()
                oracle_session.add(metadata_result)
                oracle_session.commit()
            status = messages.SUCCESS
            message = None
            
    except Exception as error:
        mssql_session.rollback()
        oracle_session.rollback()
        common_functions.print_error('update_to_aws_cet_tscmsl',error)
        status = messages.FAILED
        message = str(error)[:70]
    return operation, status, message