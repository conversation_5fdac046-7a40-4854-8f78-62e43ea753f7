from datetime import datetime, timed<PERSON><PERSON>
from sqlalchemy import and_
from commons.db_connections import create_new_oracle_session, create_oracle_session
# from commons.models import (
#     SapCompanyMaster1,
#     SapEmployeeMaster,
#     SAPPersAreaMaster,
#     SAPPersSAreaMaster,
#     SAPOrgUnitMaster,
#     SAPFunctionGrpMaster,
#     SAPSubFun1GrpMaster,
#     SAPSubFun2GrpMaster,
#     SAPOrgLevel1Master,
#     SAPOrgLevel2Master,
#     SAPOrgLevel3Master,
#     SAPCostCenterMaster,
#     SAPPayAreaMaster,
#     FileRowLogs,
#     FileLogs,
# )
from commons.models_v2 import (
    SapCompanyMaster1,
    SapEmployeeMaster,
    SAPPersAreaMaster,
    SAPPersSAreaMaster,
    SAPOrgUnitMaster,
    SAPFunctionGrpMaster,
    SAPSubFun1GrpMaster,
    SAPSubFun2GrpMaster,
    SAPOrgLevel1Master,
    SAPOrgLevel2Master,
    SAPOrgLevel3Master,
    SAPCostCenterMaster,
    SAPPayAreaMaster,
    ProcessRowLogs,
    ProcessLogs,
)
from commons.common_functions import print_error, comp_code

from sqlalchemy.orm import joinedload


def sync_sap_personal_area_master(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0

        # sap_personal_areas = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.pers_area_code,
        #         SapEmployeeMaster.pers_area_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )


        sap_personal_areas = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.pers_area_code,
                SapEmployeeMaster.pers_area_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in sap_personal_areas:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                ):
                    sap_personal_area = (
                        ms_sql_session.query(SAPPersAreaMaster)
                        .filter(
                            SAPPersAreaMaster.co_code == co_code,
                            SAPPersAreaMaster.pers_area == entry[1],
                        )
                        .first()
                    )
                    if sap_personal_area:
                        sap_personal_area = SAPPersAreaMaster()
                        sap_personal_area.pers_area_text = entry[2][:30]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPPersAreaMaster(
                                co_code=co_code,
                                pers_area=entry[1],
                                pers_area_text=entry[2][:30],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_personal_area_master", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_personal_subarea(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0

        # sap_personal_subareas = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.pers_area_code,
        #         SapEmployeeMaster.pers_subarea_code,
        #         SapEmployeeMaster.pers_subarea_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )
        sap_personal_subareas = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.pers_area_code,
                SapEmployeeMaster.pers_subarea_code,
                SapEmployeeMaster.pers_subarea_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in sap_personal_subareas:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                    and ms_sql_session.query(SAPPersAreaMaster)
                    .filter(
                        SAPPersAreaMaster.co_code == co_code,
                        SAPPersAreaMaster.pers_area == entry[1],
                    )
                    .first()
                ):
                    sap_personal_subarea = (
                        ms_sql_session.query(SAPPersSAreaMaster)
                        .filter(
                            SAPPersSAreaMaster.co_code == co_code,
                            SAPPersSAreaMaster.pers_area == entry[1],
                            SAPPersSAreaMaster.pers_subarea == entry[2],
                        )
                        .first()
                    )
                    if sap_personal_subarea:
                        sap_personal_subarea = SAPPersSAreaMaster()
                        sap_personal_subarea.pers_sarea_text = entry[3][:30]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPPersSAreaMaster(
                                co_code=co_code,
                                pers_area=entry[1],
                                pers_subarea=entry[2],
                                pers_sarea_text=entry[3][:30],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_personal_subarea", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_org_unit(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0

        # sap_org_units = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.org_unit_code,
        #         SapEmployeeMaster.org_unit_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )
        sap_org_units = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.org_unit_code,
                SapEmployeeMaster.org_unit_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in sap_org_units:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                ):
                    sap_org_unit = (
                        ms_sql_session.query(SAPOrgUnitMaster)
                        .filter(
                            SAPOrgUnitMaster.co_code == co_code,
                            SAPOrgUnitMaster.org_unit == entry[1],
                        )
                        .first()
                    )
                    if sap_org_unit:
                        sap_org_unit.org_unit_text = entry[2][:30]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPOrgUnitMaster(
                                co_code=co_code,
                                org_unit=entry[1],
                                org_unit_text=entry[2][:30],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_org_unit", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_function_grp(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0

        # function_grps = (
        #     oracle_session.query(
        #         SapEmployeeMaster.function_id_code, 
        #         SapEmployeeMaster.function_id_text
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )

        function_grps = (
            new_oracle_session.query(
                SapEmployeeMaster.function_id_code, 
                SapEmployeeMaster.function_id_text
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in function_grps:
            if None not in entry:
                function_grp = (
                    ms_sql_session.query(SAPFunctionGrpMaster)
                    .filter(SAPFunctionGrpMaster.funcode == entry[0])
                    .first()
                )
                if function_grp:
                    function_grp.fundesc = entry[1][:100]
                    function_grp.isactive = "Y"
                    ms_sql_session.commit()
                    update_count += 1
                else:
                    inserts.append(
                        SAPFunctionGrpMaster(
                            funcode=entry[0], fundesc=entry[1][:100], isactive="Y"
                        )
                    )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_function_grp", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_sub_function1_grp(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0

        # sub_function1_grps = (
        #     oracle_session.query(
        #         SapEmployeeMaster.function_id_code,
        #         SapEmployeeMaster.subfunction_id1_code,
        #         SapEmployeeMaster.subfunction_id1_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )

        sub_function1_grps = (
            new_oracle_session.query(
                SapEmployeeMaster.function_id_code,
                SapEmployeeMaster.subfunction_id1_code,
                SapEmployeeMaster.subfunction_id1_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in sub_function1_grps:
            if None not in entry:
                if (
                    ms_sql_session.query(SAPFunctionGrpMaster)
                    .filter(SAPFunctionGrpMaster.funcode == entry[0])
                    .first()
                ):
                    sub_function1_grp = (
                        ms_sql_session.query(SAPSubFun1GrpMaster)
                        .filter(
                            SAPSubFun1GrpMaster.funcode == entry[0],
                            SAPSubFun1GrpMaster.subfun1_id == entry[1],
                        )
                        .first()
                    )
                    if sub_function1_grp:
                        sub_function1_grp.subfun1_txt = entry[2][:50]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPSubFun1GrpMaster(
                                funcode=entry[0],
                                subfun1_id=entry[1],
                                subfun1_txt=entry[2][:50],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_sub_function1_grp", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_sub_function2_grp(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0
        # sub_function2_grps = (
        #     oracle_session.query(
        #         SapEmployeeMaster.function_id_code,
        #         SapEmployeeMaster.subfunction_id1_code,
        #         SapEmployeeMaster.subfunction_id2_code,
        #         SapEmployeeMaster.subfunction_id2_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )
        sub_function2_grps = (
            new_oracle_session.query(
                SapEmployeeMaster.function_id_code,
                SapEmployeeMaster.subfunction_id1_code,
                SapEmployeeMaster.subfunction_id2_code,
                SapEmployeeMaster.subfunction_id2_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in sub_function2_grps:
            if None not in entry:
                if (
                    ms_sql_session.query(SAPFunctionGrpMaster)
                    .filter(SAPFunctionGrpMaster.funcode == entry[0])
                    .first()
                    and ms_sql_session.query(SAPSubFun1GrpMaster)
                    .filter(
                        SAPSubFun1GrpMaster.funcode == entry[0],
                        SAPSubFun1GrpMaster.subfun1_id == entry[1],
                    )
                    .first()
                ):
                    sub_function2_grp = (
                        ms_sql_session.query(SAPSubFun2GrpMaster)
                        .filter(
                            SAPSubFun2GrpMaster.funcode == entry[0],
                            SAPSubFun2GrpMaster.subfun1_id == entry[1],
                            SAPSubFun2GrpMaster.subfun2_id == entry[2],
                        )
                        .first()
                    )
                    if sub_function2_grp:
                        sub_function2_grp.subfun2_txt = entry[3][:50]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPSubFun2GrpMaster(
                                funcode=entry[0],
                                subfun1_id=entry[1],
                                subfun2_id=entry[2],
                                subfun2_txt=entry[3][:50],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_sub_function2_grp", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_org_level1(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()

        inserts = []
        update_count = 0
        # org_level1s = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.org_unit_code,
        #         SapEmployeeMaster.ou_level1_code,
        #         SapEmployeeMaster.ou_level1_short_text,
        #         SapEmployeeMaster.ou_level1_long_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )
        org_level1s = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.org_unit_code,
                SapEmployeeMaster.ou_level1_code,
                SapEmployeeMaster.ou_level1_short_text,
                SapEmployeeMaster.ou_level1_long_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in org_level1s:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                    and ms_sql_session.query(SAPOrgUnitMaster)
                    .filter(
                        SAPOrgUnitMaster.co_code == co_code,
                        SAPOrgUnitMaster.org_unit == entry[1],
                    )
                    .first()
                ):
                    org_level1 = (
                        ms_sql_session.query(SAPOrgLevel1Master)
                        .filter(
                            SAPOrgLevel1Master.co_code == co_code,
                            SAPOrgLevel1Master.org_unit == entry[1],
                            SAPOrgLevel1Master.ou_l1_id == entry[2],
                        )
                        .first()
                    )
                    if org_level1:
                        org_level1.ou_l1_sht_txt = entry[3][:25]
                        org_level1.ou_l1_long_txt = entry[4][:40]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPOrgLevel1Master(
                                co_code=co_code,
                                org_unit=entry[1],
                                ou_l1_id=entry[2],
                                ou_l1_sht_txt=entry[3][:25],
                                ou_l1_long_txt=entry[4][:40],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_org_level1", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_org_level2(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0
        # org_level2s = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.org_unit_code,
        #         SapEmployeeMaster.ou_level2_code,
        #         SapEmployeeMaster.ou_level2_short_text,
        #         SapEmployeeMaster.ou_level2_long_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )

        org_level2s = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.org_unit_code,
                SapEmployeeMaster.ou_level2_code,
                SapEmployeeMaster.ou_level2_short_text,
                SapEmployeeMaster.ou_level2_long_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )
        for entry in org_level2s:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                    and ms_sql_session.query(SAPOrgUnitMaster)
                    .filter(
                        SAPOrgUnitMaster.co_code == co_code,
                        SAPOrgUnitMaster.org_unit == entry[1],
                    )
                    .first()
                ):
                    org_level2 = (
                        ms_sql_session.query(SAPOrgLevel2Master)
                        .filter(
                            SAPOrgLevel2Master.co_code == co_code,
                            SAPOrgLevel2Master.org_unit == entry[1],
                            SAPOrgLevel2Master.ou_l2_id == entry[2],
                        )
                        .first()
                    )
                    if org_level2:
                        org_level2.ou_l2_sht_txt = entry[3][:25]
                        org_level2.ou_l2_long_txt = entry[4][:40]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPOrgLevel2Master(
                                co_code=co_code,
                                org_unit=entry[1],
                                ou_l2_id=entry[2],
                                ou_l2_sht_txt=entry[3][:25],
                                ou_l2_long_txt=entry[4][:40],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_org_level2", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_org_level3(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0
        # org_level3s = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.org_unit_code,
        #         SapEmployeeMaster.ou_level3_code,
        #         SapEmployeeMaster.ou_level3_short_text,
        #         SapEmployeeMaster.ou_level3_long_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )
        org_level3s = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.org_unit_code,
                SapEmployeeMaster.ou_level3_code,
                SapEmployeeMaster.ou_level3_short_text,
                SapEmployeeMaster.ou_level3_long_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in org_level3s:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                    and ms_sql_session.query(SAPOrgUnitMaster)
                    .filter(
                        SAPOrgUnitMaster.co_code == co_code,
                        SAPOrgUnitMaster.org_unit == entry[1],
                    )
                    .first()
                ):
                    org_level3 = (
                        ms_sql_session.query(SAPOrgLevel3Master)
                        .filter(
                            SAPOrgLevel3Master.co_code == co_code,
                            SAPOrgLevel3Master.org_unit == entry[1],
                            SAPOrgLevel3Master.ou_l3_id == entry[2],
                        )
                        .first()
                    )
                    if org_level3:
                        org_level3.ou_l3_sht_txt = entry[3][:25]
                        org_level3.ou_l3_long_txt = entry[4][:40]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPOrgLevel3Master(
                                co_code=co_code,
                                org_unit=entry[1],
                                ou_l3_id=entry[2],
                                ou_l3_sht_txt=entry[3][:25],
                                ou_l3_long_txt=entry[4][:40],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_org_level3", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_cost_center(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0
        # cost_centers = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.cost_center_code,
        #         SapEmployeeMaster.cost_center_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )
        cost_centers = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.cost_center_code,
                SapEmployeeMaster.cost_center_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in cost_centers:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                ):
                    cost_center = (
                        ms_sql_session.query(SAPCostCenterMaster)
                        .filter(
                            SAPCostCenterMaster.co_code == co_code,
                            SAPCostCenterMaster.cost_center == entry[1],
                        )
                        .first()
                    )
                    if cost_center:
                        cost_center.cost_center_text = entry[2][:100]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPCostCenterMaster(
                                co_code=co_code,
                                cost_center=entry[1],
                                cost_center_text=entry[2][:100],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_cost_center", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()


def sync_sap_pay_area(session):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        ms_sql_session = session()
        inserts = []
        update_count = 0
        # pay_areas = (
        #     oracle_session.query(
        #         SapEmployeeMaster.company_code,
        #         SapEmployeeMaster.payroll_area_code,
        #         SapEmployeeMaster.payroll_area_text,
        #     )
        #     .join(
        #         FileRowLogs,
        #         FileRowLogs.perno == SapEmployeeMaster.person_no,
        #     )
        #     .join(FileLogs, FileLogs.file_id == FileRowLogs.file_id)
        #     .filter(FileLogs.created_at >= datetime.now() - timedelta(days=1))
        #     .distinct()
        #     .all()
        # )

        pay_areas = (
            new_oracle_session.query(
                SapEmployeeMaster.company_code,
                SapEmployeeMaster.payroll_area_code,
                SapEmployeeMaster.payroll_area_text,
            )
            .join(ProcessRowLogs, ProcessRowLogs.perno == SapEmployeeMaster.person_no)
            .join(ProcessLogs, ProcessLogs.file_id == ProcessRowLogs.file_id)
            .filter(ProcessLogs.started_at >= datetime.now() - timedelta(days=1))
            .options(joinedload(ProcessRowLogs.process_log))  # Eager load the ProcessLogs related data
            .distinct()
            .all()
        )

        for entry in pay_areas:
            if None not in entry:
                co_code = comp_code(comp_code=entry[0])
                if (
                    ms_sql_session.query(SapCompanyMaster1)
                    .filter(SapCompanyMaster1.co_code == co_code)
                    .first()
                ):
                    pay_area = (
                        ms_sql_session.query(SAPPayAreaMaster)
                        .filter(
                            SAPPayAreaMaster.co_code == co_code,
                            SAPPayAreaMaster.pay_area == entry[1],
                        )
                        .first()
                    )
                    if pay_area:
                        pay_area.co_code = co_code
                        pay_area.pay_area = entry[1]
                        pay_area.pay_area_name = entry[2][:100]
                        ms_sql_session.commit()
                        update_count += 1
                    else:
                        inserts.append(
                            SAPPayAreaMaster(
                                co_code=co_code,
                                pay_area=entry[1],
                                pay_area_name=entry[2][:100],
                            )
                        )
        print(f"update_count - {update_count}")
        if inserts:
            ms_sql_session.add_all(inserts)
            ms_sql_session.commit()
            print(f"insert_count - {len(inserts)}")
    except Exception as error:
        print_error("sync_sap_pay_area", error)
    finally:
        new_oracle_session.close()
        ms_sql_session.close()
