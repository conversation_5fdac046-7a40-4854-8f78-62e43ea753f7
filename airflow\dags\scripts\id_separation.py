# In-built imports...
from datetime import datetime, timedelta
import json, requests, time
from sqlalchemy import func
# Custom imports...
from commons.db_connections import create_EDP_postgres_session, create_new_oracle_session, create_oracle_session
from commons.notifications import send_failed_notification
from commons.messages import APPROVED_BY_BHR, APPROVED_BY_DEM, FAILED, PENDING, REJECTED, RE<PERSON>KED, SUCCESS
from commons.common_functions import print_error
# from commons.models import EmployeeSeparation, IdSeparationLogs, IdSeparationRowLogs
from commons.models_v2 import EmployeeSeparation, IdSeparationLogs, IdSeparationRowLogs
from commons import configs

def get_yesterdays_date():
    curr_date = datetime.now()
    yesterdays_date = curr_date - timedelta(1)
    return yesterdays_date.date()

def create_separation_log(session, separation_log_object):
    separation_log = IdSeparationLogs()
    separation_log.separation_log_id = 'SEP' + str(int(time.time_ns() * 10))
    separation_log.row_count = separation_log_object["row_count"] if "row_count" in separation_log_object else ""
    separation_log.status = separation_log_object["status"] if "status" in separation_log_object else ""
    separation_log.created_at = datetime.now()
    if "row_count" not in separation_log_object or not separation_log_object["row_count"] > 0:
        separation_log.reason = configs.DATA_NOT_FOUND
        session.add(separation_log)
        session.commit()
        # send_failed_notification(separation_log)
        return separation_log
    session.add(separation_log)
    session.commit()
    return separation_log

def update_separation_row_log(session, separation_row_log_object, separation_row_log = None):
    is_create = False
    if separation_row_log is None:
        separation_row_log = IdSeparationRowLogs()
        is_create = True
        separation_row_log.separation_row_log_id = "SEPROW" + str(int(time.time_ns() * 10))
    if "perno" in separation_row_log_object :
        separation_row_log.perno = separation_row_log_object['perno']
    if "separation_status" in separation_row_log_object :
        separation_row_log.separation_status = separation_row_log_object['separation_status'] 
    if "separation_date" in separation_row_log_object:
        separation_row_log.separation_date = separation_row_log_object['separation_date'] 
    if "is_deleted" in separation_row_log_object:
        separation_row_log.is_deleted = separation_row_log_object['is_deleted'] 
    if "separation_log_id" in separation_row_log_object:
        separation_row_log.separation_log_id = separation_row_log_object['separation_log_id'] 
    if "reason" in separation_row_log_object:
        separation_row_log.reason = separation_row_log_object['reason'] 
    if "status" in separation_row_log_object:
        separation_row_log.status = separation_row_log_object['status'] 
    if "sap_id" in separation_row_log_object:
        separation_row_log.sap_id = separation_row_log_object['sap_id'] 
    if is_create: 
        session.add(separation_row_log)
        print("Separation row log created")
    session.commit()
    return separation_row_log

def update_employee_separation() :
    try:
        postgres_session = create_EDP_postgres_session()
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        previous_date = get_yesterdays_date()
        emp_sep = postgres_session.query(EmployeeSeparation).filter(func.DATE(EmployeeSeparation.modified_datetime) == previous_date).all()
        separation_log_object = {
            'status' : SUCCESS,
            'row_count': len(emp_sep)
        }
        separation_log = create_separation_log(new_oracle_session, separation_log_object)
        for emp in emp_sep:
            API_URL = configs.SAP_GET_EMPLOYEE_DETAILS_BY_PERNO
            requests_body = {**configs.REQUEST_BODY}
            body = {**configs.REQUEST_BODY}
            body['extensionAttribute5'] = str(int(emp.employee_id))
            if 'attributes' in body:
                del body['attributes']
            if PENDING in emp.separation_status and not emp.is_deleted :
                requests_body["separation_date"] = str(emp.lwd_requested_by_employee)
            elif emp.is_deleted :
                requests_body["separation_date"] = configs.REVOKED_SEPARATION_DATE
            elif REJECTED in emp.separation_status or REVOKED in emp.separation_status:
                requests_body["separation_date"] = configs.REVOKED_SEPARATION_DATE
            elif APPROVED_BY_DEM in emp.separation_status:
                requests_body["separation_date"] = str(emp.lwd_recommended_by_dem)
            elif APPROVED_BY_BHR in emp.separation_status:
                requests_body["separation_date"] = str(emp.lwd_recommended_by_bhr)
            date_obj = datetime.strptime(requests_body["separation_date"], '%Y-%m-%d')
            separation_row_log_object = {
                'perno' : int(emp.employee_id),
                'is_deleted' : emp.is_deleted,
                'separation_status': emp.separation_status,
                'separation_date' : date_obj,
                'separation_log_id' : separation_log.separation_log_id,
                'status': SUCCESS
            }
            separtion_row_log = update_separation_row_log(new_oracle_session, separation_row_log_object)
            response = requests.get(configs.AD_LDAP_USER_URL,headers=configs.HEADER,params=body)
            ad_data = json.loads(response.content)
            if response.status_code == 200 and 'result' in ad_data and len(ad_data['result']) > 0:
                data = ad_data['result'][0]
                ad_data = data
                requests_body["user_id"] = ad_data['sAMAccountName'].upper() if ad_data.get('sAMAccountName') else None
                requests_body["employee_id"] = ad_data['sAMAccountName'].upper() if ad_data.get('sAMAccountName') else None
                separation_row_log_object['sap_id'] = requests_body["user_id"]
                requests_body["comp_code"] = int(emp.employee_comp_code)
                requests_body["company_code"] = int(emp.employee_comp_code)
                response = requests.get(API_URL,headers=configs.HEADER,params=requests_body)
                res = json.loads(response.content)
                if response.status_code == 200 and "result" in res and len(res["result"])>0 and 'TYPE' in json.loads(res["result"][0]) and json.loads(res["result"][0])['TYPE'] == 'S':
                    API_URL = configs.SAP_UPDATE_DATE_SEPARATION
                    requests_body["separation_date"] = date_obj.strftime('%d.%m.%Y')
                    separation_row_log_object['separation_date'] = date_obj
                    response = requests.post(API_URL,headers=configs.HEADER,data=json.dumps(requests_body))
                    res = json.loads(response.content)
                    res = res[0] if type(res) != dict else res
                    if response.status_code == 200 and "result" in res and len(res["result"]) > 0:
                        json_data = json.loads(''.join(res["result"]))
                        if any(item["NUMBER"] in [29, 39] for item in json_data):
                            separation_row_log_object['status'] = SUCCESS
                            separation_row_log_object['reason'] = res["MESSAGE"]  if 'MESSAGE' in res else res['message']
                            separtion_row_log = update_separation_row_log(new_oracle_session, separation_row_log_object, separtion_row_log)
                    else:
                        separation_row_log_object['status'] = FAILED
                        separation_row_log_object['reason'] = res["MESSAGE"]  if 'MESSAGE' in res else res['message']
                        separtion_row_log = update_separation_row_log(new_oracle_session, separation_row_log_object, separtion_row_log)
                        print('update_employee_separation response_code',response.status_code)
                else:
                    separation_row_log_object['status'] = FAILED
                    separation_row_log_object['reason'] = res["MESSAGE"] if 'MESSAGE' in res else res['message']
                    separtion_row_log = update_separation_row_log(new_oracle_session, separation_row_log_object, separtion_row_log)
                    print('update_employee_separation response_code',response.status_code)
            else: 
                separation_row_log_object['status'] = FAILED
                separation_row_log_object['reason'] = ad_data["message"]
                separtion_row_log = update_separation_row_log(new_oracle_session, separation_row_log_object, separtion_row_log)
                print('get_ad_data_by_perno status code - ',response.status_code)
            
    except Exception as error:
        print_error("update_employee_separation",error)