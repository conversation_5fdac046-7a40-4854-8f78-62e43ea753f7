# In-built imports...
import requests, json, time
from datetime import datetime
from dateutil.relativedelta import relativedelta
import calendar
# Custom imports...
from commons.db_connections import create_new_oracle_session, create_oracle_session
from commons.common_functions_tscmsl import encrypt_content_tscmsl, format_date_tscmsl, generate_password_tscmsl, get_changed_attributes_for_update_tscmsl
from commons import configs
from commons.messages import *
from commons.common_functions_tscmsl import print_error
# from commons.models import ADMetaData, ADChangeLogHistory, FileRowLogs, ESubGroup, IMAC_logs, PaPsa, ESubGroup,ExtAttr13Mappping
from commons.models_v2 import ADMetaData, ADChangeLogHistory, ProcessRowLogs, ESubGroup, IMAC_Logs, Pa, Psa, ESubGroup,ExtAttr13Mappping

def get_ad_data_by_perno_tscmsl(perno):
    try:
        body = {**configs.REQUEST_BODY}
        body['extensionAttribute5'] = perno
        if 'attributes' in body:
            del body['attributes']
        response = requests.get(configs.AD_LDAP_USER_URL,headers=configs.HEADER,params=body)
        if response.status_code == 200:
            ad_data = json.loads(response.content)
            if 'result' in ad_data and len(ad_data['result']) > 0:
                data = ad_data['result'][0]
                ad_data = data
                data['samAccountName'] = ad_data['sAMAccountName'] if ad_data.get('sAMAccountName') else None
                data['EmployeeID'] = ad_data['extensionAttribute5'] if ad_data.get('extensionAttribute5') else None
                data['DisplayName'] = ad_data['displayName'] if ad_data.get('displayName') else None
                data['division'] = ad_data['division'] if ad_data.get('division') else None
                data['physicalDeliveryOfficeName'] = ad_data['physicalDeliveryOfficeName'] if ad_data.get('physicalDeliveryOfficeName') else None
                data['department'] = ad_data['department'] if ad_data.get('department') else None
                data['streetAddress'] = ad_data['streetAddress'] if ad_data.get('streetAddress') else None
                data['info'] = ad_data['info'] if ad_data.get('info') else None
                data['ou'] = generate_ou_from_dn_tscmsl(ad_data.get('distinguishedName'))
                data['userPrincipalName'] = ad_data['userPrincipalName'] if ad_data.get('userPrincipalName') else None
                data['mail'] = ad_data['mail'] if ad_data.get('mail') else None
                data['manager'] = ad_data['manager'] if ad_data.get('manager') else None
                data['accountExpires'], date_error = format_date_tscmsl(ad_data['accountExpires'] if ad_data.get('accountExpires') else None)
                data['password'] = configs.AD_PASSWORD
                data['is_active'] = ad_data['is_active'] if ad_data.get('is_active')!= None else None
                return data
            return None
        else:
            print('get_ad_data_by_perno status code - ',response.status_code)
            return None
    except Exception as error:
        print_error(error)
        raise Exception(error)

def delete_to_ad_tscmsl(adid):
    try:
        body = {**configs.REQUEST_BODY}
        body['sAMAccountName'] = adid
        request = requests.delete(url = configs.AD_LDAP_USER_URL, headers = configs.HEADER, data=json.dumps(body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == RECORD_DELETED_SUCCESSFULLY:
                print(request.status_code,json.loads(request.content))
                return True,'success'
                #print(body)
            else:
                return False, json.loads(request.content)['message']
        else:
            return False, request.status_code
    except Exception as error:
        print(error)
        return False, str(error)

def create_ad_attributes_tscmsl(row, old_email=None,samAccountName = None, userPrincipalName=None):
    try:
        attributes = {}
        if row.perno and row.firstname:
            user_id = create_userid_tscmsl(row.perno,row.firstname,row.midnm,row.last_name).upper()
            attributes['EmployeeID'] = row.perno
            attributes['extensionAttribute1'] = row.perno
            attributes['extensionAttribute5'] = row.perno
            attributes['pager'] = row.perno
            attributes['samAccountName'] =samAccountName if samAccountName else user_id
            attributes['givenName'] = row.firstname
            attributes['userPrincipalName'] = userPrincipalName if userPrincipalName else f'{user_id}@tatamotors.com'
        if row.last_name is not None:
            attributes['sn'] = row.last_name
        if row.midnm is not None:
            attributes['initials'] = row.midnm[0]
        if row.postxt:
            attributes['title'] = row.postxt
        if row.function_text:
            attributes['otherTelePhone'] = row.function_text
        if row.reporting:
            attributes['manager'] = row.reporting

        if row.ccodetxt:
            attributes['company'] = row.ccodetxt
        
        # department = create_department_tscmsl(row.comp_code,row.orgtx,row.psatxt)
        # if department:
        #     attributes['department'] = department
        
        attributes['department'] = row.orgtx if row.orgtx else ''
        
        if row.state:
            attributes['st'] = row.state
        
        if row.country:
            attributes['co'] = row.country
        
        if row.city_town:
            attributes['l'] = row.city_town

        if row.pin_code:
            attributes['postalCode'] = row.pin_code

        if row.off_num:
            attributes['telephoneNumber'] = row.off_num
            
        if not row.esubgroup == 'ED':
            if row.office_mobile is not None:
                attributes['mobile'] = str(row.office_mobile).split(',')[0]

            if row.pers_mobile is not None:
                attributes['homePhone'] = str(row.pers_mobile).split(',')[0]

        if row.imailid:
            attributes['mail'] = row.imailid
        elif old_email:
            attributes['mail'] = old_email
        else:
            attributes['mail'] = f'{user_id}@tatamotors.com'
            # row.imailid = f'{user_id}@tatamotors.com'

        if row.costcenter and row.cosctrtxt:
            attributes['info'] = f'Cost Centre: {row.costcenter} - {row.cosctrtxt}'

        if row.costcenter:
            attributes['extensionAttribute3'] = row.costcenter

        if row.esgtxt:
            # attributes['extensionAttribute2'] = row.esgtxt
            attributes['msExchAssistantName'] = row.esgtxt

        # if row.subfunction2_tex:
        #     attributes['telePhoneAssistant'] = row.subfunction2_tex

        if row.title:
            attributes['extensionAttribute10'] = row.title
            ExtensionAttribute9 = identify_gender_tscmsl(row.title)
            if ExtensionAttribute9:
                attributes['extensionAttribute9'] = ExtensionAttribute9

        if row.patxt:
            attributes['extensionAttribute11'] = row.patxt

        if row.psatxt:
            attributes['extensionAttribute12'] = row.psatxt

        if row.loc_desc:
            # oracle_session = create_oracle_session()
            new_oracle_session = create_new_oracle_session()
            loc_desc = new_oracle_session.query(ExtAttr13Mappping).filter(ExtAttr13Mappping.location == row.loc_desc).first()

            attributes['extensionAttribute13'] = row.loc_desc
            if  loc_desc:
                attributes['extensionAttribute13'] = loc_desc.extattr13
            
        if row.subfunction1_tex:
            attributes['facsimileTelephoneNumber'] = row.subfunction1_tex

        if row.dob:
            accountExpires = create_account_expiry_tscmsl(row.dob)
            if accountExpires:
                attributes['accountExpires'] = accountExpires

        physicalDeliveryOfficeName = create_office_tscmsl(row.ou_level1_short_txt, row.ou_level2_short_txt, row.ou_level3_long_txt)
        if physicalDeliveryOfficeName:
            attributes['physicalDeliveryOfficeName'] = physicalDeliveryOfficeName
        if row.ou_level2_long_txt:
            attributes['division'] = row.ou_level2_long_txt
        streetAddress = f'{row.add1 if row.add1 else ""} {row.add2 if row.add2 else ""} {row.add3 if row.add3 else ""} {row.add4 if row.add4 else ""} {row.loc_desc if row.loc_desc else ""}'
        if streetAddress:  
            attributes['streetAddress'] = streetAddress


        fullname = create_fullname_tscmsl(row.firstname, row.last_name, row.title)
        if fullname:
            description = create_description_tscmsl(row.ou_level1_short_txt,row.function_text,row.city_town)
            if description:
                attributes['DisplayName'] = f'{fullname} [ {description} ]'
        return attributes
    except Exception as error:
        print_error("create_ad_attributes",error)
        raise Exception(error)

def create_fullname_tscmsl(firstname, lastname, title):
    if title is not None:
        title = title.replace(" ","").strip(".")
    fullname = ''
    if firstname:
        fullname = firstname.upper()
    if lastname:
        fullname = f'{fullname} {lastname.upper()}'
    if fullname:
        if title == None:
            return f'{fullname}'
        elif 'doctor' in  title.lower() or 'dr' in title.lower():
            return f'Dr {fullname}'
        elif 'mr' in title.lower() or "mrs" in title.lower() or "ms" in title.lower():
            return f'{fullname}'
        else:
            return f'{title} {fullname}'
    else:
        print(FULLNAME_NOT_CREATED)
        return None

def identify_gender_tscmsl(title):
    if title.strip(".") in configs.FEMALE:
        return 'F'
    elif title.strip(".") in configs.MALE:
        return 'M'
    else:
        return ''

def generate_ou_from_dn_tscmsl(dn):
    try:
        if dn is not None:
            OU = dn.split(',')
            data = {}
            for ele in OU:
                split_ele = ele.split('=')
                if len(split_ele)>=2: 
                    data[split_ele[0].lower()] = split_ele[1]
                if 'ou' in data:
                    if not 'Users' in data.get('ou'):
                        return data['ou']
                    else:
                        ou_list =data['ou'].split('Users')
                        return ou_list[0]
        return None
    except Exception as error:
        print_error(error)
        raise Exception(error)

def create_ou_tscmsl():
        return configs.TSCMSL_COMP

def create_description_tscmsl(ou_level1_short_txt,function_text,city_town):
    #----------------------------------Company Smart City-----------------------------------
    description = ''
    if ou_level1_short_txt:
        description = ou_level1_short_txt
    if function_text:
        if not description:
            description = f'{function_text}'
        else:
            description = f'{description}, {function_text}'
    if city_town:
        if not description:
            description = f'{city_town}'
        else:
            description = f'{description}, {city_town}'
    return description

def create_office_tscmsl(ou_level1_short_txt, ou_level2_short_txt, ou_level3_long_txt):
    ou_level1_short_txt = ou_level1_short_txt if ou_level1_short_txt else ''
    ou_level2_short_txt = f' , {ou_level2_short_txt}' if ou_level2_short_txt else ''
    ou_level3_long_txt = f' , {ou_level3_long_txt}' if ou_level3_long_txt else ''

    return f'{ou_level1_short_txt}{ou_level2_short_txt}{ou_level3_long_txt}'
    
def create_department_tscmsl(orgtx):
    return orgtx if orgtx else ""
    
def create_userid_tscmsl(perno,firstname,middlename,lastname):
    try:
        if firstname is not None:
            if lastname is not None:
                if middlename is not None:
                    return f'{firstname[0]}{middlename[0]}{lastname[0]}{perno}'
                else:
                    return f'{firstname[0]}{lastname[0]}{lastname[0]}{perno}'
            elif middlename is not None:
                return f'{firstname[0]}{middlename[0]}{middlename[0]}{perno}'
            else:
                return f'{firstname[0]}{firstname[0]}{firstname[0]}{perno}'
    except Exception as error:
        print_error("create_userid_tscmsl",error)
        raise Exception(error)

def create_account_expiry_tscmsl(dob):
    try:
        dob = dob[:10]
        try:
            datetime_object = datetime.strptime(dob, '%Y-%m-%d')
        except:
            datetime_object = datetime.strptime(dob, '%d-%m-%Y')
        account_expiry = datetime_object+relativedelta(years=60)
        last_date = calendar.monthrange(account_expiry.year, account_expiry.month)[1]
        return str(account_expiry.replace(day=last_date))
    except Exception as error:
        print_error("create_account_expiry_tscmsl",error)
        raise Exception(error)

def disable_employee_tscmsl(username):
    try:
        request_body = {**configs.REQUEST_BODY}
        request_body['username'] = username
        request_body['attributes'] = {
            "userAccountControl": 514,
            "accountExpires":  str(datetime.now())[:19]
        }
        request = requests.put(configs.AD_LDAP_USER_URL,headers=configs.HEADER,data=json.dumps(request_body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == RECORD_UPDATED_SUCCESSFULLY:
                return True, request_body['attributes']
            else:
                print("disable_employee response_object",json.loads(request.content))
                return False, None
        else:
            print('disable_employee response_code',request.status_code)
            return False, None
    except Exception as error:
        print_error('disable_employee_tscmsl',error)
        raise Exception(error)
    
def access_revocation_sap_tscmsl(username):
    try:
        request_body = {**configs.REQUEST_BODY}
        request_body['user_id'] = username
        request_body['company_code'] = 760
        request = requests.post(configs.SAP_TSCMSL_USER_ACCESS_REVOCATION,headers=configs.HEADER,data=json.dumps(request_body))
        result = json.loads(request.content)
        if request.status_code == 200:
            return True, result["message"]
        else:
            print('access_revocation_sap_tscmsl response_code',request.status_code)
            return False, result["message"]
    except Exception as error:
        print_error('access_revocation_sap_tscmsl',error)
        return False, error
    
def update_employee_tscmsl(result,record):
    try:
        attributes = create_ad_attributes_tscmsl(record, result.get('mail'), result.get('samAccountName'), result.get('userPrincipalName'))
        change = get_changed_attributes_for_update_tscmsl(attributes,result)
        request_body = {**configs.REQUEST_BODY}
        request_body['username'] = result['samAccountName'] 
        ou = create_ou_tscmsl()
        if result['ou'] != ou:
            print(f'changed - ou - {result["ou"]} to {ou}')
            request_body['ou'] = f'{ou}Users'
        request_body['attributes'] = change
        request = requests.put(configs.AD_LDAP_USER_URL,headers=configs.HEADER,data=json.dumps(request_body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == RECORD_UPDATED_SUCCESSFULLY:
                if request_body.get('ou'):
                    attributes['ou'] = request_body['ou']
                    if 'ou' in request_body:
                        del request_body['ou']
                return True, attributes
            else:
                print("update_employee response_object",json.loads(request.content))
                return False, None
        else:
            print('update_employee response_code',request.status_code)
            return False, None
    except Exception as error:
        print_error('update_employee_tscmsl',error)
        raise Exception(error)
    
def insert_employee_tscmsl(record):
    try:
        attributes = create_ad_attributes_tscmsl(record)
        request_body = {**configs.REQUEST_BODY}
        request_body['samAccountName'] = attributes['samAccountName']
        request_body['ou'] = f'{create_ou_tscmsl()}Users'
        request_body['password'] = generate_password_tscmsl()
        request_body['add_to_internet_group'] = True
        request_body['add_to_wifi_group'] = True
        request_body['attributes'] = attributes
        request = requests.post(configs.AD_LDAP_USER_URL,headers=configs.HEADER,data=json.dumps(request_body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == RECORD_ADDED_SUCCESSFULLY:
                attributes['ou'] = request_body['ou']
                attributes['passwd'] = request_body['password']
                attributes['internet_access'] = request_body['add_to_internet_group']
                return True, attributes
            else:
                print("insert_employee_tscmsl response_object",json.loads(request.content))
                return False, None
        else:
            print('insert_employee_tscmsl response_code',request.status_code)
            return False, None
    except Exception as error:
        print_error("insert_employee_tscmsl",error)
        raise Exception(error)
    
# def update_upn_tscmsl(perno,comp_code,upn,email):
#     try:
#         API_URL = configs.SAP_TSCMSL_UPDATE_UPN_EMAIL_URL

#         requests_body = {**configs.REQUEST_BODY}
#         requests_body['data']=[{'perno': perno, "upn_id": upn}]

#         request = requests.post(API_URL,headers=configs.HEADER,data=json.dumps(requests_body))

#         if request.status_code == 200:
#             request = json.loads(request.content)
#             if 'statusCode' in request and request['statusCode'] and configs.STATUS_CODE in request['statusCode'] and 'status' in request and request['status'] and configs.REQUEST_RECEIVED in request['status'] and 'message' in request and request['message'] and configs.REQUEST_RECIEVED_SUCCESSFULLY in request['message']:
#                 return True
#             else:
#                 print("update_upn_tscmsl response_object",request)
#                 return False
#         else:
#             print('update_upn_tscmsl response_code',request.status_code)
#             return False
#     except Exception as error:
#         print_error("update_upn_tscmsl",error)
#         return False
    
def map_ad_change_log_history_data(ad_change_log, metadata_result,file_id):
    ad_change_log.user_id = metadata_result.user_id
    ad_change_log.person_no = metadata_result.person_no
    ad_change_log.displayname = metadata_result.displayname
    ad_change_log.division = metadata_result.division
    ad_change_log.office = metadata_result.office
    ad_change_log.department = metadata_result.department
    ad_change_log.address = metadata_result.address
    ad_change_log.notes = metadata_result.notes
    ad_change_log.ou = metadata_result.ou
    ad_change_log.domain = metadata_result.domain
    ad_change_log.upn = metadata_result.upn
    ad_change_log.email = metadata_result.email
    ad_change_log.reporting_dn = metadata_result.reporting_dn
    ad_change_log.expiry_date = metadata_result.expiry_date
    ad_change_log.initial_password = metadata_result.initial_password
    ad_change_log.created_by = metadata_result.created_by
    ad_change_log.created_at = metadata_result.created_at
    ad_change_log.updated_at = metadata_result.updated_at
    ad_change_log.is_deleted = metadata_result.is_deleted
    ad_change_log.is_active = metadata_result.is_active
    ad_change_log.is_manual_entry = metadata_result.is_manual_entry
    ad_change_log.internet_access = metadata_result.internet_access
    ad_change_log.is_external_email = metadata_result.is_external_email
    ad_change_log.is_internal_migration = metadata_result.is_internal_migration
    ad_change_log.file_id = file_id
    ad_change_log.log_created_at = datetime.now()
    ad_change_log.log_created_by = configs.LOG_CREATED_BY
    ad_change_log.spoc_per_no = metadata_result.spoc_per_no
    
    return ad_change_log
    
# ------------------------------------Smart City -----------------------------------
# Active Directory Sync
def sync_record_in_active_directory_tsctmsl(oracle_session, record, file_id):
    try:
        status = None
        message = None
        operation = None
        upn_status = None
        sap_status = None
        sap_msg = None
        result = get_ad_data_by_perno_tscmsl(record.perno)
        if result:
            operation = UPDATED
            metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
            if not metadata_result:
                metadata_result = ADMetaData()
                metadata_result.user_id = result['samAccountName'].upper()
                metadata_result.person_no = result['EmployeeID']
                metadata_result.displayname = result['DisplayName']
                metadata_result.division = result['division']
                metadata_result.office = result['physicalDeliveryOfficeName']
                metadata_result.department = result['department']
                metadata_result.address = result['streetAddress']
                metadata_result.notes = result['info']
                metadata_result.ou = result['ou']
                metadata_result.domain = configs.DOMAIN
                metadata_result.upn = result['userPrincipalName']
                metadata_result.email = result['mail']
                metadata_result.reporting_dn = result['manager']
                metadata_result.expiry_date = datetime.strptime(result['accountExpires'][:19], '%Y-%m-%d %H:%M:%S')
                metadata_result.initial_password = None
                metadata_result.created_by = configs.CREATED_BY
                metadata_result.created_at = datetime.strptime(result['whenCreated'][:19], '%Y-%m-%d %H:%M:%S')
                metadata_result.updated_at = datetime.strptime(result['whenChanged'][:19], '%Y-%m-%d %H:%M:%S')
                metadata_result.is_deleted = False
                metadata_result.is_active = result['is_active']
                metadata_result.is_manual_entry = False
                metadata_result.internet_access = True
                metadata_result.is_external_email = False
                metadata_result.mailbox_status = True
                metadata_result.is_internal_migration = False
                oracle_session.add(metadata_result)
                oracle_session.commit()
            # if oracle_session.query(ESubGroup).filter(ESubGroup.esubgroup_code == record.esubgroup).first():
            print("""Valid Subgroup""")
            if metadata_result.is_sync_excluded:
                print("""Sync Excluded""")
                operation = SKIP
                status = SUCCESS
                message = f'Sync excluded'
            elif record.empstatus == "0":
                sap_status, sap_msg = access_revocation_sap_tscmsl(result['samAccountName'])
                if metadata_result.is_active == True:
                    print("""DISABLE TO AD""")
                    operation = DISABLE
                    status, attributes = disable_employee_tscmsl(result['samAccountName'])
                    if status:
                        metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
                        if metadata_result:
                            ad_change_log = ADChangeLogHistory()
                            
                            ad_change_log = map_ad_change_log_history_data(ad_change_log, metadata_result, file_id)
                            
                            oracle_session.add(ad_change_log)
                            oracle_session.commit()

                            metadata_result.expiry_date = datetime.strptime(attributes['accountExpires'], '%Y-%m-%d %H:%M:%S')
                            metadata_result.updated_at = datetime.now()
                            metadata_result.is_sync_excluded = False
                            metadata_result.is_manual_entry = False
                            metadata_result.is_active = False
                            oracle_session.commit()

                            status = SUCCESS
                            message = None
                    else:
                        print("""Disable Failed""")
                        status = FAILED
                        message = 'AD Failed'
                else:
                    print("""Already Disabled""")
                    operation = SKIP
                    status = SUCCESS
                    message = ALREADY_DISABLED.format(str(metadata_result.expiry_date)[:10])
            else:
                print("""UPDATE TO AD""")

                """Update UPN and Email"""
                # if not record.imailid:
                #     if not update_upn_tscmsl(record.perno,record.comp_code,result['userPrincipalName'],result['mail']):
                #         print('UPN Update Failed')
                #         upn_status = False
                #     else:
                #         upn_status = True
                #         record.imailid = result['mail']
                if not record.imailid:
                    record.imailid = result['mail']
                upn_status = False
                operation = UPDATED
                status, attributes = update_employee_tscmsl(result,record)
                if status:
                    metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
                    if metadata_result:
                        ad_change_log = ADChangeLogHistory()
                        ad_change_log = map_ad_change_log_history_data(ad_change_log, metadata_result, file_id)
                        oracle_session.add(ad_change_log)
                        oracle_session.commit()
                        metadata_result.user_id = attributes['samAccountName'].upper() if attributes.get('samAccountName') else None
                        metadata_result.person_no = attributes['EmployeeID'] if attributes.get('EmployeeID') else None
                        metadata_result.displayname = attributes['DisplayName'] if attributes.get('DisplayName') else None
                        metadata_result.division = attributes['division'] if attributes.get('division') else None
                        metadata_result.office = attributes['physicalDeliveryOfficeName'] if attributes.get('physicalDeliveryOfficeName') else None
                        metadata_result.department = attributes['department'] if attributes.get('department') else None
                        metadata_result.address = attributes['streetAddress'] if attributes.get('streetAddress') else None
                        metadata_result.notes = attributes['info'] if attributes.get('info') else None
                        if attributes.get('ou'):
                            metadata_result.ou = attributes['ou']
                        metadata_result.domain = configs.DOMAIN
                        metadata_result.upn = attributes['userPrincipalName'] if attributes.get('userPrincipalName') else None
                        metadata_result.email = attributes['mail'] if attributes.get('mail') else None
                        metadata_result.reporting_dn = attributes['manager'] if attributes.get('manager') else None
                        metadata_result.expiry_date = datetime.strptime(attributes['accountExpires'], '%Y-%m-%d %H:%M:%S')
                        metadata_result.updated_at = datetime.now()
                        metadata_result.is_manual_entry = False
                        metadata_result.is_active = True
                        metadata_result.is_sync_excluded = False
                        oracle_session.commit()

                        status = SUCCESS
                        message = None

                else:
                    print("""Update Failed""")
                    status = FAILED
                    message = AD_FAILED
            # else:
            #     print("""Invalid Subgroup""")
            #     operation = messages.SKIP
            #     status = messages.SUCCESS
            #     message = f'{messages.INVALID_SUBGROUP} - {record.esubgroup}'
        else:
            print("""INSERT TO AD""")
            operation = INSERTED
            is_valid = None
            # if record.incident_no is not None:
            #     is_valid = True
            # else:
            #     is_valid = oracle_session.query(ESubGroup).filter(ESubGroup.esubgroup_code == record.esubgroup, ESubGroup.esubgroup_type == True).first()
            if True:     # added testing ...
                if record.empstatus == "0":
                    if result and 'samAccountName' in result:
                        sap_status, sap_msg = access_revocation_sap_tscmsl(result['samAccountName'])
                    print("""Disabled at time of insert""")
                    operation = SKIP
                    status = SUCCESS
                    message = DISABLED_AT_TIME_OF_INSERT
                else:
                    status, attributes = insert_employee_tscmsl(record)
                    if status:
                        metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
                        is_present = True
                        if not metadata_result:
                            is_present = False
                            metadata_result = ADMetaData()
                        metadata_result.user_id = attributes['samAccountName'] if attributes.get('samAccountName') else None
                        metadata_result.person_no = attributes['EmployeeID'] if attributes.get('EmployeeID') else None
                        metadata_result.displayname = attributes['DisplayName'] if attributes.get('DisplayName') else None
                        metadata_result.division = attributes['division'] if attributes.get('division') else None
                        metadata_result.office = attributes['physicalDeliveryOfficeName'] if attributes.get('physicalDeliveryOfficeName') else None
                        metadata_result.department = attributes['department'] if attributes.get('department') else None
                        metadata_result.address = attributes['streetAddress'] if attributes.get('streetAddress') else None
                        metadata_result.notes = attributes['info'] if attributes.get('info') else None
                        metadata_result.ou = attributes['ou'] if attributes.get('ou') else None
                        metadata_result.domain = configs.DOMAIN
                        metadata_result.upn = attributes['userPrincipalName'] if attributes.get('userPrincipalName') else None
                        metadata_result.email = attributes['mail'] if attributes.get('mail') else None
                        metadata_result.reporting_dn = attributes['manager'] if attributes.get('manager') else None
                        metadata_result.expiry_date = datetime.strptime(attributes['accountExpires'], '%Y-%m-%d %H:%M:%S')
                        metadata_result.initial_password = encrypt_content_tscmsl(attributes['passwd'])
                        metadata_result.created_by = configs.CREATED_BY if record.created_by is None else record.created_by
                        metadata_result.created_at = datetime.now()
                        metadata_result.updated_at = datetime.now()
                        metadata_result.is_deleted = False
                        metadata_result.is_active = True
                        metadata_result.is_manual_entry = False
                        metadata_result.internet_access = True
                        metadata_result.is_external_email = False
                        metadata_result.mailbox_status = False
                        metadata_result.is_internal_migration = False
                        metadata_result.incident_no = record.incident_no
                        if is_present == False:
                            oracle_session.add(metadata_result)
                        oracle_session.commit()

                        status = SUCCESS
                        message = None

                        """Update UPN and Email"""
                        # if not record.imailid:
                        #     if not update_upn_tscmsl(record.perno,record.comp_code,attributes['userPrincipalName'],attributes['mail']):
                        #         print('UPN Update Failed')
                        #         upn_status = False
                        #     else:
                        #         upn_status = True
                        upn_status = False

                        if record.incident_no is None:
                            if not record.pers_area == 'ERCU':
                                imac_status = update_imac_logs(oracle_session,attributes['samAccountName'],record.reporting,record.ccodetxt,record.patxt,record.esubgroup)
                                if not imac_status:
                                    print("IMAC failed")
                    else:
                        print("""Insert Failed""")
                        status = FAILED
                        message = AD_FAILED
            else:
                print("""Invalid Subgroup""")
                operation = messages.SKIP
                status = messages.SUCCESS
                message = f'{messages.INVALID_SUBGROUP} - {record.esubgroup}'
    except Exception as error:
        print_error('sync_record_in_active_directory_tsctmsl',error)
        oracle_session.rollback()
        status = FAILED
        message = str(error)[:100]
    return operation, status, message, upn_status, sap_status, sap_msg

def bulk_update_upn_tscmsl(file_id, oracle_session):
    try:
        API_URL = configs.SAP_TSCMSL_UPDATE_UPN_EMAIL_URL

        requests_body = {**configs.REQUEST_BODY}
        requests_body['data'] = []
        file_row_log_result = oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.file_id==file_id).filter(ProcessRowLogs.ad_status=='success').filter((ProcessRowLogs.ad_operation == 'insert') | (ProcessRowLogs.ad_operation == 'update')).all()
        for file_row_log in file_row_log_result:
            ad_metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == file_row_log.perno).first()
            requests_body['data'].append({
                "employee_id" : ad_metadata_result.person_no,
                "company_code" : 760,
                "upn": ad_metadata_result.upn
            })
        
        request = requests.post(API_URL,headers=configs.HEADER,data=json.dumps(requests_body))

        if request.status_code == 200:
            request = json.loads(request.content)
            if 'message' in request and request['message'] and "Data updated successfully" in request['message']:
                return True
            else:
                print("bulk_update_upn_tscmsl response_object",request)
                return False
        else:
            print('bulk_update_upn_tscmsl response_code',request.status_code)
            return False
    except Exception as error:
        print_error("bulk_update_upn_tscmsl",error)
        return False
    
def update_imac_logs(oracle_session,user_id,reporting,company_text,pa_text,esubgroup_code):
    imac_log = IMAC_Logs()
    imac_log.imac_log_id = ("IMACLOG"+str(int(time.time_ns() * 10)))[:23]
    imac_log.user_id = user_id
    reporting_data = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == reporting).first()
    if reporting_data:
        imac_log.report_to = reporting_data.user_id
    else:
        reporting_data = get_ad_data_by_perno_tscmsl(reporting)
        print('manager details',reporting_data)
        if reporting_data:
            imac_log.report_to = reporting_data.get('samAccountName').upper()
            metadata_result = ADMetaData()
            metadata_result.user_id = reporting_data.get('samAccountName').upper()
            metadata_result.person_no = reporting_data.get('EmployeeID')
            metadata_result.displayname = reporting_data.get('DisplayName')
            metadata_result.division = reporting_data.get('division')
            metadata_result.office = reporting_data.get('physicalDeliveryOfficeName')
            metadata_result.department = reporting_data.get('department')
            metadata_result.address = reporting_data.get('streetAddress')
            metadata_result.notes = reporting_data.get('info')
            metadata_result.ou = reporting_data.get('ou')
            metadata_result.domain = configs.DOMAIN
            metadata_result.upn = reporting_data.get('userPrincipalName')
            metadata_result.email = reporting_data.get('mail')
            metadata_result.reporting_dn = reporting_data.get('manager')
            metadata_result.expiry_date = datetime.strptime(reporting_data['accountExpires'], '%Y-%m-%d %H:%M:%S')
            metadata_result.initial_password = None
            metadata_result.created_by = configs.CREATED_BY
            metadata_result.created_at = datetime.strptime(reporting_data['whenCreated'][:19], '%Y-%m-%d %H:%M:%S')
            metadata_result.updated_at = datetime.strptime(reporting_data['whenChanged'][:19], '%Y-%m-%d %H:%M:%S')
            metadata_result.is_deleted = False
            metadata_result.is_active = reporting_data.get('is_active')
            metadata_result.is_manual_entry = False
            metadata_result.internet_access = True
            metadata_result.is_external_email = False
            metadata_result.mailbox_status = True
            metadata_result.is_internal_migration = False
            oracle_session.add(metadata_result)
            oracle_session.commit()
        else:
            print('Imac - reporting not found')
            return False
    imac_log.imac_status = PENDING
    imac_log.imac_comments = NOT_RAISED
    # papsa_imac_mapping = oracle_session.query(PaPsa).filter(PaPsa.pa_text == pa_text).first()
    # if papsa_imac_mapping:
    #     imac_log.imac_location = papsa_imac_mapping.imac_location
    # else:
    #     print('Imac - imac location not found')
    #     return False
    papsa_entry = (
        oracle_session.query(Psa)
        .join(Pa, Psa.pa_id == Pa.id)
        .filter(Pa.pa_text == pa_text)
        .first()
    )

    if iloc_name := getattr(papsa_entry.imac_location, 'iloc_name', None):
        imac_log.imac_location = iloc_name
    else:
        print('Imac - PSA or imac location not found')
        return False
    imac_log.imac_company_text = company_text
    imac_log.created_at = datetime.now()
    imac_log.updated_at = datetime.now()
    esubgroup_details = oracle_session.query(ESubGroup).filter(ESubGroup.esubgroup_code == esubgroup_code).first()
    if esubgroup_code:
        imac_log.asset_type = esubgroup_details.asset_type
    else:
        return False
    imac_log.is_deleted = False
    oracle_session.add(imac_log)
    oracle_session.commit()
    return True