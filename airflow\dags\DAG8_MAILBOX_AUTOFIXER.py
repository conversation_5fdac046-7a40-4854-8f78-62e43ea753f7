# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.configs import DAG8_MAILBOX_AUTOFIXER_JOBS, DAG8_MAILBOX_AUTOFIXER_SCHEDULER, CHECK_CONNECTION_MAILBOX, TRIGGER_MAILBOX_AUTOFIXER_API
from scripts.mailbox_autofixer_job import  check_telnet_connection, trigger_autofixer_mailbox_API


# DAG initialization...
with DAG(
    dag_id = DAG8_MAILBOX_AUTOFIXER_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG8_MAILBOX_AUTOFIXER_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Check mailbox connection...
    connection_cmd_exec = PythonOperator(
        task_id = CHECK_CONNECTION_MAILBOX,
        python_callable = check_telnet_connection,
        do_xcom_push = True
    )

    # Task 2 trigger Mailbox autofixer API...
    trigger_mailbox_autofix_API = PythonOperator( 
        task_id = TRIGGER_MAILBOX_AUTOFIXER_API, 
        python_callable = trigger_autofixer_mailbox_API, 
        do_xcom_push = True
    )

    # Task sequencing & call...
    connection_cmd_exec>>trigger_mailbox_autofix_API