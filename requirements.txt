aiohttp==3.8.5
aiosignal==1.3.1
alembic==1.11.1
amqp==5.1.1
anyio==3.7.1
apache-airflow==2.7.1
apache-airflow-providers-celery==3.3.3
apache-airflow-providers-common-sql==1.6.0
apache-airflow-providers-ftp==3.4.2
apache-airflow-providers-http==4.5.0
apache-airflow-providers-imap==3.3.0
apache-airflow-providers-microsoft-mssql==3.4.1
apache-airflow-providers-oracle==3.7.1
apache-airflow-providers-postgres==5.5.2
apache-airflow-providers-redis==3.2.1
apache-airflow-providers-sftp==4.4.0
apache-airflow-providers-sqlite==3.4.2
apache-airflow-providers-ssh==3.7.1
apispec==6.3.0
argcomplete==3.1.1
asgiref==3.7.2
async-timeout==4.0.2
attrs==23.1.0
Babel==2.12.1
bcrypt==4.0.1
billiard==4.1.0
blinker==1.6.2
cachelib==0.9.0
cattrs==23.1.2
celery==5.3.1
certifi==2023.5.7
cffi==1.15.1
charset-normalizer==3.2.0
click==8.1.5
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
clickclick==20.10.2
colorama==0.4.6
colorlog==4.8.0
ConfigUpdater==3.1.1
connexion==2.14.2
cron-descriptor==1.4.0
croniter==1.4.1
cryptography==41.0.4
cx-Oracle==8.3.0  
Deprecated==1.2.14
dill==0.3.6
dnspython==2.4.0
docutils==0.20.1
email-validator==1.3.1
exceptiongroup==1.1.2
Flask==2.2.5
Flask-AppBuilder==4.3.6
Flask-Babel==2.0.0
Flask-Caching==2.0.2
Flask-JWT-Extended==4.5.2
Flask-Login==0.6.2
Flask-Session==0.5.0
Flask-SQLAlchemy==2.5.1
Flask-WTF==1.1.1
flower==2.0.0
frozenlist==1.4.0
graphviz==0.20.1
greenlet==2.0.2
gunicorn==20.1.0
h11==0.14.0
httpcore==0.17.3
httpx==0.24.1
humanize==4.7.0
idna==3.4
importlib-metadata==6.8.0
inflection==0.5.1
itsdangerous==2.1.2
Jinja2==3.1.2
jsonschema==4.18.3
jsonschema-specifications==2023.6.1
kombu==5.3.1
lazy-object-proxy==1.9.0
linkify-it-py==2.0.2
lockfile==0.12.2
Mako==1.2.4
Markdown==3.4.3
markdown-it-py==3.0.0
MarkupSafe==2.1.3
marshmallow==3.19.0
marshmallow-enum==1.5.1
marshmallow-oneofschema==3.0.1
marshmallow-sqlalchemy==0.26.1
mdit-py-plugins==0.4.0
mdurl==0.1.2
multidict==6.0.4
numpy==1.25.1
oracledb==1.3.2
packaging==23.1
pandas==2.0.3
paramiko==3.2.0
pathspec==0.9.0
pendulum==2.1.2
pluggy==1.2.0
prison==0.2.1
prometheus-client==0.17.1
prompt-toolkit==3.0.39
psutil==5.9.5
psycopg2-binary==2.9.6
pycparser==2.21
pycryptodome==3.14.1
Pygments==2.15.1
PyJWT==2.7.0
pymssql==2.2.7
pyodbc==4.0.39
PyNaCl==1.5.0
python-daemon==3.0.1
python-dateutil==2.8.2
python-nvd3==0.15.0
python-slugify==8.0.1
pytz==2023.3
pytzdata==2020.1
PyYAML==6.0
redis==4.6.0
referencing==0.29.1
requests==2.31.0
requests-toolbelt==1.0.0
rich==13.4.2
rpds-py==0.8.10
setproctitle==1.3.2
six==1.16.0
sniffio==1.3.0
SQLAlchemy==1.4.49
SQLAlchemy-JSONField==1.0.1.post0
SQLAlchemy-Utils==0.41.1
sqlparse==0.4.4
sshtunnel==0.4.0
swagger-ui-bundle==0.0.9
tabulate==0.9.0
tenacity==8.2.2
termcolor==2.3.0
text-unidecode==1.3
tornado==6.3.3
typing_extensions==4.7.1
tzdata==2023.3
uc-micro-py==1.0.2
unicodecsv==0.14.1
urllib3==2.0.6
vine==5.0.0
wcwidth==0.2.6
Werkzeug==2.2.3
wrapt==1.15.0
WTForms==3.0.1
yarl==1.9.2
zipp==3.16.2


apache-airflow[celery,redis]==2.7.1
# psycopg2
apache-airflow-providers-postgres
apache-airflow-providers-sftp
apache-airflow-providers-microsoft-mssql
apache-airflow-providers-oracle
pandas
requests
pycryptodome==3.14.1
cx-Oracle
telnetlib3==2.0.4
