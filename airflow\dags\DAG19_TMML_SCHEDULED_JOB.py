# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.models_v2 import TMMLTempTable
from commons.configs import  DAG19_TMML_SCHEDULED_JOBS, DAG19_TMML_SAP_AUTOSYNC_SCHEDULER, LOAD_TO_TEMP, TMML, TMML_CO_CODE, TMML_SFTP_ARCHIVED_PATH, TMML_SFTP_CONN_ID, TMML_SFTP_ORIGINAL_PATH, AD, AWS, AZURE, BLUECOLLAR, EDP, LOAD_TO_AD, LOAD_TO_AWS_CET, LOAD_TO_AZURE_CET, LOAD_TO_BLUECOLLAR_KEYCLOACK, LOAD_TO_EDP_CET, LOAD_TO_NEXTGEN_CET, LOAD_TO_SAP, LOAD_TO_TEMP, NEXTGEN, SAP, UPDATE_UPN_AND_SEND_EMAIL
from scripts.scheduled_job import schedule_load_to_cet, update_upn_and_send_email
from scripts.parallel_schedule_job import load_to_temp_table

# DAG initialization...
with DAG(
    dag_id = DAG19_TMML_SCHEDULED_JOBS,
    start_date = datetime(2024, 9, 5),
    schedule_interval = DAG19_TMML_SAP_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    load_to_temp_ = PythonOperator(
        task_id=LOAD_TO_TEMP,
        python_callable=load_to_temp_table,
        do_xcom_push=True,
        op_kwargs={
            "sftp_connection_id": TMML_SFTP_CONN_ID,
            "sftp_path": TMML_SFTP_ORIGINAL_PATH,
            "sftp_archived_path": TMML_SFTP_ARCHIVED_PATH,
            "temp_table": TMMLTempTable,
            "job_name":TMML
        },
        dag=dag,
    )
    
    # Task 2 Initialization...
    load_to_sap = PythonOperator(
        task_id = LOAD_TO_SAP,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMMLTempTable, "cet": SAP, "comp_code" : TMML_CO_CODE},
        do_xcom_push = True    
    )
    
    load_to_ad = PythonOperator(
        task_id = LOAD_TO_AD,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMMLTempTable, "cet": AD, "comp_code" : TMML_CO_CODE},
        do_xcom_push = True    
    )
    
    
    # Task 2 Initialization...
    load_to_azure_cet = PythonOperator(
        task_id = LOAD_TO_AZURE_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMMLTempTable, "cet": AZURE, "comp_code" : TMML_CO_CODE},
        do_xcom_push = True    
    )
    
    load_to_edp_cet = PythonOperator(
        task_id = LOAD_TO_EDP_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMMLTempTable, "cet": EDP, "comp_code" : TMML_CO_CODE},
        do_xcom_push = True    
    )
    
    load_to_nextgen_cet = PythonOperator(
        task_id = LOAD_TO_NEXTGEN_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMMLTempTable, "cet": NEXTGEN, "comp_code" : TMML_CO_CODE},
        do_xcom_push = True    
    )
    
    load_to_blue_collar_keycloak = PythonOperator(
        task_id = LOAD_TO_BLUECOLLAR_KEYCLOACK,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMMLTempTable, "cet": BLUECOLLAR, "comp_code" : TMML_CO_CODE},
        do_xcom_push = True    
    )
    
    update_upn_and_send_email_task = PythonOperator(
        task_id = UPDATE_UPN_AND_SEND_EMAIL,
        python_callable = update_upn_and_send_email,
        do_xcom_push = True    
    )
    

    # Task sequencing & call...
    load_to_temp_ >> load_to_sap >> [load_to_ad, load_to_azure_cet, load_to_edp_cet, load_to_nextgen_cet, load_to_blue_collar_keycloak] >> update_upn_and_send_email_task