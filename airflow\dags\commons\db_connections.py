from threading import Lock
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.hooks.postgres_hook import PostgresHook
from sqlalchemy.exc import SQLAlchemyError, OperationalError

from commons.configs import EDP_CONN_ID, EDP_SCHEMA_NAME, MSSQL_CONN_ID, MSSQL_SCHEMA_NAME, AWS_USERNAME, AWS_PASSWORD, AWS_SERVER, AWS_DATABASE, AWS_DRIVER , AZURE_CONN_ID, AZURE_DATABASE, AZURE_DRIVER, AZURE_PASSWORD, AZURE_SCHEMA_NAME, AZURE_SERVER, AZURE_USERNAME, EDP_BC_CV, EDP_BC_PV, EDP_BC_EV, EDP_BC_SCHEMA_NAME, NEW_ORACLE_CONN_ID, NEXTGEN_CONN_ID, ORACLE_CONN_ID
from commons.common_functions import print_error

class DatabaseSessionManager:
    _instances = {}
    _lock = Lock()

    def __new__(cls, db_type, db_name, *args, **kwargs):
        # Ensures a single session instance per database type and name.
        with cls._lock:
            key = f"{db_type}_{db_name}"
            if key not in cls._instances:
                instance = super(DatabaseSessionManager, cls).__new__(cls)
                instance._db_type = db_type
                instance._db_name = db_name
                instance._session = None
                cls._instances[key] = instance
        return cls._instances[key]

    def create_session(self, engine):
        # Creates a session using the provided engine.
        try:
            if not self._session:
                Session = sessionmaker(bind=engine)
                self._session = Session()
            return self._session
        except SQLAlchemyError as err:
            print_error(f"Failed to create session for {self._db_name}: {err}")
            raise err
        
    def create_session_edp(self, engine):
        try:
            Session = sessionmaker(bind=engine)
            return Session()  # Always return a new session
        except SQLAlchemyError as err:
            print_error(f"Failed to create session for {self._db_name}: {err}")
            raise err
    
    def create_oracle_engine(self, connection_details):
        # Creates an SQLAlchemy engine for Oracle.
        try:
            hook = OracleHook(oracle_conn_id=connection_details['conn_id'])
            return hook.get_sqlalchemy_engine()
        except Exception as err:
            print_error(f"Failed to create Oracle engine: {err}")
            raise err
    
    def create_postgres_engine(self, connection_details):
        # Creates an SQLAlchemy engine for Postgres.
        hook = PostgresHook(postgres_conn_id = connection_details['conn_id'], schema = connection_details['schema'])
        return hook.get_sqlalchemy_engine()

    def create_mssql_engine(self, connection_details):
        # Creates an SQLAlchemy engine for MSSQL.
        try:
            connection_string = (
                f"mssql+pyodbc://{connection_details['username']}:"
                f"{connection_details['password']}@{connection_details['server']}/"
                f"{connection_details['database']}?driver={connection_details['driver']}"
            )
            return create_engine(connection_string)
        except Exception as err:
            print_error(f"Failed to create MSSQL engine: {err}")
            raise err
    
    def close_session(self):
        # Closes the database session.
        try:
            if self._session:
                self._session.close()
                self._session = None
        except SQLAlchemyError as err:
            print_error(f"Failed to close session for {self._db_name}: {err}")
            raise err
    
    @classmethod
    def close_all_sessions(cls):
        # Closes all database sessions.
        with cls._lock:
            for instance in cls._instances.values():
                instance.close_session()
            cls._instances.clear()


# Engine creation
def create_oracle_session():
    try:
        oracle_details = {"conn_id": ORACLE_CONN_ID}
        oracle_manager = DatabaseSessionManager("oracle", ORACLE_CONN_ID)
        engine = oracle_manager.create_oracle_engine(oracle_details)
        return oracle_manager.create_session(engine)
    except Exception as err:
        print_error(f"Failed to create Oracle session: {err}")
        raise
    
def create_new_oracle_session():
    try:
        oracle_details = {"conn_id": NEW_ORACLE_CONN_ID}
        oracle_manager = DatabaseSessionManager("oracle", NEW_ORACLE_CONN_ID)
        engine = oracle_manager.create_oracle_engine(oracle_details)
        return oracle_manager.create_session(engine)
    except Exception as err:
        print_error(f"Failed to create Oracle session: {err}")
        raise
    

def create_EDP_postgres_session():
    try:
        postgres_details = {"conn_id": EDP_CONN_ID, "schema": EDP_SCHEMA_NAME}
        postgres_manager = DatabaseSessionManager("postgres", EDP_SCHEMA_NAME)
        engine = postgres_manager.create_postgres_engine(postgres_details)
        return postgres_manager.create_session(engine)
    except Exception as err:
        print_error(f"Failed to create Oracle session: {err}")
        raise
    
def create_NEXTGEN_oracle_session():
    try:
        oracle_details = {"conn_id": NEXTGEN_CONN_ID}
        oracle_manager = DatabaseSessionManager("oracle", NEXTGEN_CONN_ID)
        engine = oracle_manager.create_oracle_engine(oracle_details)
        return oracle_manager.create_session(engine)
    except Exception as error:
        print_error('sync_record_in_active_directory',error)
        raise Exception(error)

def create_AWS_mssql_session():
    try:
        mssql_details = {
            "username": AWS_USERNAME,
            "password": AWS_PASSWORD,
            "server": AWS_SERVER,
            "database": AWS_DATABASE,
            "driver": AWS_DRIVER,
        }
        mssql_manager = DatabaseSessionManager("mssql", AWS_DATABASE)
        engine = mssql_manager.create_mssql_engine(mssql_details)
        return mssql_manager.create_session(engine)
    except Exception as err:
        print_error("create_AWS_mssql_session()",f"Failed to create AWS MSSQL session: {err}")
        raise

def create_AZURE_mssql_session():
    try:
        mssql_details = {
            "username": AZURE_USERNAME,
            "password": AZURE_PASSWORD,
            "server": AZURE_SERVER,
            "database": AZURE_DATABASE,
            "driver": AZURE_DRIVER,
        }
        mssql_manager = DatabaseSessionManager("mssql", AZURE_DATABASE)
        engine = mssql_manager.create_mssql_engine(mssql_details)
        return mssql_manager.create_session(engine)
    except Exception as err:
        print_error("create_AZURE_mssql_session()" ,f"Failed to create AZURE MSSQL session: {err}")
        raise
    
def create_EDP_BC_CV_postgres_session():
    try:
        postgres_details = {"conn_id": EDP_BC_CV, "schema": EDP_BC_SCHEMA_NAME}
        postgres_manager = DatabaseSessionManager("postgres", EDP_BC_SCHEMA_NAME)
        engine = postgres_manager.create_postgres_engine(postgres_details)
        return postgres_manager.create_session_edp(engine)
    
    except Exception as error:
        print_error('create_EDP_BC_CV_postgres_session',error)
        raise Exception(error)
    
def create_EDP_BC_PV_postgres_session():
    try:
        postgres_details = {"conn_id": EDP_BC_PV, "schema": EDP_BC_SCHEMA_NAME}
        postgres_manager = DatabaseSessionManager("postgres", EDP_BC_SCHEMA_NAME)
        engine = postgres_manager.create_postgres_engine(postgres_details)
        return postgres_manager.create_session_edp(engine)
    
    except Exception as error:
        print_error('create_EDP_BC_PV_postgres_session',error)
        raise Exception(error)
    
def create_EDP_BC_EV_postgres_session():
    try:
        postgres_details = {"conn_id": EDP_BC_EV, "schema": EDP_BC_SCHEMA_NAME}
        postgres_manager = DatabaseSessionManager("postgres", EDP_BC_SCHEMA_NAME)
        engine = postgres_manager.create_postgres_engine(postgres_details)
        return postgres_manager.create_session_edp(engine)
    
    except Exception as error:
        print_error('create_EDP_BC_EV_postgres_session',error)
        raise Exception(error)

def close_all_connections():
    # Close all sessions at the end of the job
    DatabaseSessionManager.close_all_sessions()
    print("All sessions closed.")



# Session cache dictionary
SESSION_CACHE = {}

def is_session_alive(session):
    try:
        # Try a lightweight query to ensure session is alive
        session.execute('SELECT 1')
        return True
    except OperationalError:
        return False
    except Exception:
        return False

def recreate_session(comp_code):
    if comp_code == '100':
        return create_EDP_BC_CV_postgres_session()
    elif comp_code == '550':
        return create_EDP_BC_PV_postgres_session()
    elif comp_code == '650':
        return create_EDP_BC_EV_postgres_session()
    else:
        raise ValueError(f"Unsupported company code: {comp_code}")

def get_postgres_session(comp_code):
    session = SESSION_CACHE.get(comp_code)

    # Validate session; recreate if needed
    if session is None or not is_session_alive(session):
        SESSION_CACHE[comp_code] = recreate_session(comp_code)
    print("SESSION_CACHE", SESSION_CACHE)
    return SESSION_CACHE[comp_code]
