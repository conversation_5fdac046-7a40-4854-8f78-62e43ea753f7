# In-built imports...
import json
import requests
# Custom imports...
from commons.configs import NDA_HEADER, PARTNER_ONBOARDING_DELETE_DOMAIN_ID_URL, PARTNER_ONBOARDING_DELETE_DOMAIN_ID_URL_V2, PARTNER_ONBOARDING_DISABLE_DOMAIN_ID_URL, PARTNER_ONBOARDING_DISABLE_DOMAIN_ID_URL_V2, PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DELETE_URL, PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DISABLE_URL, PARTNER_ONBOARDING_GET_TML_MANAGER_TO_SEND_DOMAIN_ID_DISABLE_REMINDER_URL, PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_TO_EMPLOYEE_URL, PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_URL, PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_URL_V2, PARTNER_ONBOARDING_SEND_EMAILTO_MANAGER_SPOC_BEFORE_DISABLE_DOMAIN_ID_URL, PARTNER_ONBOARDING_SYNC_DOMAIN_IDS
from commons.common_functions import print_error

def nda_disable_domain_id():
    try:
        API_URL = PARTNER_ONBOARDING_DISABLE_DOMAIN_ID_URL

        request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1:
                print("nda_disable_domain_id response_object",request)
                return True
            else:
                print("nda_disable_domain_id response_object",request)
                return False
        else:
            print('nda_disable_domain_id response_code',request.status_code)
            return False
    except Exception as error:
        print_error("nda_disable_domain_id",error)
        return False
    
    
def nda_delete_domain_id():
    try:
        API_URL = PARTNER_ONBOARDING_DELETE_DOMAIN_ID_URL

        request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1:
                print("nda_delete_domain_id response_object",request)
                return True
            else:
                print("nda_delete_domain_id response_object",request)
                return False
        else:
            print('nda_delete_domain_id response_code',request.status_code)
            return False
    except Exception as error:
        print_error("nda_delete_domain_id",error)
        return False
    
def nda_send_email_before_disable_domain_id():
    try:
        API_URL = PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_URL

        request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1:
                print("nda_send_email_before_disable_domain_id response_object",request)
                return True
            else:
                print("nda_send_email_before_disable_domain_id response_object",request)
                return False
        else:
            print('nda_send_email_before_disable_domain_id response_code',request.status_code)
            return False
    except Exception as error:
        print_error("nda_send_email_before_disable_domain_id",error)
        return False
    
def nda_disable_domain_id_v2():
    try:
        API_URL = PARTNER_ONBOARDING_DISABLE_DOMAIN_ID_URL_V2
        GET_DOMAIN_ID_API = PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DISABLE_URL

        request = requests.post(GET_DOMAIN_ID_API,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1 and 'data' in request:
                print("nda_disable_domain_ids request['data']", request['data'])
                for data in request['data']:
                    disable_request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({'domainId':data['domainId']}),verify=False)
                    if disable_request.status_code == 200:
                        disable_request = json.loads(disable_request.content)
                        if disable_request['status'] == 1:
                            print("nda_disable_domain_id response_object",disable_request)
                        else:
                            print("nda_disable_domain_id response_object",disable_request)
                    else:
                        print('nda_disable_domain_id response_code',disable_request.status_code)
                return True
            else:
                print("nda_disable_domain_id response_object",request)
                return False
        else:
            print('nda_disable_domain_id response_code',request.status_code)
            return False
    except Exception as error:
        print_error("nda_disable_domain_id",error)
        return False
    
    
def nda_delete_domain_id_v2():
    try:
        GET_DOMAIN_ID_API = PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DELETE_URL
        API_URL = PARTNER_ONBOARDING_DELETE_DOMAIN_ID_URL_V2

        request = requests.post(GET_DOMAIN_ID_API,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1 and 'data' in request:
                print("nda_delete_domain_ids request['data']", request['data'])
                for data in request['data']:
                    delete_request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({'domainId':data['domainId']}),verify=False)
                    if delete_request.status_code == 200:
                        delete_request = json.loads(delete_request.content)
                        if delete_request['status'] == 1:
                            print("nda_delete_domain_id response_object",delete_request)
                        else:
                            print("nda_delete_domain_id response_object",delete_request)
                    else:
                        print('nda_delete_domain_id response_code',delete_request.status_code)
                return True
            else:
                print("nda_delete_domain_id response_object",request)
                return False
        else:
            print('nda_delete_domain_id response_code',request.status_code)
            return False
    except Exception as error:
        print_error("nda_delete_domain_id",error)
        return False

def nda_send_email_before_disable_domain_id_v2():
    try:
        API_URL = PARTNER_ONBOARDING_SEND_EMAILTO_MANAGER_SPOC_BEFORE_DISABLE_DOMAIN_ID_URL
        GET_DOMAIN_ID_API_URL= PARTNER_ONBOARDING_GET_TML_MANAGER_TO_SEND_DOMAIN_ID_DISABLE_REMINDER_URL

        request = requests.post(GET_DOMAIN_ID_API_URL,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1 and 'data' in request:
                print("nda_send_email_before_disable_domain_id request['data']", request['data'])
                for data in request['data']:
                    reminder_request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps(data),verify=False)
                    if reminder_request.status_code == 200:
                        reminder_request = json.loads(reminder_request.content)
                        if reminder_request['status'] == 1:
                            print("nda_send_email_before_disable_domain_id response_object",reminder_request)
                        else:
                            print("nda_send_email_before_disable_domain_id response_object",reminder_request)
                    else:
                        print('nda_send_email_before_disable_domain_id response_code',reminder_request.status_code)
                return True
            else:
                print("nda_send_email_before_disable_domain_id response_object",request)
                return False
        else:
            print('nda_send_email_before_disable_domain_id response_code',request.status_code)
            return False
    except Exception as error:
        print_error("nda_send_email_before_disable_domain_id",error)
        return False
    
def nda_send_email_before_disable_domain_id_to_employee():
    try:
        API_URL = PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_TO_EMPLOYEE_URL
        GET_DOMAIN_ID_API_URL= PARTNER_ONBOARDING_GET_TML_MANAGER_TO_SEND_DOMAIN_ID_DISABLE_REMINDER_URL

        request = requests.post(GET_DOMAIN_ID_API_URL,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1 and 'data' in request:
                print("nda_send_email_before_disable_domain_id_to_employee request['data']", request['data'])
                domainIDLists = []
                for entry in request['data']:
                    domainIDLists.extend(entry["domainIds"])
                domainIDLists = list(set(domainIDLists))
                for domainId in domainIDLists:
                    reminder_request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({'domainId':domainId}),verify=False)
                    if reminder_request.status_code == 200:
                        reminder_request = json.loads(reminder_request.content)
                        if reminder_request['status'] == 1:
                            print("nda_send_email_before_disable_domain_id_to_employee response_object",reminder_request)
                        else:
                            print("nda_send_email_before_disable_domain_id_to_employee response_object",reminder_request)
                    else:
                        print('nda_send_email_before_disable_domain_id_to_employee response_code',reminder_request.status_code)
                return True
            else:
                print("nda_send_email_before_disable_domain_id_to_employee response_object",request)
                return False
        else:
            print('nda_send_email_before_disable_domain_id_to_employee response_code',request.status_code)
            return False
    except Exception as error:
        print_error("nda_send_email_before_disable_domain_id_to_employee",error)
        return False
    
def sync_before_send_email():
    try:
        API_URL = PARTNER_ONBOARDING_SYNC_DOMAIN_IDS
        GET_DOMAIN_ID_API_URL= PARTNER_ONBOARDING_GET_TML_MANAGER_TO_SEND_DOMAIN_ID_DISABLE_REMINDER_URL

        request = requests.post(GET_DOMAIN_ID_API_URL,headers=NDA_HEADER,data=json.dumps({}),verify=False)

        if request.status_code == 200:
            request = json.loads(request.content)
            if request['status'] == 1 and 'data' in request:
                print("nda_send_email_before_disable_domain_id request['data']", request['data'])
                domain_id_list = [domain_id for data in request['data'] for domain_id in data['domainIds']]
                domain_id_list = list(set(domain_id_list))
                for data in domain_id_list:
                    sync_request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({'domainId':[data]}),verify=False)
                    if sync_request.status_code == 200:
                        sync_request = json.loads(sync_request.content)
                        if sync_request['status'] == 1:
                            print("sync_disable_domain_id response_object",sync_request)
                        else:
                            print("sync_disable_domain_id response_object",sync_request)
                    else:
                        print('sync_disable_domain_id response_code',sync_request.status_code)
                return True
            else:
                print("nda_send_email_before_disable_domain_id response_object",request)
                return False
        else:
            print('nda_send_email_before_disable_domain_id response_code',request.status_code)
            return False

    except Exception as error:
        print("sync_before_send_email", error)
        return False
    
def sync_before_disable_delete_email():
    try:
        API_URL = PARTNER_ONBOARDING_SYNC_DOMAIN_IDS
        GET_DISABLE_DOMAIN_ID_API = PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DISABLE_URL
        GET_DELETE_DOMAIN_ID_API = PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DELETE_URL
        request_disable = requests.post(GET_DISABLE_DOMAIN_ID_API,headers=NDA_HEADER,data=json.dumps({}),verify=False)
        request_delete = requests.post(GET_DELETE_DOMAIN_ID_API,headers=NDA_HEADER,data=json.dumps({}),verify=False)
        domain_ids_list = []
        if request_disable.status_code == 200:
            request_disable = json.loads(request_disable.content)
            if request_disable['status'] == 1 and 'data' in request_disable:
                print("nda_disable_domain_ids request['data']", request_disable['data'])
                domain_ids_list = [data['domainId'] for data in request_disable['data']]
                domain_ids_list = list(set(domain_ids_list))
            else:
                print("nda_disable_domain_id response_object",request_disable)
        else:
            print('nda_disable_domain_id response_code',request_disable.status_code)
        
        if request_delete.status_code == 200:
            request_delete = json.loads(request_delete.content)
            if request_delete['status'] == 1 and 'data' in request_delete:
                print("nda_delete_domain_ids request['data']", request_delete['data'])
                for data in request_delete['data']:
                    domain_ids_list.append(data['domainId'])
                domain_ids_list = list(set(domain_ids_list))
            else:
                print("nda_delete_domain_id response_object",request_delete)
        else:
            print('nda_delete_domain_id response_code',request_delete.status_code)
        
        for data in domain_ids_list:
            sync_request = requests.post(API_URL,headers=NDA_HEADER,data=json.dumps({'domainId':[data]}),verify=False)
            if sync_request.status_code == 200:
                sync_request = json.loads(sync_request.content)
                if sync_request['status'] == 1:
                    print("sync_disable_domain_id response_object",sync_request)
                else:
                    print("sync_disable_domain_id response_object",sync_request)
            else:
                print('sync_disable_domain_id response_code',sync_request.status_code)
    except Exception as error:
        print("sync_before_disable_delete_domain", error)
        return False