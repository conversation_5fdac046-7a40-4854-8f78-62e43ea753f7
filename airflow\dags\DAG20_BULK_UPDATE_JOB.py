# In-built imports...
import requests
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.configs import DAG20_BULK_UPDATE_JOB, DAG20_BULK_UPDATE_SCHEDULER
from scripts.bulk_update_job import fetch_and_push_details


with DAG(
    dag_id = DAG20_BULK_UPDATE_JOB,
    start_date = datetime(2024, 10, 16),
    schedule_interval = DAG20_BULK_UPDATE_SCHEDULER,
    catchup = False,
) as dag:
    
    fetch_and_push_details_task = PythonOperator(
        task_id = 'fetch_and_push_details_task',
        python_callable = fetch_and_push_details
    )

fetch_and_push_details_task