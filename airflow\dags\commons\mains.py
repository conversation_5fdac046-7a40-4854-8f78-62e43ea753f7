# In-built imports...
# Custom imports...
from commons import db_connections
from commons.configs import AD, AWS, AZURE, BLUECOLLAR, EDP, NEXTGEN, SAP, TMML_CO_CODE, TMML_CO_CODE_0, TMM<PERSON>_CO_DEP_CODE,TMM<PERSON>_CO_DEP_CODE_0
from operations.nextgen_cet_operations import update_to_nextgen_cet
from operations.azure_cet_tscmsl_operations import update_to_azure_cet_tscmsl
from commons.messages import FAILED, INSERTED, RESOLVED, SUCCESS, UPDATED, SKIP
from commons.common_functions import update_file_row_log, validate_fields
from commons.common_functions_tscmsl import insert_sap_file_row, update_file_row_log_tscmsl, validate_fields_tscmsl
from operations.edp_cet_operations import update_to_edp_cet
from operations.aws_cet_operations import update_to_aws_cet
from operations.aws_cet_tscmsl_operations import update_to_aws_cet_tscmsl
from operations.active_directory_tscmsl_operations import sync_record_in_active_directory_tsctmsl
from operations.active_directory_operations import get_ad_data_by_perno, sync_record_in_active_directory
from operations.sap_operations import sync_record_in_sap_cet
from operations.azure_cet_operations import update_to_azure_cet
from operations.keycloak_operations import sync_records_in_keycloak
# from commons.models import ProcessRowLogs
from commons.models_v2 import ADMetaData, AWSEmployeeData, ProcessRowLogs
mssql_azure_session = db_connections.create_AZURE_mssql_session()
# mssql_aws_session = db_connections.create_AWS_mssql_session()
def perform_operations(oracle_session, item, file_id, file_row_log, cet):
    # print(f'-----------###########----START--{item.perno}--START---------##############-------------')
        if cet==SAP:
            sap_employee = sync_record_in_sap_cet(
                oracle_session, 
                item, 
                file_id
            )
        
        # print("""---------------------Active Directory----------------------""")
        if cet==AD and item.comp_code not in ['TTL']:
            operation, status, message, upn_status = sync_record_in_active_directory(
                    oracle_session = oracle_session,
                    record = item,
                    file_id = file_id,
                )
            file_row_log = update_file_row_log(
                    oracle_session,
                    file_row_log = file_row_log,
                    ad_status = status,
                    ad_operation = operation,
                    ad_reason = message,
                    is_upn_update = upn_status,
                    commit = True
                )
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno == item.perno, ProcessRowLogs.ad_status == FAILED).update({ProcessRowLogs.ad_status : RESOLVED, ProcessRowLogs.resolved_file_id : file_row_log.file_id, ProcessRowLogs.resolved_file_row_id : file_row_log.file_row_id})
                oracle_session.commit()
        
        # print("""-----------------------AWS CET------------------------------""")
        if cet==AWS and item.comp_code not in [TMML_CO_DEP_CODE, TMML_CO_DEP_CODE_0]:
            operation, status, message = update_to_aws_cet(
                oracle_session = oracle_session,
                row = item,
                file_id = file_id
            )
            file_row_log = update_file_row_log(
                oracle_session,
                file_row_log = file_row_log,
                aws_cet_status = status,
                aws_cet_operation = operation,
                aws_cet_reason = message,
                commit = True
            )
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno == item.perno, ProcessRowLogs.aws_cet_status == FAILED).update({ProcessRowLogs.aws_cet_status : RESOLVED, ProcessRowLogs.resolved_file_id : file_row_log.file_id, ProcessRowLogs.resolved_file_row_id : file_row_log.file_row_id})
                oracle_session.commit()

            # print("""-----------------------AZURE CET------------------------------""")
        if cet==AZURE and item.comp_code not in [TMML_CO_DEP_CODE, TMML_CO_DEP_CODE_0]:
            operation, status, message = update_to_azure_cet(
                oracle_session = oracle_session,
                row = item,
                file_id = file_id
            )
            # poi_operation, poi_status, poi_message = update_email_in_POI(
            #     row = item
            # )
            file_row_log = update_file_row_log(
                oracle_session,
                file_row_log = file_row_log,
                azure_cet_status = status,
                azure_cet_operation = operation,
                azure_cet_reason = message,
                commit = True
            )
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno == item.perno, ProcessRowLogs.azure_cet_status == FAILED).update({ProcessRowLogs.azure_cet_status : RESOLVED, ProcessRowLogs.resolved_file_id : file_row_log.file_id, ProcessRowLogs.resolved_file_row_id : file_row_log.file_row_id})
                oracle_session.commit()
        
        # print("""-----------------------EDP CET------------------------------""")
        if cet==EDP and item.comp_code not in ['TTL', TMML_CO_CODE, TMML_CO_CODE_0, TMML_CO_DEP_CODE, TMML_CO_DEP_CODE_0]:
            operation, status, message = update_to_edp_cet(
                oracle_session = oracle_session,
                row = item,
                file_id = file_id
            )
            file_row_log = update_file_row_log(
                    oracle_session,
                    file_row_log = file_row_log,
                    edp_status = status,
                    edp_operation = operation,
                    edp_reason = message,
                    commit = True
                )
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno == item.perno, ProcessRowLogs.edp_status == FAILED).update({ProcessRowLogs.edp_status : RESOLVED, ProcessRowLogs.resolved_file_id : file_row_log.file_id, ProcessRowLogs.resolved_file_row_id : file_row_log.file_row_id})
                oracle_session.commit()

        # print("""----------------------NEXTGEN CET--------------------------""")
        if cet==NEXTGEN and item.comp_code not in ['TTL', TMML_CO_DEP_CODE, TMML_CO_DEP_CODE_0]:
            operation, status, message = update_to_nextgen_cet(
                oracle_session = oracle_session,
                row = item,
                file_id = file_id
            )
            file_row_log = update_file_row_log(
                    oracle_session,
                    file_row_log = file_row_log,
                    nextgen_status = status,
                    nextgen_operation = operation,
                    nextgen_reason = message,
                    commit = True
                )
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(
                    ProcessRowLogs.perno == item.perno,
                    ProcessRowLogs.nextgen_status == FAILED,
                ).update(
                    {
                        ProcessRowLogs.nextgen_status: RESOLVED,
                        ProcessRowLogs.resolved_file_id: file_row_log.file_id,
                        ProcessRowLogs.resolved_file_row_id: file_row_log.file_row_id,
                    }
                )
                oracle_session.commit()
                
        # print("""----------------------KEYCLOAK--------------------------""")
        if cet==BLUECOLLAR and item.comp_code not in ["TTL", "300", TMML_CO_CODE, TMML_CO_CODE_0, TMML_CO_DEP_CODE, TMML_CO_DEP_CODE_0]:
            operation, status, message = sync_records_in_keycloak(
                oracle_session,
                record=item,
                file_id=file_id
            )
            file_row_log = update_file_row_log(
                oracle_session,
                file_row_log=file_row_log,
                keycloak_status=status,
                keycloak_operation=operation,
                keycloak_reason=message,
                commit=True,
            )
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(
                    ProcessRowLogs.perno == item.perno, ProcessRowLogs.keycloak_status == FAILED
                ).update(
                    {
                        ProcessRowLogs.keycloak_status: RESOLVED,
                        ProcessRowLogs.resolved_file_id: file_row_log.file_id,
                        ProcessRowLogs.resolved_file_row_id: file_row_log.file_row_id,
                    }
                )
                oracle_session.commit()
    # print("Iterator ends here...")
    # print(f'-----------###########----END--{item.perno}--END---------##############-------------')

def perform_operations_tscmsl(oracle_session, item, file_id):
    # valid_subgroups = create_valid_sub_group()
    # Iterator start here...
    # for row in temp_data_list:
    # print(f'-----------###########----START--{item.perno}--START---------##############-------------')
    file_row_log = update_file_row_log_tscmsl(
        oracle_session, 
        perno = item.perno, 
        comp_code = item.comp_code, 
        is_upn_update = False, 
        file_id = file_id,
        commit = False
    )
    status, error = validate_fields_tscmsl(
        oracle_session, 
        item
    )
    if status == False:
        file_row_log = update_file_row_log_tscmsl(
            oracle_session,
            file_row_log = file_row_log,
            # edp_status = FAILED,
            # edp_reason = error,
            aws_cet_status = FAILED,
            aws_cet_reason = error,
            azure_cet_status = FAILED,
            azure_cet_reason = error,
            ad_status = FAILED,
            ad_reason = error,
            is_upn_update = False,
            commit = True
        )
    else:
        # print("""-------------------------SAP--------------------------------""")
            sync_record_in_sap_cet(
                oracle_session, 
                item, 
                file_id
            )
        # print("""---------------------Active Directory----------------------""")
            operation, status, message, upn_status, sap_status, sap_msg = sync_record_in_active_directory_tsctmsl(
                oracle_session = oracle_session,
                record = item,
                file_id = file_id,
            )
            file_row_log = update_file_row_log_tscmsl(
                oracle_session,
                file_row_log = file_row_log,
                ad_status = status,
                ad_operation = operation,
                ad_reason = message,
                is_upn_update = upn_status,
                commit = True
            )
            if sap_msg:
                insert_sap_file_row(oracle_session,file_row_log.file_row_id, item.perno, sap_status, sap_msg)
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno == item.perno, ProcessRowLogs.ad_status == FAILED).update({ProcessRowLogs.ad_status : RESOLVED, ProcessRowLogs.resolved_file_id : file_row_log.file_id, ProcessRowLogs.resolved_file_row_id : file_row_log.file_row_id})
                oracle_session.commit()
        
            
        # print("""-----------------------AWS CET------------------------------""")
            # operation, status, message = update_to_aws_cet_tscmsl(
            #     oracle_session = oracle_session,
            #     row = item,
            #     file_id = file_id
            # )
            # file_row_log = update_file_row_log_tscmsl(
            #     oracle_session,
            #     file_row_log = file_row_log,
            #     aws_cet_status = status,
            #     aws_cet_operation = operation,
            #     aws_cet_reason = message,
            #     commit = True
            # )
            # if status == SUCCESS:
            #     oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno == item.perno, ProcessRowLogs.aws_cet_status == FAILED).update({ProcessRowLogs.aws_cet_status : RESOLVED, ProcessRowLogs.resolved_file_id : file_row_log.file_id, ProcessRowLogs.resolved_file_row_id : file_row_log.file_row_id})
            #     oracle_session.commit()
                
        # print("""-----------------------AZURE CET------------------------------""")
            operation, status, message = update_to_azure_cet_tscmsl(
                oracle_session = oracle_session,
                row = item,
                file_id = file_id
            )
            file_row_log = update_file_row_log_tscmsl(
                oracle_session,
                file_row_log = file_row_log,
                azure_cet_status = status,
                azure_cet_operation = operation,
                azure_cet_reason = message,
                commit = True
            )
            if status == SUCCESS:
                oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno == item.perno, ProcessRowLogs.azure_cet_status == FAILED).update({ProcessRowLogs.azure_cet_status : RESOLVED, ProcessRowLogs.resolved_file_id : file_row_log.file_id, ProcessRowLogs.resolved_file_row_id : file_row_log.file_row_id})
                oracle_session.commit()


def update_upn_in_cet(oracle_session, file_id):
    
    #------- update in AWS ------
    process_row_logs = oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.file_id == file_id)
    
    for log in process_row_logs:
        if log.comp_code != "TTL":
            co_code = log.comp_code.rjust(4,'0') if log.comp_code != '300' else '0100'
        else :
            co_code = log.comp_code
        result_azure = mssql_azure_session.query(AWSEmployeeData).filter(AWSEmployeeData.pers_no == log.perno, AWSEmployeeData.co_code == co_code).first()
        # result_aws = mssql_aws_session.query(AWSEmployeeData).filter(AWSEmployeeData.pers_no == log.perno, AWSEmployeeData.co_code == co_code).first()
        # if log.aws_cet_operation == UPDATED and log.aws_cet_status == SUCCESS:
        #     if not result_aws.upn_id:
        #         ad_data = get_ad_data_by_perno(log.perno)
        #         if ad_data:
        #             result_aws.upn_id = ad_data.get('sAMAccountName').lower()
        #             mssql_aws_session.commit()
        # if log.aws_cet_operation == INSERTED and log.aws_cet_status == SUCCESS:
        #     ad_data = get_ad_data_by_perno(log.perno)
        #     if ad_data:
        #         result_aws.upn_id = ad_data.get('sAMAccountName').lower()
        #         if not log.is_upn_update:
        #             result_aws.email_id = ad_data.get('mail').lower()
        #             mssql_aws_session.commit()
        if log.azure_cet_operation == UPDATED and log.azure_cet_status == SUCCESS:
            if not result_azure.upn_id:
                ad_data = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == log.perno).first()
                if ad_data:
                    result_azure.upn_id = ad_data.user_id.lower()
                    mssql_azure_session.commit()
        if log.azure_cet_operation == INSERTED and log.azure_cet_status == SUCCESS:
            ad_data = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == log.perno).first()
            if ad_data:
                result_azure.upn_id = ad_data.user_id.lower()
                if log.is_upn_update:
                    result_azure.email_id = ad_data.email
                    mssql_azure_session.commit()
    