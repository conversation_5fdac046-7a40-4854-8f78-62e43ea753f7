from commons.db_connections import create_new_oracle_session, create_oracle_session
from commons.common_functions_poc import get_total_count, print_error
from commons.common_functions import update_file_log
from scripts.scheduled_job import extract_and_load_common
from airflow.operators.python import PythonOperator
from commons.mains import perform_operations
from commons.configs import LOAD_TO_TEMP
# from commons.models import FileLogs
from commons.models_v2 import ProcessLogs
from commons.notifications import send_success_email
from commons.messages import SUCCESS

def load_to_temp_table(sftp_connection_id, sftp_path, sftp_archived_path, temp_table):
    try:
        return extract_and_load_common(
            sftp_conn_id=sftp_connection_id,
            sftp_path=sftp_path,
            sftp_archived_path=sftp_archived_path,
            temp_table=temp_table,
        )
    except Exception as error:
        print_error("load_to_temp_table", error)


def load_to_cets(limit, offset, temp_table, **kwargs):
    try:
        ti = kwargs["ti"]
        file_id = ti.xcom_pull(task_ids=LOAD_TO_TEMP)
        if not file_id:
            raise Exception('File ID not found')
        session = create_oracle_session()
        file_log = session.query(ProcessLogs).filter(ProcessLogs.file_id == file_id).first()
        if not file_log:
            raise Exception('File Log not found')
        employee_data = session.query(temp_table).limit(limit).offset(offset).all()
        for items in employee_data:
            perform_operations(session, items, file_log.file_id)
        update_file_log(
            session, file_log=file_log,reason=None, commit=True
        )
    except Exception as error:
        print("load_to_cets", error)

# Function to create dynamic tasks
def create_dynamic_tasks(dag, temp_table):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        data_count = get_total_count(table=temp_table, session=new_oracle_session)
        workers = 4
        limit = data_count // workers
        remaining = data_count % workers

        tasks = []
        for i in range(workers):
            offset = i * limit
            if remaining and i == workers - 1:
                limit += remaining

            task_id = f"worker_{i+1}"
            task = PythonOperator(
                task_id=task_id,
                python_callable=load_to_cets,
                op_kwargs={"limit": limit, "offset": offset, "temp_table": temp_table},
                dag=dag,
            )
            tasks.append(task)

        return tasks

    except Exception as error:
        print(error)
        return []

def send_success_email_and_update_log(ti):
    try:
        file_log_id = ti.xcom_pull(task_ids=LOAD_TO_TEMP)
        session = create_oracle_session()
        file_log = session.query(ProcessLogs).filter(ProcessLogs.file_id == file_log_id).first()
        update_file_log(session, file_log=file_log, status=SUCCESS, commit=True)
        send_success_email(file_log_id)
    except Exception as error:
        print_error("send_success_email", error)
