# In-built imports...
from datetime import datetime

# Custom imports...
# from commons.models import SapEmployeeMaster, SapChangeLogHistory
from commons.models_v2 import SapEmployeeMaster, SapChangeLogHistory
from commons.common_functions import print_error


def sync_record_in_sap_cet(oracle_session, sap_row_obj, file_id ):
    try:
        is_new_record = False
        sap_data = oracle_session.query(SapEmployeeMaster).filter(SapEmployeeMaster.person_no==sap_row_obj.perno).first()
        if not sap_data:
            is_new_record = True
            sap_data = SapEmployeeMaster(person_no=sap_row_obj.perno)
        else:
            add_sap_change_log(oracle_session, sap_data, file_id)
        sap_data.company_code = sap_row_obj.comp_code
        sap_data.company_text = sap_row_obj.ccodetxt
        sap_data.pers_area_code = sap_row_obj.pers_area
        sap_data.pers_area_text = sap_row_obj.patxt
        sap_data.pers_subarea_code = sap_row_obj.p_subarea
        sap_data.pers_subarea_text = sap_row_obj.psatxt
        sap_data.employee_group_code = sap_row_obj.egroup
        sap_data.employee_group_text = sap_row_obj.ptext
        sap_data.employee_subgroup_code = sap_row_obj.esubgroup
        sap_data.employee_subgroup_text = sap_row_obj.esgtxt
        sap_data.payroll_area_code = sap_row_obj.payarea
        sap_data.payroll_area_text = sap_row_obj.paytxt
        sap_data.cost_center_code = sap_row_obj.costcenter
        sap_data.cost_center_text = sap_row_obj.cosctrtxt
        sap_data.position_code = sap_row_obj.position1
        sap_data.position_text = sap_row_obj.postxt
        sap_data.job_code = sap_row_obj.job
        sap_data.job_text = sap_row_obj.jobtxt
        sap_data.bus_area_code = sap_row_obj.bus_area
        sap_data.bus_area_text = sap_row_obj.bustxt
        sap_data.fkbtx = sap_row_obj.fkbtx
        sap_data.salutation = sap_row_obj.title
        sap_data.first_name = sap_row_obj.firstname
        sap_data.middle_name = sap_row_obj.midnm
        sap_data.last_name = sap_row_obj.last_name
        sap_data.full_name = sap_row_obj.comp_name
        sap_data.initials = sap_row_obj.initials
        sap_data.gender = sap_row_obj.gender
        sap_data.religion_code = sap_row_obj.konfe
        sap_data.religion_text = sap_row_obj.religion
        sap_data.maritial_status_code = sap_row_obj.famst
        sap_data.maritial_status_text = sap_row_obj.mar_status
        sap_data.blood_group = sap_row_obj.bloodgroup
        sap_data.dob = sap_row_obj.dob
        sap_data.doj = sap_row_obj.doj
        sap_data.dobsorp = sap_row_obj.dobsorp
        sap_data.dsvcvp = sap_row_obj.dsvcvp
        sap_data.dlprom = sap_row_obj.dlprom
        sap_data.dosep = sap_row_obj.dosep
        sap_data.seprsn = sap_row_obj.seprsn
        sap_data.challenged = sap_row_obj.challenged
        sap_data.reporting_person_no = sap_row_obj.reporting
        sap_data.reporting_fullname = sap_row_obj.nameofreporting
        sap_data.employee_status_code = sap_row_obj.empstatus
        sap_data.employee_status_text = sap_row_obj.empstattxt
        sap_data.org_unit_code = sap_row_obj.orgeh
        sap_data.org_unit_text = sap_row_obj.orgtx
        sap_data.office_telephone_no = sap_row_obj.off_num
        sap_data.office_mobile_no = sap_row_obj.office_mobile
        sap_data.personal_mobile_no = sap_row_obj.pers_mobile
        sap_data.emergency_mobile_no = sap_row_obj.emrg_mobile
        sap_data.company_email = sap_row_obj.imailid
        sap_data.personal_email = sap_row_obj.exmailid
        sap_data.transport_code = sap_row_obj.transport_code
        sap_data.office_address = sap_row_obj.off_addr
        sap_data.sep_code = sap_row_obj.sepcode
        sap_data.function_group_code = sap_row_obj.fun
        sap_data.function_group_text = sap_row_obj.funt
        sap_data.hiring_reason = sap_row_obj.mgtxt
        sap_data.flag1 = sap_row_obj.flag1
        sap_data.last_prom1_date = sap_row_obj.last_prom1
        sap_data.last_prom2_date = sap_row_obj.last_prom2
        sap_data.last_prom3_date = sap_row_obj.last_prom3
        sap_data.dummy1 = sap_row_obj.dummy1
        sap_data.dummy2 = sap_row_obj.dummy2
        sap_data.dummy3 = sap_row_obj.dummy3
        sap_data.dummy4 = sap_row_obj.dummy4
        sap_data.dummy5 = sap_row_obj.dummy5
        sap_data.bhr_perno = sap_row_obj.bhr_perno
        sap_data.mat1_mngr = sap_row_obj.mat1_mngr
        sap_data.mat2_mngr = sap_row_obj.mat2_mngr
        sap_data.mat3_mngr = sap_row_obj.mat3_mngr
        sap_data.mat4_mngr = sap_row_obj.mat4_mngr
        sap_data.mat5_mngr = sap_row_obj.mat5_mngr
        sap_data.head_hr_perno = sap_row_obj.head_hr_perno
        sap_data.er_hr_perno = sap_row_obj.er_hr_perno
        sap_data.ou_short_text = sap_row_obj.ou_short_txt
        sap_data.region_code = sap_row_obj.reg_cod
        sap_data.region_text = sap_row_obj.reg_cod_txt
        sap_data.jobtxt_short = sap_row_obj.jobtxt_short
        sap_data.ou_level1_code = sap_row_obj.ou_level1
        sap_data.ou_level1_short_text = sap_row_obj.ou_level1_short_txt
        sap_data.ou_level1_long_text = sap_row_obj.ou_level1_long_txt
        sap_data.ou_level2_code = sap_row_obj.ou_level2
        sap_data.ou_level2_short_text = sap_row_obj.ou_level2_short_txt
        sap_data.ou_level2_long_text = sap_row_obj.ou_level2_long_txt
        sap_data.ou_level3_code = sap_row_obj.ou_level3
        sap_data.ou_level3_short_text = sap_row_obj.ou_level3_short_txt
        sap_data.ou_level3_long_text = sap_row_obj.ou_level3_long_txt
        sap_data.function_id_code = sap_row_obj.func_id01
        sap_data.function_id_text = sap_row_obj.function_text
        sap_data.subfunction_id1_code = sap_row_obj.subfunction1_id
        sap_data.subfunction_id1_text = sap_row_obj.subfunction1_tex
        sap_data.subfunction_id2_code = sap_row_obj.subfunction2_id
        sap_data.subfunction_id2_text = sap_row_obj.subfunction2_tex
        sap_data.location_code = sap_row_obj.loc_cod
        sap_data.location_text = sap_row_obj.loc_desc
        sap_data.add1 = sap_row_obj.add1
        sap_data.add2 = sap_row_obj.add2
        sap_data.add3 = sap_row_obj.add3
        sap_data.add4 = sap_row_obj.add4
        sap_data.city = sap_row_obj.city_town
        sap_data.state = sap_row_obj.state
        sap_data.pincode = sap_row_obj.pin_code
        sap_data.country = sap_row_obj.country
        sap_data.a962_perno = sap_row_obj.a962_perno
        sap_data.a962_fullname = sap_row_obj.a962_cname
        if is_new_record:
            oracle_session.add(sap_data)
        oracle_session.commit()
    except Exception as error:
        print_error('sync_record_in_sap_cet',error)
        oracle_session.rollback()

        
def add_sap_change_log(oracle_session, sap_data, file_id):
    sap_change_log = SapChangeLogHistory(
        person_no=sap_data.person_no,
        company_code=sap_data.company_code,
        company_text=sap_data.company_text,
        pers_area_code=sap_data.pers_area_code,
        pers_area_text=sap_data.pers_area_text,
        pers_subarea_code=sap_data.pers_subarea_code,
        pers_subarea_text=sap_data.pers_subarea_text,
        employee_group_code=sap_data.employee_group_code,
        employee_group_text=sap_data.employee_group_text,
        employee_subgroup_code=sap_data.employee_subgroup_code,
        employee_subgroup_text=sap_data.employee_subgroup_text,
        payroll_area_code=sap_data.payroll_area_code,
        payroll_area_text=sap_data.payroll_area_text,
        cost_center_code=sap_data.cost_center_code,
        cost_center_text=sap_data.cost_center_text,
        position_code=sap_data.position_code,
        position_text=sap_data.position_text,
        job_code=sap_data.job_code,
        job_text=sap_data.job_text,
        bus_area_code=sap_data.bus_area_code,
        bus_area_text=sap_data.bus_area_text,
        fkbtx=sap_data.fkbtx,
        salutation=sap_data.salutation,
        first_name=sap_data.first_name,
        middle_name=sap_data.middle_name,
        last_name=sap_data.last_name,
        full_name=sap_data.full_name,
        intials=sap_data.initials,
        gender=sap_data.gender,
        religion_code=sap_data.religion_code,
        religion_text=sap_data.religion_text,
        maritial_status_code=sap_data.maritial_status_code,
        maritial_status_text=sap_data.maritial_status_text,
        blood_group=sap_data.blood_group,
        dob=sap_data.dob,
        doj=sap_data.doj,
        dobsorp=sap_data.dobsorp,
        dsvcvp=sap_data.dsvcvp,
        dlprom=sap_data.dlprom,
        dosep=sap_data.dosep,
        seprsn=sap_data.seprsn,
        challenged=sap_data.challenged,
        reporting_person_no=sap_data.reporting_person_no,
        reporting_fullname=sap_data.reporting_fullname,
        employee_status_code=sap_data.employee_status_code,
        employee_status_text=sap_data.employee_status_text,
        org_unit_code=sap_data.org_unit_code,
        org_unit_text=sap_data.org_unit_text,
        office_telephone_no=sap_data.office_telephone_no,
        office_mobile_no=sap_data.office_mobile_no,
        personal_mobile_no=sap_data.personal_mobile_no,
        emergency_mobile_no=sap_data.emergency_mobile_no,
        company_email=sap_data.company_email,
        personal_email=sap_data.personal_email,
        transport_code=sap_data.transport_code,
        office_address=sap_data.office_address,
        sep_code=sap_data.sep_code,
        funtion_group_code=sap_data.function_group_code,
        function_group_text=sap_data.function_group_text,
        hiring_reason=sap_data.hiring_reason,
        flag1=sap_data.flag1,
        last_prom1_date=sap_data.last_prom1_date,
        last_prom2_date=sap_data.last_prom2_date,
        last_prom3_date=sap_data.last_prom3_date,
        dummy1=sap_data.dummy1,
        dummy2=sap_data.dummy2,
        dummy3=sap_data.dummy3,
        dummy4=sap_data.dummy4,
        dummy5=sap_data.dummy5,
        bhr_perno=sap_data.bhr_perno,
        mat1_mngr=sap_data.mat1_mngr,
        mat2_mngr=sap_data.mat2_mngr,
        mat3_mngr=sap_data.mat3_mngr,
        mat4_mngr=sap_data.mat4_mngr,
        mat5_mngr=sap_data.mat5_mngr,
        head_hr_perno=sap_data.head_hr_perno,
        er_hr_perno=sap_data.er_hr_perno,
        ou_short_text=sap_data.ou_short_text,
        region_code=sap_data.region_code,
        region_text=sap_data.region_text,
        jobtxt_short=sap_data.jobtxt_short,
        ou_level1_code=sap_data.ou_level1_code,
        ou_level1_short_text=sap_data.ou_level1_short_text,
        ou_level1_long_text=sap_data.ou_level1_long_text,
        ou_level2_code=sap_data.ou_level2_code,
        ou_level2_short_text=sap_data.ou_level2_short_text,
        ou_level2_long_text=sap_data.ou_level2_long_text,
        ou_level3_code=sap_data.ou_level3_code,
        ou_level3_short_text=sap_data.ou_level3_short_text,
        ou_level3_long_text=sap_data.ou_level3_long_text,
        function_id_code=sap_data.function_id_code,
        function_id_text=sap_data.function_id_text,
        subfunction_id1_code=sap_data.subfunction_id1_code,
        subfunction_id1_text=sap_data.subfunction_id1_text,
        subfunction_id2_code=sap_data.subfunction_id2_code,
        subfunction_id2_text=sap_data.subfunction_id2_text,
        location_code=sap_data.location_code,
        location_text=sap_data.location_text,
        add1=sap_data.add1,
        add2=sap_data.add2,
        add3=sap_data.add3,
        add4=sap_data.add4,
        city=sap_data.city,
        state=sap_data.state,
        pincode=sap_data.pincode,
        country=sap_data.country,
        a962_perno=sap_data.a962_perno,
        a962_fullname=sap_data.a962_fullname,
        is_manual_entry=False,
        is_deleted=False,
        file_id= file_id,
        created_at=datetime.now()
    )
    oracle_session.add(sap_change_log)
    oracle_session.commit()
