import os, sys

def print_error(func_name, error=""):
    try:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb:
            file_name = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            error = f"Error - {error} \n Exception - {exc_type} \n File Name - {file_name} \n Function Name - {func_name} \n Line No - {exc_tb.tb_lineno}"
            print(error)
            return error
        else:
            error = f"{str(func_name)} : {str(error)}"
            print(error)
            return error
    except Exception as error:
        print(f"print_error(): {error}")


def get_total_count(table, session):
    try:
        count = session.query(table).count()
        return count
    except Exception as error:
        print_error("get_total_count", error)
