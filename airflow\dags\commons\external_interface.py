# In-built imports...
from requests import delete
from json import loads, dumps
from commons.configs import AD_LDAP_USER_URL, REQUEST_BODY, HEADER
from commons.messages import  RECORD_DELETED_SUCCESSFULLY, SUCCESS , SOMETHING_WENT_WRONG


def delete_user_from_AD(adid):
    try:
        status, resp, request_object, request_object['sAMAccountName'] = False, "", REQUEST_BODY, adid
        request = delete(AD_LDAP_USER_URL, headers = HEADER, data = dumps(request_object))
        if request.status_code == 200:
            response_object = loads(request.content)
            if  'message' in response_object and response_object['message'] == RECORD_DELETED_SUCCESSFULLY:
                print(f'delete_user_from_AD() : {request.status_code}, {response_object}')
                status, resp = True, SUCCESS
            else:
                resp = response_object['message'] if 'message' in response_object else SOMETHING_WENT_WRONG
        else:
            resp = request.status_code
    except Exception as error:
        print(f'delete_user_from_AD() : {error}')
        resp = str(error)
    return status, resp
