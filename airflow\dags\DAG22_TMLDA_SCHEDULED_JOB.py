# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
# Custom imports...
from commons.models_v2 import TMDATempTable
from commons.configs import  AD, A<PERSON><PERSON><PERSON>, DAG22_TMLDA_SCHEDULED_JOBS, DAG22_TMLDA_SAP_AUTOSYNC_SCHEDULER, EDP, LOAD_TO_AD, LOAD_TO_AWS_CET, LOAD_TO_AZURE_CET, LOAD_TO_BLUECOLLAR_KEYCLOACK, LOAD_TO_EDP_CET, LOAD_TO_NEXTGEN_CET, LOAD_TO_SAP, LOAD_TO_TEMP, NEXTGEN, SAP, TRIGGER_ID, DAG2_DELETE_JOBS, UPDATE_UPN_AND_SEND_EMAIL
from scripts.scheduled_job import extract_and_load_TMLDA, schedule_load_to_cet, update_upn_and_send_email


# DAG initialization...
with DAG(
    dag_id = DAG22_TMLDA_SCHEDULED_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG22_TMLDA_SAP_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    load_to_temp_ = PythonOperator(
        task_id = LOAD_TO_TEMP,
        python_callable = extract_and_load_TMLDA,
        do_xcom_push = True    
    )
    
    # Task 2 Initialization...
    load_to_sap = PythonOperator(
        task_id = LOAD_TO_SAP,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMDATempTable, "cet": SAP},
        do_xcom_push = True    
    )
    
    load_to_ad = PythonOperator(
        task_id = LOAD_TO_AD,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMDATempTable, "cet": AD},
        do_xcom_push = True    
    )
    
    # Task 2 Initialization...
    load_to_azure_cet = PythonOperator(
        task_id = LOAD_TO_AZURE_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMDATempTable, "cet": AZURE},
        do_xcom_push = True    
    )
    
    load_to_edp_cet = PythonOperator(
        task_id = LOAD_TO_EDP_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMDATempTable, "cet": EDP},
        do_xcom_push = True    
    )
    
    load_to_nextgen_cet = PythonOperator(
        task_id = LOAD_TO_NEXTGEN_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TMDATempTable, "cet": NEXTGEN},
        do_xcom_push = True    
    )
    
    # load_to_blue_collar_keycloak = PythonOperator(
    #     task_id = LOAD_TO_BLUECOLLAR_KEYCLOACK,
    #     python_callable = schedule_load_to_cet,
    #     op_kwargs={'temp_table': TMDATempTable, "cet": BLUECOLLAR},
    #     do_xcom_push = True    
    # )
    
    update_upn_and_send_email_task = PythonOperator(
        task_id = UPDATE_UPN_AND_SEND_EMAIL,
        python_callable = update_upn_and_send_email,
        do_xcom_push = True    
    )
    
    # # Task 3 Initialization...
    # delete_job = TriggerDagRunOperator(
    #     task_id = TRIGGER_ID,
    #     trigger_dag_id = DAG2_DELETE_JOBS, 
    #     dag = dag,
    # )

    # Task sequencing & call...
    load_to_temp_ >> load_to_sap >> [load_to_ad, load_to_azure_cet, load_to_edp_cet, load_to_nextgen_cet] >> update_upn_and_send_email_task