# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.configs import DAG3_IMAC_JOBS, DAG3_IMAC_JOB_SCHEDULER, TRIGGER_IMAC
from scripts.trigger_imac_job import trigger_imac_task


# DAG initialization...
with DAG(
    dag_id = DAG3_IMAC_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG3_IMAC_JOB_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    trigger_imac = PythonOperator(
        task_id = TRIGGER_IMAC,
        python_callable = trigger_imac_task,
        do_xcom_push = True    
    )
    
    # Task sequencing & call...
    trigger_imac
    # >>imac_run_notification