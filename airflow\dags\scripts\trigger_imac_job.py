# In-built imports...
from datetime import datetime, timedelta
from json import loads, dumps
from requests import post
# Custom imports...
from commons.configs import REQUEST_BODY, IMAC_CREATE_TICKET, HEADER, SEND_IMAC_JOB_NOTIFICATION_TO, SUBJECT_IMAC_JOB_NOTIFICATION, BODY_IMAC_JOB_NOTIFICATION, SUBJECT_JOB_FAILED_NOTIFICATION
from commons.messages import PENDING, FAILED
from commons.db_connections import create_new_oracle_session, create_oracle_session
from commons.notifications import send_email_notification
# from commons.models import IMAC_logs
from commons.models_v2 import IMAC_Logs
from commons.common_functions import print_error


# DAG 3 : Trigger IMAC Task 
def trigger_imac_task():
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        # Get all pending IMAC request list...
        yesterday = datetime.now().date() - timedelta(days=1)
        pending_imac_list = new_oracle_session.query(IMAC_Logs).filter(IMAC_Logs.imac_status == PENDING).all()
        if pending_imac_list:
            for data in pending_imac_list:
                if data.created_at.date() <= yesterday:
                    body = REQUEST_BODY
                    body['username'] = data.user_id
                    body["report_to"] = data.report_to
                    body["location"] = data.imac_location.iloc_name
                    body["company_name"] = data.imac_company_text
                    body['asset_type'] = data.asset_type
                    # Trigger IMAC create ticket...
                    response = post(url = IMAC_CREATE_TICKET, headers = HEADER, data = dumps(body))
                    if response.status_code == 200:
                        response = response.content.decode('utf-8')
                        response = loads(response)
                    # Update IMAC status...
                    if response and 'result' in response and 'data' in response['result'] and 'ticketId' in response['result']['data'] and 'message' in response['result']['data']:
                        response_data = response['result']['data']
                        if response_data['message'] == 'Success':
                            print('IMAC Success ','User ID - ',data.user_id, 'Ticket ID - ',response_data['ticketId'])
                            data.imac_ticket_id = response_data['ticketId']
                            data.imac_status = response_data['message']
                            data.imac_comments = None
                        else:
                            print('IMAC Failed ','User ID - ',data.user_id,'Message - ',response_data['message'])
                            data.imac_status = FAILED.title()
                            data.imac_comments = response_data['message']
                        data.updated_at = datetime.now()
                        new_oracle_session.commit()
                    else:
                        print('IMAC Failed ','User ID - ',data.user_id,' Response - ',response)
        else:
            print('-----------------No IMAC requests found-------------------')
        send_email_notification(to_email=SEND_IMAC_JOB_NOTIFICATION_TO, subject=SUBJECT_IMAC_JOB_NOTIFICATION, body=BODY_IMAC_JOB_NOTIFICATION)
    except Exception as error:
        error = print_error('trigger_imac_task()',error=error)
        send_email_notification(to_email=SEND_IMAC_JOB_NOTIFICATION_TO,subject=SUBJECT_JOB_FAILED_NOTIFICATION,body=error)
