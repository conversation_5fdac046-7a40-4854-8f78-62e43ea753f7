from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Date
from airflow.models.base import Base
from sqlalchemy.orm import relationship

class CVTempTable(Base):
    __tablename__ = 'cv_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)

class PVTempTable(Base):
    __tablename__ = 'pv_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)

class EVTempTable(Base):
    __tablename__ = 'ev_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)

class TMBSLTempTable(Base):
    __tablename__ = 'tmbsl_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)

class TTLTempTable(Base):
    __tablename__ = 'ttl_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)

class TMDATempTable(Base):
    __tablename__ = 'tmda_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)
class EDPEmployeeDetails(Base):
    __tablename__ = 'edp_employee_details'

    edp_cet_id =  Column(String, primary_key=True)
    Perno = Column(String)
    PersArea = Column(String)
    Patxt = Column(String)
    Egroup = Column(String)
    Ptext = Column(String)
    Esubgroup = Column(String)
    Esgtxt = Column(String)
    CompCode = Column(String)
    Ccodetxt = Column(String)
    BusArea = Column(String)
    Bustxt = Column(String)
    PSubarea = Column(String)
    Psatxt = Column(String)
    Payarea = Column(String)
    Paytxt = Column(String)
    Costcenter = Column(String)
    Cosctrtxt = Column(String)
    Position1 = Column(String)
    Postxt = Column(String)
    Job = Column(String)
    Jobtxt = Column(String)
    Fkbtx = Column(String)
    Initials = Column(String)
    CompName = Column(String)
    LastName = Column(String)
    Firstname = Column(String)
    Midnm = Column(String)
    Title = Column(String)
    OffNum = Column(String)
    Gender = Column(String)
    Konfe = Column(String)
    Religion = Column(String)
    Famst = Column(String)
    MarStatus = Column(String)
    Bloodgroup = Column(String)
    Imailid = Column(String)
    Exmailid = Column(String)
    Dobsorp = Column(String)
    Doj = Column(String)
    Dob = Column(String)
    Dsvcvp = Column(String)
    Dlprom = Column(String)
    Dosep = Column(String)
    Seprsn = Column(String)
    Challenged = Column(String)
    Reporting = Column(String)
    Nameofreporting = Column(String)
    Empstatus = Column(String)
    Empstattxt = Column(String)
    Orgeh = Column(String)
    Orgtx = Column(String)
    OfficeMobile = Column(String)
    PersMobile = Column(String)
    EmrgMobile = Column(String)
    TransportCode = Column(String)
    OffAddr = Column(String)
    Sepcode = Column(String)
    Fun = Column(String)
    Funt = Column(String)
    Mgtxt = Column(String)
    Flag1 = Column(String)
    LastProm1 = Column(String)
    LastProm2 = Column(String)
    LastProm3 = Column(String)
    Dummy1 = Column(String)
    Dummy2 = Column(String)
    Dummy3 = Column(String)
    Dummy4 = Column(String)
    Dummy5 = Column(String)
    BhrPerno = Column(String)
    Mat1Mngr = Column(String)
    Mat2Mngr = Column(String)
    Mat3Mngr = Column(String)
    Mat4Mngr = Column(String)
    Mat5Mngr = Column(String)
    HeadHrPerno = Column(String)
    ErHrPerno = Column(String)
    OuShortTxt = Column(String)
    RegCod = Column(String)
    RegCodTxt = Column(String)
    JobtxtShort = Column(String)
    OuLevel1 = Column(String)
    OuLevel1ShortTxt = Column(String)
    OuLevel1LongTxt = Column(String)
    OuLevel2 = Column(String)
    OuLevel2ShortTxt = Column(String)
    OuLevel2LongTxt = Column(String)
    OuLevel3 = Column(String)
    OuLevel3ShortTxt = Column(String)
    OuLevel3LongTxt = Column(String)
    FuncId01 = Column(String)
    FunctionText = Column(String)
    Subfunction1Id = Column(String)
    Subfunction1Tex = Column(String)
    Subfunction2Id = Column(String)
    Subfunction2Tex = Column(String)
    LocCod = Column(String)
    LocDesc = Column(String)
    Add1 = Column(String)
    Add2 = Column(String)
    Add3 = Column(String)
    Add4 = Column(String)
    CityTown = Column(String)
    State = Column(String)
    PinCode = Column(String)
    Country = Column(String)
    A962Perno = Column(String)
    A962Cname = Column(String)
    created_datetime = Column(String)
    modified_datetime = Column(String)

class NextGenEmployeeDetails(Base):
    __tablename__ = 'sap_daily_sync_cet_data'

    perno = Column(String,primary_key=True)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    comp_code = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    hd_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    created_on = Column(DateTime)
    updated_on = Column(DateTime)

class AWSEmployeeData(Base):
    __tablename__ = 'sap_empmaster'

    pers_no = Column(Integer, primary_key=True, autoincrement=False)
    co_code = Column(String, primary_key=True)
    pers_area = Column(String)
    empl_subgroup = Column(String)
    pers_subarea = Column(String)
    cost_center = Column(String)
    position_code = Column(String)
    position_text = Column(String)
    job_code = Column(String)
    job_text = Column(String)
    ps_group = Column(String)
    payroll_area = Column(String)
    last_name = Column(String)
    first_name = Column(String)
    complete_name = Column(String)
    middle_name = Column(String)
    known_as = Column(String)
    form_addr_key = Column(String)
    employment_status_code = Column(String)
    email_id = Column(String)
    cell_no = Column(String)
    code_sex = Column(String)
    code_blood_grp = Column(String)
    code_marital_sta = Column(String)
    code_relgn = Column(String)
    date_absorp = Column(String)
    date_birth = Column(String)
    date_joining = Column(String)
    date_last_svpc = Column(String)
    date_last_prom = Column(String)
    date_prob_comp = Column(String)
    date_separation = Column(String)
    code_del_reason = Column(String)
    code_handicap = Column(String)
    code_status = Column(String)
    smart_card_no = Column(String)
    report_to = Column(String)
    code_relgn_text = Column(String)
    code_marital_sta_text = Column(String)
    code_status_text = Column(String)
    empl_subgroup_text = Column(String)
    function_grp = Column(String)
    hiring_reason = Column(String)
    region = Column(String)
    empl_group = Column(String)
    empl_group_text = Column(String)
    location = Column(String)
    off_num = Column(String)
    ext_email = Column(String)
    org_unit = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    fax_num = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    office_mobile = Column(String)
    location_code = Column(String)
    pa_code = Column(String)
    psa_code = Column(String)
    employment_status_txt = Column(String)
    ou_l1_id = Column(String)
    ou_l2_id = Column(String)
    ou_l3_id = Column(String)
    subfun1_id = Column(String)
    subfun2_id = Column(String)
    upn_id = Column(String)
    last_modified_datetime = Column(DateTime(timezone=True))

class AwsCetMetaData(Base):

    __tablename__ = 'awscet_metadata' 

    person_no = Column(String, primary_key=True)
    known_as = Column(String)
    cell_no = Column(String)
    date_prob_comp = Column(String)
    smart_card_no = Column(String)
    fax_num = Column(String)
    upn_id = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class AzureCetMetaData(Base):

    __tablename__ = 'azurecet_metadata' 

    person_no = Column(String, primary_key=True)
    known_as = Column(String)
    cell_no = Column(String)
    date_prob_comp = Column(String)
    smart_card_no = Column(String)
    fax_num = Column(String)
    upn_id = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class AwsCetChangeLogHistory(Base):

    __tablename__ = 'aws_change_log_history'

    aws_id = Column(Integer,primary_key=True, autoincrement=True)
    person_no = Column(String)
    known_as = Column(String)
    cell_no = Column(String)
    date_prob_comp = Column(String)
    smart_card_no = Column(String)
    fax_num = Column(String)
    upn_id = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    file_id = Column(String)
    log_created_at = Column(DateTime)

class AzureCetChangeLogHistory(Base):

    __tablename__ = 'azure_change_log_history'

    azure_id = Column(Integer,primary_key=True, autoincrement=True)
    person_no = Column(String)
    known_as = Column(String)
    cell_no = Column(String)
    date_prob_comp = Column(String)
    smart_card_no = Column(String)
    fax_num = Column(String)
    upn_id = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    file_id = Column(String)
    log_created_at = Column(DateTime)
class EDPMetaData(Base):

    __tablename__ = 'edp_metadata'

    person_no = Column(String, primary_key=True)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class EDPChangeLogHistory(Base):

    __tablename__ = 'edp_change_log_history'

    edp_id = Column(Integer,primary_key=True, autoincrement=True)
    person_no = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    file_id = Column(String)
    log_created_at = Column(DateTime)

class NextGenMetaData(Base):

    __tablename__ = 'nextgen_metadata'

    person_no = Column(String, primary_key=True)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class SapCompanyMaster(Base):

    __tablename__ = 'company_master'

    com_mast_id = Column(Integer, primary_key=True, autoincrement=True)
    comp_code = Column(String)
    company_name = Column(String)
    belongs_to = Column(String)
    is_deleted = Column(Boolean)

class SapCompanyMaster1(Base):
    __tablename__ = 'sap_company_master'

    co_code = Column(String, primary_key = True)
    co_code_desc = Column(String)

class KeyCloakMetaData(Base):
    __tablename__ = 'keycloak_metadata'

    person_no = Column(String)
    username = Column(String, primary_key=True)
    firstname = Column(String)
    lastname = Column(String)
    mobilenumber = Column(String)
    location = Column(String)
    enabled = Column(Boolean)
    created_by = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class KeyCloakChangeLogHistory(Base):

    __tablename__ = "keycloak_change_log_history"
    
    keycloak_chng_id = Column(Integer,primary_key=True, autoincrement=True)
    person_no = Column(String)
    username = Column(String)
    firstname = Column(String)
    lastname = Column(String)
    mobilenumber = Column(String)
    location = Column(String)
    enabled = Column(Boolean)
    created_by = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    file_id = Column(String)
    log_created_at = Column(DateTime)
    log_created_by = Column(String)

class ESubGroup(Base):

    __tablename__ = 'subgrp_table'

    id = Column(Integer, primary_key=True, autoincrement=True)
    esubgroup_code = Column(String)
    esubgroup_text = Column(String)
    asset_type = Column(String)
    update_telephone = Column(Boolean)
    is_deleted = Column(Boolean)
    esubgroup_type = Column(Boolean)
    
class KeycloakESubGroup(Base):

    __tablename__ = 'keycloak_subgrp_table'

    id = Column(Integer, primary_key=True, autoincrement=True)
    esubgroup_code = Column(String)
    is_deleted = Column(Boolean)
    esubgroup_type = Column(Boolean)
    egroup_code = Column(String)
    
class ESubGroupTMML(Base):

    __tablename__ = 'subgrp_table_tmml'

    id = Column(Integer, primary_key=True, autoincrement=True)
    esubgroup_code = Column(String)
    esubgroup_text = Column(String)
    asset_type = Column(String)
    update_telephone = Column(Boolean)
    is_deleted = Column(Boolean)
    esubgroup_type = Column(Boolean)

class SAPPersAreaMaster(Base):

    __tablename__ = 'sap_pers_area_master'

    co_code = Column(String, primary_key=True)
    pers_area = Column(String, primary_key=True)
    pers_area_text = Column(String)

class SAPPersSAreaMaster(Base):

    __tablename__ = 'sap_pers_sarea_master'

    co_code = Column(String, primary_key=True)
    pers_area = Column(String, primary_key=True)
    pers_subarea = Column(String, primary_key=True)
    pers_sarea_text = Column(String)

class SAPOrgUnitMaster(Base):

    __tablename__ = 'sap_org_unit_master'

    co_code = Column(String, primary_key=True)
    org_unit = Column(String, primary_key=True)
    org_unit_text = Column(String)

class SAPFunctionGrpMaster(Base):

    __tablename__ = 'sap_function_grp_master'

    funcode = Column(String, primary_key=True)
    fundesc = Column(String)
    isactive = Column(String)

class SAPSubFun1GrpMaster(Base):

    __tablename__ = 'sap_subfun1_grp_master'

    funcode = Column(String, primary_key=True)
    subfun1_id = Column(String, primary_key=True)
    subfun1_txt = Column(String)

class SAPSubFun2GrpMaster(Base):

    __tablename__ = 'sap_subfun2_grp_master'

    funcode = Column(String, primary_key=True)
    subfun1_id = Column(String, primary_key=True)
    subfun2_id = Column(String, primary_key=True)
    subfun2_txt = Column(String)

class SAPOrgLevel1Master(Base):

    __tablename__ = 'sap_org_level1_master'

    co_code = Column(String, primary_key=True)
    org_unit = Column(String, primary_key=True)
    ou_l1_id = Column(String, primary_key=True)
    ou_l1_sht_txt = Column(String)
    ou_l1_long_txt = Column(String)

class SAPOrgLevel2Master(Base):

    __tablename__ = 'sap_org_level2_master'

    co_code = Column(String, primary_key=True)
    org_unit = Column(String, primary_key=True)
    ou_l2_id = Column(String, primary_key=True)
    ou_l2_sht_txt = Column(String)
    ou_l2_long_txt = Column(String)

class SAPOrgLevel3Master(Base):

    __tablename__ = 'sap_org_level3_master'

    co_code = Column(String, primary_key=True)
    org_unit = Column(String, primary_key=True)
    ou_l3_id = Column(String, primary_key=True)
    ou_l3_sht_txt = Column(String)
    ou_l3_long_txt = Column(String)

class SAPCostCenterMaster(Base):

    __tablename__ = 'sap_cost_center_master'

    co_code = Column(String, primary_key=True)
    cost_center = Column(String, primary_key=True)
    cost_center_text = Column(String)

class SAPPayAreaMaster(Base):

    __tablename__ = 'sap_pay_area_master'

    co_code = Column(String, primary_key=True)
    pay_area = Column(String, primary_key=True)
    pay_area_name = Column(String)

class TSCMSLTempTable(Base):
    __tablename__ = 'tscmsl_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)

class TMMLTempTable(Base):
    __tablename__ = 'tmml_temp_table'

    perno = Column(String, primary_key=True)
    comp_code = Column(String)
    pers_area = Column(String)
    patxt = Column(String)
    egroup = Column(String)
    ptext = Column(String)
    esubgroup = Column(String)
    esgtxt = Column(String)
    ccodetxt = Column(String)
    bus_area = Column(String)
    bustxt = Column(String)
    p_subarea = Column(String)
    psatxt = Column(String)
    payarea = Column(String)
    paytxt = Column(String)
    costcenter = Column(String)
    cosctrtxt = Column(String)
    position1 = Column(String)
    postxt = Column(String)
    job = Column(String)
    jobtxt = Column(String)
    fkbtx = Column(String)
    initials = Column(String)
    comp_name = Column(String)
    last_name = Column(String)
    firstname = Column(String)
    midnm = Column(String)
    title = Column(String)
    off_num = Column(String)
    gender = Column(String)
    konfe = Column(String)
    religion = Column(String)
    famst = Column(String)
    mar_status = Column(String)
    bloodgroup = Column(String)
    imailid = Column(String)
    exmailid = Column(String)
    dobsorp = Column(String)
    doj = Column(String)
    dob = Column(String)
    dsvcvp = Column(String)
    dlprom = Column(String)
    dosep = Column(String)
    seprsn = Column(String)
    challenged = Column(String)
    reporting = Column(String)
    nameofreporting = Column(String)
    empstatus = Column(String)
    empstattxt = Column(String)
    orgeh = Column(String)
    orgtx = Column(String)
    office_mobile = Column(String)
    pers_mobile = Column(String)
    emrg_mobile = Column(String)
    transport_code = Column(String)
    off_addr = Column(String)
    sepcode = Column(String)
    fun = Column(String)
    funt = Column(String)
    mgtxt = Column(String)
    flag1 = Column(String)
    last_prom1 = Column(String)
    last_prom2 = Column(String)
    last_prom3 = Column(String)
    dummy1 = Column(String)
    dummy2 = Column(String)
    dummy3 = Column(String)
    dummy4 = Column(String)
    dummy5 = Column(String)
    bhr_perno = Column(String)
    mat1_mngr = Column(String)
    mat2_mngr = Column(String)
    mat3_mngr = Column(String)
    mat4_mngr = Column(String)
    mat5_mngr = Column(String)
    head_hr_perno = Column(String)
    er_hr_perno = Column(String)
    ou_short_txt = Column(String)
    reg_cod = Column(String)
    reg_cod_txt = Column(String)
    jobtxt_short = Column(String)
    ou_level1 = Column(String)
    ou_level1_short_txt = Column(String)
    ou_level1_long_txt = Column(String)
    ou_level2 = Column(String)
    ou_level2_short_txt = Column(String)
    ou_level2_long_txt = Column(String)
    ou_level3 = Column(String)
    ou_level3_short_txt = Column(String)
    ou_level3_long_txt = Column(String)
    func_id01 = Column(String)
    function_text = Column(String)
    subfunction1_id = Column(String)
    subfunction1_tex = Column(String)
    subfunction2_id = Column(String)
    subfunction2_tex = Column(String)
    loc_cod = Column(String)
    loc_desc = Column(String)
    add1 = Column(String)
    add2 = Column(String)
    add3 = Column(String)
    add4 = Column(String)
    city_town = Column(String)
    state = Column(String)
    pin_code = Column(String)
    country = Column(String)
    a962_perno = Column(String)
    a962_cname = Column(String)
    incident_no = Column(String)
    created_by = Column(String)

class EmployeeSeparation(Base):
    
    __tablename__ = 'employee_separation'
    id = Column(Integer, primary_key=True, autoincrement=True)
    separation_request_id = Column(String)
    employee_id = Column(String)
    position_code= Column(String)
    separation_status = Column(String)
    employee_comp_code = Column(String)
    resignation_request_date = Column(DateTime)
    actual_resignation_date = Column(DateTime)
    lwd_requested_by_employee = Column(DateTime)
    lwd_recommended_by_dem = Column(DateTime)
    lwd_recommended_by_bhr = Column(DateTime)
    created_by = Column(String)
    created_datetime = Column(DateTime)
    modified_by = Column(String)
    modified_datetime = Column(String)
    is_deleted = Column(DateTime)

class EmployeeRegistration(Base):

    __tablename__ = 'employee_registration'
    unique_id = Column(String, primary_key=True)
    employee_id = Column(String)
    mobile_number = Column(String)
    is_active = Column(Boolean)
    is_pin_set = Column(Boolean)
    consent_text = Column(String)
    is_deleted = Column(Boolean)
    created_by = Column(String)
    created_date_time = Column(DateTime)
    modified_by = Column(String)
    modified_date_time = Column(DateTime)

class ExtAttr13Mappping(Base):
    __tablename__ = 'ext_attr_13_mapping'
    id = Column(String, primary_key=True)
    extattr13 = Column(String)
    location = Column(String)
    is_deleted = Column(Boolean)


#-----------------------------------------------------------------------------------------------------------------------------------#
# Changes

class ProcessLogs(Base):
    __tablename__ = 'process_logs'

    file_id = Column(String(256), primary_key=True)
    job_name = Column(String(256), nullable=True)
    file_name = Column(String(256), nullable=True)
    file_format = Column(String(256), nullable=True)
    row_count = Column(String(256), nullable=True)
    column_count = Column(String(256), nullable=True)
    status = Column(String(256), nullable=True)
    reason = Column(String(256), nullable=True)
    trigger_type = Column(String(256), nullable=True)
    created_by = Column(String(256), nullable=True)
    started_at = Column(DateTime, default=datetime.utcnow)
    ended_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    row_logs = relationship("ProcessRowLogs", back_populates="process_log")

class ProcessRowLogs(Base):
    __tablename__ = 'process_row_logs'

    file_row_id = Column(String(256), primary_key=True)
    perno = Column(String(256), nullable=True)
    comp_code = Column(String(256), nullable=True)
    ad_operation = Column(String(256), nullable=True)
    aws_cet_operation = Column(String(256), nullable=True)
    azure_cet_operation = Column(String(256), nullable=True)
    edp_operation = Column(String(256), nullable=True)
    nextgen_operation = Column(String(256), nullable=True)
    keycloak_operation = Column(String(256), nullable=True)
    ad_status = Column(String(256), nullable=True)
    aws_cet_status = Column(String(256), nullable=True)
    azure_cet_status = Column(String(256), nullable=True)
    edp_status = Column(String(256), nullable=True)
    nextgen_status = Column(String(256), nullable=True)
    keycloak_status = Column(String(256), nullable=True)
    ad_reason = Column(String(256), nullable=True)
    aws_cet_reason = Column(String(256), nullable=True)
    azure_cet_reason = Column(String(256), nullable=True)
    edp_reason = Column(String(256), nullable=True)
    nextgen_reason = Column(String(256), nullable=True)
    keycloak_reason = Column(String(256), nullable=True)
    resolved_file_id = Column(String(256), nullable=True)
    resolved_file_row_id = Column(String(256), nullable=True)
    is_upn_update = Column(Boolean, default=False)
    file_id = Column(String(256), ForeignKey('process_logs.file_id'))
    is_processed = Column(Boolean, default=False)
    processed_at = Column(DateTime, nullable=True)

    process_log = relationship("ProcessLogs", back_populates="row_logs")

class ADChangeLogHistory(Base):
    __tablename__ = 'ad_change_log_history'

    adm_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(256), nullable=True)
    person_no = Column(String(256), nullable=True)
    # first_name = Column(String(256), nullable=True)
    # last_name = Column(String(256), nullable=True)
    # middle_name = Column(String(256), nullable=True)
    displayname = Column(String(256), nullable=True)
    # title = Column(String(256), nullable=True)
    # other_telephone = Column(String(256), nullable=True)
    office = Column(String(256), nullable=True)
    department = Column(String(256), nullable=True)
    # company = Column(String(256), nullable=True)
    # state = Column(String(256), nullable=True)
    # country = Column(String(256), nullable=True)
    # city = Column(String(256), nullable=True)
    # postal_code = Column(String(256), nullable=True)
    # telephone_number = Column(String(256), nullable=True)
    # mobile = Column(String(256), nullable=True)
    # home_phone = Column(String(256), nullable=True)
    # info = Column(String(256), nullable=True)
    # pager = Column(String(256), nullable=True)
    # extension_attribute1 = Column(String(256), nullable=True)
    # extension_attribute3 = Column(String(256), nullable=True)
    # extension_attribute5 = Column(String(256), nullable=True)
    # extension_attribute9 = Column(String(256), nullable=True)
    # extension_attribute10 = Column(String(256), nullable=True)
    # extension_attribute11 = Column(String(256), nullable=True)
    # extension_attribute12 = Column(String(256), nullable=True)
    # extension_attribute13 = Column(String(256), nullable=True)
    # msexch_assistant_name = Column(String(256), nullable=True)
    # facsimile_telephone_number = Column(String(256), nullable=True)
    # physical_delivery_office_name = Column(String(256), nullable=True)
    address = Column(String(256), nullable=True)
    notes = Column(String(256), nullable=True)
    ou = Column(String(256), nullable=True)
    domain = Column(String(256), nullable=True)
    upn = Column(String(256), nullable=True)
    email = Column(String(256), nullable=True)
    reporting_dn = Column(String(256), nullable=True)
    spoc_per_no = Column(String(256), nullable=True)
    expiry_date = Column(Date, nullable=True)
    initial_password = Column(String(256), nullable=True)
    created_by = Column(String(256), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True, nullable=True)
    is_manual_entry = Column(Boolean, default=True, nullable=True)
    internet_access = Column(Boolean, default=False, nullable=True)
    is_external_email = Column(Boolean, default=False, nullable=True)
    is_internal_migration = Column(Boolean, default=False, nullable=True)
    mailbox_status = Column(Boolean, default=False, nullable=True)
    mailbox_comment = Column(String(256), nullable=True)
    is_sync_excluded = Column(Boolean, default=False, nullable=True)
    incident_no = Column(String(256), nullable=True)
    is_hidden = Column(Boolean, default=False, nullable=True)
    file_id = Column(String(256), nullable=True)
    log_created_at = Column(DateTime, default=datetime.utcnow)
    log_created_by = Column(String(256), nullable=True)
    division = Column(String(256), nullable=True)

    delete_log = relationship("ADDeleteRowLogs", back_populates="ad_change_log")

class ADDeleteLogs(Base):
    __tablename__ = 'ad_delete_logs'

    ad_delete_log_id = Column(String(256), primary_key=True)
    row_count = Column(String(256), nullable=True)
    status = Column(String(256), nullable=True)
    reason = Column(String(256), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    deleted_row_logs = relationship("ADDeleteRowLogs", back_populates="ad_delete_log")


class ADDeleteRowLogs(Base):
    __tablename__ = 'ad_delete_row_logs'

    deleted_row_id = Column(String(256), primary_key=True)
    user_id = Column(String(256), nullable=False)
    ad_change_log_id = Column(
        String(256),
        ForeignKey('ad_change_log_history.adm_id', ondelete="CASCADE"),
        nullable=True
    )
    status = Column(String(256), nullable=True)
    reason = Column(String(256), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    ad_delete_log_id = Column(
        String(256),
        ForeignKey('ad_delete_logs.ad_delete_log_id', ondelete="CASCADE")
    )

    # Relationships
    ad_delete_log = relationship("ADDeleteLogs", back_populates="deleted_row_logs")
    ad_change_log = relationship("ADChangeLogHistory", back_populates="delete_log")

class ADMetaData(Base):
    __tablename__ = 'ad_metadata'

    user_id = Column(String(256), primary_key=True)
    person_no = Column(String(256), ForeignKey('sap_employee_master.person_no'), nullable=True)
    # first_name = Column(String(256), nullable=True)
    # last_name = Column(String(256), nullable=True)
    # middle_name = Column(String(256), nullable=True)
    displayname = Column(String(256), nullable=True)
    # title = Column(String(256), nullable=True)
    # other_telephone = Column(String(256), nullable=True)
    office = Column(String(256), nullable=True)
    department = Column(String(256), nullable=True)
    # company = Column(String(256), nullable=True)
    # state = Column(String(256), nullable=True)
    # country = Column(String(256), nullable=True)
    # city = Column(String(256), nullable=True)
    # postal_code = Column(String(256), nullable=True)
    # telephone_number = Column(String(256), nullable=True)
    # mobile = Column(String(256), nullable=True)
    # home_phone = Column(String(256), nullable=True)
    # info = Column(String(256), nullable=True)
    # pager = Column(String(256), nullable=True)
    # extension_attribute1 = Column(String(256), nullable=True)
    # extension_attribute3 = Column(String(256), nullable=True)
    # extension_attribute5 = Column(String(256), nullable=True)
    # extension_attribute9 = Column(String(256), nullable=True)
    # extension_attribute10 = Column(String(256), nullable=True)
    # extension_attribute11 = Column(String(256), nullable=True)
    # extension_attribute12 = Column(String(256), nullable=True)
    # extension_attribute13 = Column(String(256), nullable=True)
    # msexch_assistant_name = Column(String(256), nullable=True)
    # facsimile_telephone_number = Column(String(256), nullable=True)
    # physical_delivery_office_name = Column(String(256), nullable=True)
    address = Column(String(256), nullable=True)
    notes = Column(String(256), nullable=True)
    ou = Column(String(256), nullable=True)
    domain = Column(String(256), nullable=True)
    upn = Column(String(256), nullable=True)
    email = Column(String(256), nullable=True)
    reporting_dn = Column(String(256), nullable=True)
    spoc_per_no = Column(String(256), nullable=True)
    expiry_date = Column(Date, nullable=True)
    initial_password = Column(String(256), nullable=True)
    created_by = Column(String(256), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True, nullable=True)
    is_manual_entry = Column(Boolean, default=True, nullable=True)
    internet_access = Column(Boolean, default=False, nullable=True)
    is_external_email = Column(Boolean, default=False, nullable=True)
    is_internal_migration = Column(Boolean, default=False, nullable=True)
    mailbox_status = Column(Boolean, default=False, nullable=True)
    mailbox_comment = Column(String(256), nullable=True)
    is_sync_excluded = Column(Boolean, default=False, nullable=True)
    incident_no = Column(String(256), nullable=True)
    is_hidden = Column(Boolean, default=False, nullable=True)
    division = Column(String(256), nullable=True)

    # Relationship to the SapEmployeeMaster model
    # person_no_relation = relationship("SapEmployeeMaster", back_populates="ad_metadata")

class SyncExclusionLog(Base):
    """
    SQLAlchemy model for the SyncExclusionLog table.
    """
    __tablename__ = 'sync_exclusion_logs'

    sync_log_id = Column(String(255), primary_key=True)
    user_id = Column(String(256), nullable=True)  # ForeignKey to ADMetaData
    sap_employee_status = Column(Boolean, default=False)
    sap_expiry_date = Column(DateTime, nullable=True)
    extended_date = Column(DateTime, nullable=True)
    is_extended = Column(Boolean, default=False)
    created_by = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)
    is_sync_enable = Column(Boolean, default=False)
    category = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=False)

class SapFileRowLogTSCMSL(Base):
    
    __tablename__ = 'sap_file_row_log_tscmsl'
    
    sap_file_row_id = Column(String, primary_key=True)
    file_row_id = Column(String, ForeignKey(ProcessRowLogs.file_row_id))
    perno = Column(String)
    response_status = Column(String)
    response_message = Column(String)

class SapEmployeeMaster(Base):
    __tablename__ = 'sap_employee_master'

    person_no = Column(String(256), primary_key=True)
    company_code = Column(String(256), nullable=True)
    company_text = Column(String(256), nullable=True)
    pers_area_code = Column(String(256), nullable=True)
    pers_area_text = Column(String(256), nullable=True)
    pers_subarea_code = Column(String(256), nullable=True)
    pers_subarea_text = Column(String(256), nullable=True)
    employee_group_code = Column(String(256), nullable=True)
    employee_group_text = Column(String(256), nullable=True)
    employee_subgroup_code = Column(String(256), nullable=True)
    employee_subgroup_text = Column(String(256), nullable=True)
    payroll_area_code = Column(String(256), nullable=True)
    payroll_area_text = Column(String(256), nullable=True)
    cost_center_code = Column(String(256), nullable=True)
    cost_center_text = Column(String(256), nullable=True)
    position_code = Column(String(256), nullable=True)
    position_text = Column(String(256), nullable=True)
    job_code = Column(String(256), nullable=True)
    job_text = Column(String(256), nullable=True)
    bus_area_code = Column(String(256), nullable=True)
    bus_area_text = Column(String(256), nullable=True)
    fkbtx = Column(String(256), nullable=True)
    salutation = Column(String(256), nullable=True)
    first_name = Column(String(256), nullable=True)
    middle_name = Column(String(256), nullable=True)
    last_name = Column(String(256), nullable=True)
    full_name = Column(String(256), nullable=True)
    initials = Column(String(256), nullable=True)
    gender = Column(String(256), nullable=True)
    religion_code = Column(String(256), nullable=True)
    religion_text = Column(String(256), nullable=True)
    maritial_status_code = Column(String(256), nullable=True)
    maritial_status_text = Column(String(256), nullable=True)
    blood_group = Column(String(256), nullable=True)
    dob = Column(String(256), nullable=True)
    doj = Column(String(256), nullable=True)
    dobsorp = Column(String(256), nullable=True)
    dsvcvp = Column(String(256), nullable=True)
    dlprom = Column(String(256), nullable=True)
    dosep = Column(String(256), nullable=True)
    seprsn = Column(String(256), nullable=True)
    challenged = Column(String(256), nullable=True)
    reporting_person_no = Column(String(256), nullable=True)
    reporting_fullname = Column(String(256), nullable=True)
    employee_status_code = Column(String(256), nullable=True)
    employee_status_text = Column(String(256), nullable=True)
    org_unit_code = Column(String(256), nullable=True)
    org_unit_text = Column(String(256), nullable=True)
    office_telephone_no = Column(String(256), nullable=True)
    office_mobile_no = Column(String(256), nullable=True)
    personal_mobile_no = Column(String(256), nullable=True)
    emergency_mobile_no = Column(String(256), nullable=True)
    company_email = Column(String(256), nullable=True)
    personal_email = Column(String(256), nullable=True)
    transport_code = Column(String(256), nullable=True)
    office_address = Column(String(256), nullable=True)
    sep_code = Column(String(256), nullable=True)
    function_group_code = Column(String(256), nullable=True)
    function_group_text = Column(String(256), nullable=True)
    hiring_reason = Column(String(256), nullable=True)
    flag1 = Column(String(256), nullable=True)
    last_prom1_date = Column(String(256), nullable=True)
    last_prom2_date = Column(String(256), nullable=True)
    last_prom3_date = Column(String(256), nullable=True)
    dummy1 = Column(String(256), nullable=True)
    dummy2 = Column(String(256), nullable=True)
    dummy3 = Column(String(256), nullable=True)
    dummy4 = Column(String(256), nullable=True)
    dummy5 = Column(String(256), nullable=True)
    bhr_perno = Column(String(256), nullable=True)
    mat1_mngr = Column(String(256), nullable=True)
    mat2_mngr = Column(String(256), nullable=True)
    mat3_mngr = Column(String(256), nullable=True)
    mat4_mngr = Column(String(256), nullable=True)
    mat5_mngr = Column(String(256), nullable=True)
    head_hr_perno = Column(String(256), nullable=True)
    er_hr_perno = Column(String(256), nullable=True)
    ou_short_text = Column(String(256), nullable=True)
    region_code = Column(String(256), nullable=True)
    region_text = Column(String(256), nullable=True)
    jobtxt_short = Column(String(256), nullable=True)
    ou_level1_code = Column(String(256), nullable=True)
    ou_level1_short_text = Column(String(256), nullable=True)
    ou_level1_long_text = Column(String(256), nullable=True)
    ou_level2_code = Column(String(256), nullable=True)
    ou_level2_short_text = Column(String(256), nullable=True)
    ou_level2_long_text = Column(String(256), nullable=True)
    ou_level3_code = Column(String(256), nullable=True)
    ou_level3_short_text = Column(String(256), nullable=True)
    ou_level3_long_text = Column(String(256), nullable=True)
    function_id_code = Column(String(256), nullable=True)
    function_id_text = Column(String(256), nullable=True)
    subfunction_id1_code = Column(String(256), nullable=True)
    subfunction_id1_text = Column(String(256), nullable=True)
    subfunction_id2_code = Column(String(256), nullable=True)
    subfunction_id2_text = Column(String(256), nullable=True)
    location_code = Column(String(256), nullable=True)
    location_text = Column(String(256), nullable=True)
    add1 = Column(String(256), nullable=True)
    add2 = Column(String(256), nullable=True)
    add3 = Column(String(256), nullable=True)
    add4 = Column(String(256), nullable=True)
    city = Column(String(256), nullable=True)
    state = Column(String(256), nullable=True)
    pincode = Column(String(256), nullable=True)
    country = Column(String(256), nullable=True)
    a962_perno = Column(String(256), nullable=True)
    a962_fullname = Column(String(256), nullable=True)

class SapChangeLogHistory(Base):
    __tablename__ = 'sap_change_logs_history'

    chng_hist_id = Column(Integer, primary_key=True, autoincrement=True)
    person_no = Column(String(256), nullable=True)
    company_code = Column(String(256), nullable=True)
    company_text = Column(String(256), nullable=True)
    pers_area_code = Column(String(256), nullable=True)
    pers_area_text = Column(String(256), nullable=True)
    pers_subarea_code = Column(String(256), nullable=True)
    pers_subarea_text = Column(String(256), nullable=True)
    employee_group_code = Column(String(256), nullable=True)
    employee_group_text = Column(String(256), nullable=True)
    employee_subgroup_code = Column(String(256), nullable=True)
    employee_subgroup_text = Column(String(256), nullable=True)
    payroll_area_code = Column(String(256), nullable=True)
    payroll_area_text = Column(String(256), nullable=True)
    cost_center_code = Column(String(256), nullable=True)
    cost_center_text = Column(String(256), nullable=True)
    position_code = Column(String(256), nullable=True)
    position_text = Column(String(256), nullable=True)
    job_code = Column(String(256), nullable=True)
    job_text = Column(String(256), nullable=True)
    bus_area_code = Column(String(256), nullable=True)
    bus_area_text = Column(String(256), nullable=True)
    fkbtx = Column(String(256), nullable=True)
    salutation = Column(String(256), nullable=True)
    first_name = Column(String(256), nullable=True)
    middle_name = Column(String(256), nullable=True)
    last_name = Column(String(256), nullable=True)
    full_name = Column(String(256), nullable=True)
    intials = Column(String(256), nullable=True)
    gender = Column(String(256), nullable=True)
    religion_code = Column(String(256), nullable=True)
    religion_text = Column(String(256), nullable=True)
    maritial_status_code = Column(String(256), nullable=True)
    maritial_status_text = Column(String(256), nullable=True)
    blood_group = Column(String(256), nullable=True)
    dob = Column(String(256), nullable=True)
    doj = Column(String(256), nullable=True)
    dobsorp = Column(String(256), nullable=True)
    dsvcvp = Column(String(256), nullable=True)
    dlprom = Column(String(256), nullable=True)
    dosep = Column(String(256), nullable=True)
    seprsn = Column(String(256), nullable=True)
    challenged = Column(String(256), nullable=True)
    reporting_person_no = Column(String(256), nullable=True)
    reporting_fullname = Column(String(256), nullable=True)
    employee_status_code = Column(String(256), nullable=True)
    employee_status_text = Column(String(256), nullable=True)
    org_unit_code = Column(String(256), nullable=True)
    org_unit_text = Column(String(256), nullable=True)
    office_telephone_no = Column(String(256), nullable=True)
    office_mobile_no = Column(String(256), nullable=True)
    personal_mobile_no = Column(String(256), nullable=True)
    emergency_mobile_no = Column(String(256), nullable=True)
    company_email = Column(String(256), nullable=True)
    personal_email = Column(String(256), nullable=True)
    transport_code = Column(String(256), nullable=True)
    office_address = Column(String(256), nullable=True)
    sep_code = Column(String(256), nullable=True)
    funtion_group_code = Column(String(256), nullable=True)
    function_group_text = Column(String(256), nullable=True)
    hiring_reason = Column(String(256), nullable=True)
    flag1 = Column(String(256), nullable=True)
    last_prom1_date = Column(String(256), nullable=True)
    last_prom2_date = Column(String(256), nullable=True)
    last_prom3_date = Column(String(256), nullable=True)
    dummy1 = Column(String(256), nullable=True)
    dummy2 = Column(String(256), nullable=True)
    dummy3 = Column(String(256), nullable=True)
    dummy4 = Column(String(256), nullable=True)
    dummy5 = Column(String(256), nullable=True)
    bhr_perno = Column(String(256), nullable=True)
    mat1_mngr = Column(String(256), nullable=True)
    mat2_mngr = Column(String(256), nullable=True)
    mat3_mngr = Column(String(256), nullable=True)
    mat4_mngr = Column(String(256), nullable=True)
    mat5_mngr = Column(String(256), nullable=True)
    head_hr_perno = Column(String(256), nullable=True)
    er_hr_perno = Column(String(256), nullable=True)
    ou_short_text = Column(String(256), nullable=True)
    region_code = Column(String(256), nullable=True)
    region_text = Column(String(256), nullable=True)
    jobtxt_short = Column(String(256), nullable=True)
    ou_level1_code = Column(String(256), nullable=True)
    ou_level1_short_text = Column(String(256), nullable=True)
    ou_level1_long_text = Column(String(256), nullable=True)
    ou_level2_code = Column(String(256), nullable=True)
    ou_level2_short_text = Column(String(256), nullable=True)
    ou_level2_long_text = Column(String(256), nullable=True)
    ou_level3_code = Column(String(256), nullable=True)
    ou_level3_short_text = Column(String(256), nullable=True)
    ou_level3_long_text = Column(String(256), nullable=True)
    function_id_code = Column(String(256), nullable=True)
    function_id_text = Column(String(256), nullable=True)
    subfunction_id1_code = Column(String(256), nullable=True)
    subfunction_id1_text = Column(String(256), nullable=True)
    subfunction_id2_code = Column(String(256), nullable=True)
    subfunction_id2_text = Column(String(256), nullable=True)
    location_code = Column(String(256), nullable=True)
    location_text = Column(String(256), nullable=True)
    add1 = Column(String(256), nullable=True)
    add2 = Column(String(256), nullable=True)
    add3 = Column(String(256), nullable=True)
    add4 = Column(String(256), nullable=True)
    city = Column(String(256), nullable=True)
    state = Column(String(256), nullable=True)
    pincode = Column(String(256), nullable=True)
    country = Column(String(256), nullable=True)
    a962_perno = Column(String(256), nullable=True)
    a962_fullname = Column(String(256), nullable=True)
    file_id = Column(String(256), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_manual_entry = Column(Boolean, default=False)
    is_deleted = Column(Boolean, default=False)


class IdSeparationLogs(Base):
    __tablename__ = 'id_separation_logs'

    separation_log_id = Column(String(256), primary_key=True)
    row_count = Column(String(256), nullable=True)
    status = Column(String(256), nullable=True)
    reason = Column(String(256), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship to IdSeparationRowLogs model
    separation_row_logs = relationship("IdSeparationRowLogs", backref="separation_log", cascade="all, delete-orphan")

class IdSeparationRowLogs(Base):
    __tablename__ = 'id_separation_row_logs'

    separation_row_log_id = Column(String(256), primary_key=True)
    perno = Column(String(256), nullable=True)
    sap_id = Column(String(256), nullable=True)
    separation_status = Column(String(256), nullable=True)
    separation_date = Column(DateTime, nullable=True)
    is_deleted = Column(Boolean, default=False)
    reason = Column(String(256), nullable=True)
    status = Column(String(256), nullable=True)

    # Foreign key to IdSeparationLogs
    separation_log_id = Column(String(256), ForeignKey('id_separation_logs.separation_log_id'), nullable=True)

class ManualTempTable(Base):
    __tablename__ = 'manual_temp_table'

    perno = Column(String(256), primary_key=True)
    comp_code = Column(String(256), nullable=True)
    pers_area = Column(String(256), nullable=True)
    patxt = Column(String(256), nullable=True)
    egroup = Column(String(256), nullable=True)
    ptext = Column(String(256), nullable=True)
    esubgroup = Column(String(256), nullable=True)
    esgtxt = Column(String(256), nullable=True)
    ccodetxt = Column(String(256), nullable=True)
    bus_area = Column(String(256), nullable=True)
    bustxt = Column(String(256), nullable=True)
    p_subarea = Column(String(256), nullable=True)
    psatxt = Column(String(256), nullable=True)
    payarea = Column(String(256), nullable=True)
    paytxt = Column(String(256), nullable=True)
    costcenter = Column(String(256), nullable=True)
    cosctrtxt = Column(String(256), nullable=True)
    position1 = Column(String(256), nullable=True)
    postxt = Column(String(256), nullable=True)
    job = Column(String(256), nullable=True)
    jobtxt = Column(String(256), nullable=True)
    fkbtx = Column(String(256), nullable=True)
    initials = Column(String(256), nullable=True)
    comp_name = Column(String(256), nullable=True)
    last_name = Column(String(256), nullable=True)
    firstname = Column(String(256), nullable=True)
    midnm = Column(String(256), nullable=True)
    title = Column(String(256), nullable=True)
    off_num = Column(String(256), nullable=True)
    gender = Column(String(256), nullable=True)
    konfe = Column(String(256), nullable=True)
    religion = Column(String(256), nullable=True)
    famst = Column(String(256), nullable=True)
    mar_status = Column(String(256), nullable=True)
    bloodgroup = Column(String(256), nullable=True)
    imailid = Column(String(256), nullable=True)
    exmailid = Column(String(256), nullable=True)
    dobsorp = Column(String(256), nullable=True)
    doj = Column(String(256), nullable=True)
    dob = Column(String(256), nullable=True)
    dsvcvp = Column(String(256), nullable=True)
    dlprom = Column(String(256), nullable=True)
    dosep = Column(String(256), nullable=True)
    seprsn = Column(String(256), nullable=True)
    challenged = Column(String(256), nullable=True)
    reporting = Column(String(256), nullable=True)
    nameofreporting = Column(String(256), nullable=True)
    empstatus = Column(String(256), nullable=True)
    empstattxt = Column(String(256), nullable=True)
    orgeh = Column(String(256), nullable=True)
    orgtx = Column(String(256), nullable=True)
    office_mobile = Column(String(256), nullable=True)
    pers_mobile = Column(String(256), nullable=True)
    emrg_mobile = Column(String(256), nullable=True)
    transport_code = Column(String(256), nullable=True)
    off_addr = Column(String(256), nullable=True)
    sepcode = Column(String(256), nullable=True)
    fun = Column(String(256), nullable=True)
    funt = Column(String(256), nullable=True)
    mgtxt = Column(String(256), nullable=True)
    flag1 = Column(String(256), nullable=True)
    last_prom1 = Column(String(256), nullable=True)
    last_prom2 = Column(String(256), nullable=True)
    last_prom3 = Column(String(256), nullable=True)
    dummy1 = Column(String(256), nullable=True)
    dummy2 = Column(String(256), nullable=True)
    dummy3 = Column(String(256), nullable=True)
    dummy4 = Column(String(256), nullable=True)
    dummy5 = Column(String(256), nullable=True)
    bhr_perno = Column(String(256), nullable=True)
    mat1_mngr = Column(String(256), nullable=True)
    mat2_mngr = Column(String(256), nullable=True)
    mat3_mngr = Column(String(256), nullable=True)
    mat4_mngr = Column(String(256), nullable=True)
    mat5_mngr = Column(String(256), nullable=True)
    head_hr_perno = Column(String(256), nullable=True)
    er_hr_perno = Column(String(256), nullable=True)
    ou_short_txt = Column(String(256), nullable=True)
    reg_cod = Column(String(256), nullable=True)
    reg_cod_txt = Column(String(256), nullable=True)
    jobtxt_short = Column(String(256), nullable=True)
    ou_level1 = Column(String(256), nullable=True)
    ou_level1_short_txt = Column(String(256), nullable=True)
    ou_level1_long_txt = Column(String(256), nullable=True)
    ou_level2 = Column(String(256), nullable=True)
    ou_level2_short_txt = Column(String(256), nullable=True)
    ou_level2_long_txt = Column(String(256), nullable=True)
    ou_level3 = Column(String(256), nullable=True)
    ou_level3_short_txt = Column(String(256), nullable=True)
    ou_level3_long_txt = Column(String(256), nullable=True)
    func_id01 = Column(String(256), nullable=True)
    function_text = Column(String(256), nullable=True)
    subfunction1_id = Column(String(256), nullable=True)
    subfunction1_tex = Column(String(256), nullable=True)
    subfunction2_id = Column(String(256), nullable=True)
    subfunction2_tex = Column(String(256), nullable=True)
    loc_cod = Column(String(256), nullable=True)
    loc_desc = Column(String(256), nullable=True)
    add1 = Column(String(256), nullable=True)
    add2 = Column(String(256), nullable=True)
    add3 = Column(String(256), nullable=True)
    add4 = Column(String(256), nullable=True)
    city_town = Column(String(256), nullable=True)
    state = Column(String(256), nullable=True)
    pin_code = Column(String(256), nullable=True)
    country = Column(String(256), nullable=True)
    a962_perno = Column(String(256), nullable=True)
    a962_cname = Column(String(256), nullable=True)
    incident_no = Column(String(256), nullable=True)
    created_by = Column(String(256), nullable=True)

class Pa(Base):
    __tablename__ = 'pa_table'

    id = Column(Integer, primary_key=True)
    pa_code = Column(String(256))
    pa_text = Column(String(256))
    company_code = Column(String(256), default="100")
    is_deleted = Column(Boolean, default=False)

class Psa(Base):
    __tablename__ = 'psa_table'

    id = Column(Integer, primary_key=True)
    pa_id = Column(Integer, ForeignKey('pa_table.id'))
    psa_code = Column(String(256))
    psa_text = Column(String(256))
    imac_location_id = Column(Integer, ForeignKey('imac_locations.iloc_id'), nullable=True, name='imac_location')
    is_deleted = Column(Boolean, default=False)

    # Relationship to Pa and ImacLocations
    pa = relationship("Pa")
    imac_location = relationship("ImacLocations")
    
class ImacLocations(Base):
    __tablename__ = 'imac_locations'

    iloc_id = Column(Integer, primary_key=True)
    iloc_name = Column(String(256))
    site = Column(String(256), nullable=True)
    is_deleted = Column(Boolean, default=False)

class IMAC_Logs(Base):
    __tablename__ = 'imac_logs'

    imac_log_id = Column(String(256), primary_key=True)
    user_id = Column(String(256), nullable=True)
    report_to = Column(String(256), nullable=True)
    imac_ticket_id = Column(String(256))
    imac_status = Column(String(256))
    imac_comments = Column(String(256), nullable=True)
    imac_ticket_status = Column(String(256), nullable=True)
    imac_location_id = Column(Integer, ForeignKey('imac_locations.iloc_id'), nullable=True, name='imac_location')
    imac_company_text = Column(String(256), nullable=True)
    asset_type = Column(String(256), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False)

    # Relationship to ImacLocations
    imac_location = relationship("ImacLocations")