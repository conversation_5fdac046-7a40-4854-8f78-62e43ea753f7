# In-built imports...
import time
from datetime import datetime, timedelta
# Custom imports...
from commons.db_connections import create_new_oracle_session, create_oracle_session
from commons.configs import CREATED_BY, DATA_NOT_FOUND
from commons.messages import SUCCE<PERSON>, FAIL<PERSON>, NO_<PERSON>ATA_FOUND, FAIL<PERSON>
from commons.external_interface import delete_user_from_AD
# from commons.models import ADMetaData, ADChangeLogHistory, ADDeleteLogs, ADDeleteRowLogs
from commons.models_v2 import ADMetaData, ADChangeLogHistory, ADDeleteLogs, ADDeleteRowLogs

# def dag2_delete_disabled_task():
#     oracle_session = create_oracle_session()
#     five_weeks_ago = datetime.now() - timedelta(weeks=5)
#     one_hour_ago = datetime.now() - timedelta(hours=1)
#     # Get all disable employee list..
#     sap_disable_emp = oracle_session.query(ADMetaData).filter(ADMetaData.is_active == False, ADMetaData.is_sync_excluded == False, ADMetaData.expiry_date <= five_weeks_ago).all()
#     try:
#         # Create default delete log object....
#         ad_delete_log = ADDeleteLogs()
#         ad_delete_log.ad_delete_log_id = "DEL"+str(int(time.time_ns() * 10))
#         ad_delete_log.row_count = len(sap_disable_emp)
#         ad_delete_log.status = SUCCESS

#         if len(sap_disable_emp):
#             for data in sap_disable_emp:
#                 # Delete user from AD...
#                 ad_delete_row_log = ADDeleteRowLogs()
#                 ad_delete_row_log.deleted_row_id = "ROW"+str(int(time.time_ns() * 10))
#                 ad_delete_row_log.user_id = data.user_id
#                 ad_delete_row_log.ad_delete_log_id = ad_delete_log.ad_delete_log_id

#                 status, message = delete_user_from_AD(data.user_id)
#                 if status:
#                     print('Deleted User ID -',data.user_id)
#                     # Maintain the AD change logs...
#                     sap_change_log = ADChangeLogHistory()
#                     sap_change_log.user_id = data.user_id
#                     sap_change_log.person_no = data.person_no
#                     sap_change_log.displayname = data.displayname
#                     sap_change_log.office = data.office
#                     sap_change_log.department = data.department
#                     sap_change_log.address = data.address
#                     sap_change_log.notes = data.notes
#                     sap_change_log.ou = data.ou
#                     sap_change_log.domain = data.domain
#                     sap_change_log.upn = data.upn
#                     sap_change_log.email = data.email
#                     sap_change_log.reporting_dn = data.reporting_dn
#                     sap_change_log.expiry_date = data.expiry_date
#                     sap_change_log.initial_password = data.initial_password
#                     sap_change_log.created_by = data.created_by
#                     sap_change_log.created_at = data.created_at
#                     sap_change_log.updated_at = data.updated_at
#                     sap_change_log.is_deleted = True
#                     sap_change_log.is_active = data.is_active
#                     sap_change_log.is_manual_entry = data.is_manual_entry
#                     sap_change_log.internet_access = data.internet_access
#                     sap_change_log.is_external_email = data.is_external_email
#                     sap_change_log.file_id = ad_delete_log.ad_delete_log_id
#                     sap_change_log.log_created_at = datetime.now()
#                     sap_change_log.log_created_by = CREATED_BY
#                     oracle_session.add(sap_change_log)
#                     oracle_session.commit()

#                     # delete from ad_metadata...
#                     oracle_session.delete(data)
#                     oracle_session.commit()

#                     ad_delete_row_log.status = SUCCESS
#                     ad_delete_row_log.reason = None
#                 else:
#                     if message == DATA_NOT_FOUND:
#                         # copy to change log history...
#                         ad_change_log = ADChangeLogHistory()
#                         ad_change_log.user_id = data.user_id
#                         ad_change_log.person_no = data.person_no
#                         ad_change_log.displayname = data.displayname
#                         ad_change_log.office = data.office
#                         ad_change_log.department = data.department
#                         ad_change_log.address = data.address
#                         ad_change_log.notes = data.notes
#                         ad_change_log.ou = data.ou
#                         ad_change_log.domain = data.domain
#                         ad_change_log.upn = data.upn
#                         ad_change_log.email = data.email
#                         ad_change_log.reporting_dn = data.reporting_dn
#                         ad_change_log.expiry_date = data.expiry_date
#                         ad_change_log.initial_password = data.initial_password
#                         ad_change_log.created_by = data.created_by
#                         ad_change_log.created_at = data.created_at
#                         ad_change_log.updated_at = data.updated_at
#                         ad_change_log.is_deleted = True
#                         ad_change_log.is_active = data.is_active
#                         ad_change_log.is_manual_entry = data.is_manual_entry
#                         ad_change_log.internet_access = data.internet_access
#                         ad_change_log.is_external_email = data.is_external_email
#                         ad_change_log.file_id = ad_delete_log.ad_delete_log_id
#                         ad_change_log.log_created_at = datetime.now()
#                         ad_change_log.log_created_by = CREATED_BY
#                         oracle_session.add(ad_change_log)
#                         oracle_session.commit()

#                         # delete from ad_metadata...
#                         oracle_session.delete(data)
#                         oracle_session.commit()
                        
#                     print('Not Deleted User ID -',data.user_id,' Reason - ',message)
#                     ad_delete_row_log.status = FAILED
#                     ad_delete_row_log.reason = message

#                 # Insert the deleted records in AD deleted row logs...
#                 ad_delete_row_log.created_at = datetime.now()
#                 oracle_session.add(ad_delete_row_log)
#                 oracle_session.commit() 
#         else:
#             ad_delete_log.reason = NO_DATA_FOUND
#     except Exception as error:
#         print("dag2_delete_disabled_task() : ", error)
#         ad_delete_log.status = FAILED
#         ad_delete_log.reason = str(error)[:30]
#     ad_delete_log.created_at = datetime.now()
#     oracle_session.add(ad_delete_log) 
#     oracle_session.commit()
#     return ad_delete_log.ad_delete_log_id

def dag2_delete_disabled_task():
    # oracle_session = create_oracle_session()
    new_oracle_session = create_new_oracle_session()
    five_weeks_ago = datetime.now() - timedelta(weeks=5)

    sap_disable_emp = new_oracle_session.query(ADMetaData).filter(
        ADMetaData.is_active == False,
        ADMetaData.is_sync_excluded == False,
        ADMetaData.expiry_date <= five_weeks_ago
    ).all()

    try:
        ad_delete_log = ADDeleteLogs(
            ad_delete_log_id="DEL" + str(int(time.time_ns() * 10)),
            row_count=len(sap_disable_emp),
            status=SUCCESS,
            created_at=datetime.now()
        )
        new_oracle_session.add(ad_delete_log)

        if sap_disable_emp:
            for data in sap_disable_emp:
                ad_delete_row_log = ADDeleteRowLogs(
                    deleted_row_id="ROW" + str(int(time.time_ns() * 10)),
                    user_id=data.user_id,
                    ad_delete_log=ad_delete_log,  # ORM relationship
                    created_at=datetime.now()
                )

                status, message = delete_user_from_AD(data.user_id)
                if status:
                    print('Deleted User ID -', data.user_id)
                    sap_change_log = ADChangeLogHistory(
                        user_id=data.user_id,
                        person_no=data.person_no,
                        displayname=data.displayname,
                        division=data.division,
                        office=data.office,
                        department=data.department,
                        address=data.address,
                        notes=data.notes,
                        ou=data.ou,
                        domain=data.domain,
                        upn=data.upn,
                        email=data.email,
                        reporting_dn=data.reporting_dn,
                        expiry_date=data.expiry_date,
                        initial_password=data.initial_password,
                        created_by=data.created_by,
                        created_at=data.created_at,
                        updated_at=data.updated_at,
                        is_deleted=True,
                        is_active=data.is_active,
                        is_manual_entry=data.is_manual_entry,
                        internet_access=data.internet_access,
                        is_external_email=data.is_external_email,
                        file_id=ad_delete_row_log.deleted_row_id,  # Relationship
                        log_created_at=datetime.now(),
                        log_created_by=CREATED_BY
                    )
                    new_oracle_session.add(sap_change_log)
                    new_oracle_session.delete(data)
                    ad_delete_row_log.ad_change_log_id = sap_change_log.adm_id
                    ad_delete_row_log.status = SUCCESS
                else:
                    ad_delete_row_log.status = FAILED
                    ad_delete_row_log.reason = message

                new_oracle_session.add(ad_delete_row_log)

        else:
            ad_delete_log.status = FAILED
            ad_delete_log.reason = NO_DATA_FOUND

        new_oracle_session.commit()
    except Exception as error:
        print("dag2_delete_disabled_task() : ", error)
        ad_delete_log.status = FAILED
        ad_delete_log.reason = str(error)[:30]
        new_oracle_session.add(ad_delete_log)
        new_oracle_session.commit()

    return ad_delete_log.ad_delete_log_id

