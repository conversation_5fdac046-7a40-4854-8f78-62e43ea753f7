# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# from airflow.providers.sftp.sensors.sftp import SFTPSensor
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
# Custom imports...
from commons.configs import  DAG12_NDA_SCHEDULED_JOBS, DAG12_NDA_NDA_AUTOSYNC_SCHEDULER, NDA_ID_CREATION, LOAD_TO_AD_CET, TRIGGER_ID, DAG2_DELETE_JOBS
from scripts.scheduled_job import nda_domain_id_creation 


# DAG initialization...
with DAG(
    dag_id = DAG12_NDA_SCHEDULED_JOBS,
    start_date = datetime(2024, 3, 1),
    schedule_interval = DAG12_NDA_NDA_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    nda_domain_creation = PythonOperator(
        task_id = NDA_ID_CREATION,
        python_callable = nda_domain_id_creation,
        do_xcom_push = True    
    )
    
    # Task sequencing & call...
    nda_domain_creation