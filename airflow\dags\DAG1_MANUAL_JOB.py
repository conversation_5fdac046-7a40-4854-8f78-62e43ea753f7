# In-built Imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom Imports...
from commons.configs import DAG1_MANUAL_JOBS, DAG1_MANUAL_JOB_SCHEDULER, AD, AWS, AZURE, BLUECOLLAR, EDP, LOAD_TO_AD, LOAD_TO_AWS_CET, LOAD_TO_AZURE_CET, LOAD_TO_BLUECOLLAR_KEYCLOACK, LOAD_TO_EDP_CET, LOAD_TO_NEXTGEN_CET, LOAD_TO_SAP, LOAD_TO_TEMP, NEXTGEN, SAP, UPDATE_UPN_AND_SEND_EMAIL
from scripts.manual_job import create_manual_file_log, manual_load_to_cet_ad_
from scripts.scheduled_job import update_upn_and_send_email



# DAG initialization...
with DAG(
    dag_id = DAG1_MANUAL_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG1_MANUAL_JOB_SCHEDULER,
    catchup = False,
) as dag:
    
    load_to_temp_ = PythonOperator(
        task_id = LOAD_TO_TEMP,
        python_callable = create_manual_file_log,
        do_xcom_push = True
    )
    
    load_to_sap = PythonOperator(
        task_id = LOAD_TO_SAP,
        python_callable = manual_load_to_cet_ad_,
        op_kwargs={"cet": SAP},
        do_xcom_push = True
    )
    
    load_to_ad = PythonOperator(
        task_id = LOAD_TO_AD,
        python_callable = manual_load_to_cet_ad_,
        op_kwargs={"cet": AD},
        do_xcom_push = True    
    )
    
    # Task 2 Initialization...
    load_to_azure_cet = PythonOperator(
        task_id = LOAD_TO_AZURE_CET,
        python_callable = manual_load_to_cet_ad_,
        op_kwargs={"cet": AZURE},
        do_xcom_push = True    
    )
    
    load_to_edp_cet = PythonOperator(
        task_id = LOAD_TO_EDP_CET,
        python_callable = manual_load_to_cet_ad_,
        op_kwargs={"cet": EDP},
        do_xcom_push = True    
    )
    
    load_to_nextgen_cet = PythonOperator(
        task_id = LOAD_TO_NEXTGEN_CET,
        python_callable = manual_load_to_cet_ad_,
        op_kwargs={"cet": NEXTGEN},
        do_xcom_push = True    
    )
    
    load_to_blue_collar_keycloak = PythonOperator(
        task_id = LOAD_TO_BLUECOLLAR_KEYCLOACK,
        python_callable = manual_load_to_cet_ad_,
        op_kwargs={"cet": BLUECOLLAR},
        do_xcom_push = True    
    )
    
    update_upn_and_send_email_task = PythonOperator(
        task_id = UPDATE_UPN_AND_SEND_EMAIL,
        python_callable = update_upn_and_send_email,
        do_xcom_push = True    
    )
    
    load_to_temp_ >> load_to_sap >> [load_to_ad, load_to_azure_cet, load_to_edp_cet, load_to_nextgen_cet, load_to_blue_collar_keycloak] >> update_upn_and_send_email_task