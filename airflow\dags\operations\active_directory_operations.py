# In-built imports...
import time, requests, json, string
from datetime import datetime
from dateutil.relativedelta import relativedelta
import calendar
# Custom imports...
from commons import configs
from commons import messages
from commons import common_functions
# from commons.models import ADMetaData, ADChangeLogHistory, ESubGroup, ESubGroupTMML, IMAC_logs, PaPsa
from commons.models_v2 import ADMetaData, ADChangeLogHistory, ESubGroup, ESubGroupTMML, IMAC_Logs, Pa, Psa

# Active Directory Sync
def sync_record_in_active_directory(oracle_session, record, file_id):
    try:
        status = None
        message = None
        operation = None
        upn_status = None
        result = get_ad_data_by_perno(record.perno)
        if result:
            operation = messages.UPDATED
            metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
            if not metadata_result:
                metadata_result = ADMetaData()
                metadata_result.user_id = result['samAccountName'].upper()
                metadata_result.person_no = record.perno
                metadata_result.displayname = result['DisplayName']
                metadata_result.division = result['division']
                metadata_result.office = result['physicalDeliveryOfficeName']
                metadata_result.department = result['department']
                metadata_result.address = result['streetAddress']
                metadata_result.notes = result['info']
                metadata_result.ou = result['ou']
                metadata_result.domain = configs.DOMAIN
                metadata_result.upn = result['userPrincipalName']
                metadata_result.email = result['mail']
                metadata_result.reporting_dn = result['manager']
                metadata_result.expiry_date = datetime.strptime(result['accountExpires'][:19], '%Y-%m-%d %H:%M:%S')
                metadata_result.initial_password = None
                metadata_result.created_by = configs.CREATED_BY
                metadata_result.created_at = datetime.strptime(result['whenCreated'][:19], '%Y-%m-%d %H:%M:%S')
                metadata_result.updated_at = datetime.strptime(result['whenChanged'][:19], '%Y-%m-%d %H:%M:%S')
                metadata_result.is_deleted = False
                metadata_result.is_active = result['is_active']
                metadata_result.is_manual_entry = False
                metadata_result.internet_access = False if record.comp_code == configs.TMML_CO_CODE else True
                metadata_result.is_external_email = False
                metadata_result.mailbox_status = True
                metadata_result.is_internal_migration = False
                oracle_session.add(metadata_result)
                oracle_session.commit()
            print("""Valid Subgroup""")
            if metadata_result.is_sync_excluded:
                print("""Sync Excluded""")
                operation = messages.SKIP
                status = messages.SUCCESS
                message = f'Sync excluded'
            elif record.empstatus == "0":
                if metadata_result.is_active == True:
                    print("""DISABLE TO AD""")
                    operation = messages.DISABLE
                    status, attributes = disable_employee(result['samAccountName'])
                    if status:
                        metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
                        if metadata_result:
                            ad_change_log = ADChangeLogHistory()
                            ad_change_log.user_id = metadata_result.user_id
                            ad_change_log.person_no = metadata_result.person_no
                            ad_change_log.displayname = metadata_result.displayname
                            ad_change_log.division = metadata_result.division
                            ad_change_log.office = metadata_result.office
                            ad_change_log.department = metadata_result.department
                            ad_change_log.address = metadata_result.address
                            ad_change_log.notes = metadata_result.notes
                            ad_change_log.ou = metadata_result.ou
                            ad_change_log.domain = metadata_result.domain
                            ad_change_log.upn = metadata_result.upn
                            ad_change_log.email = metadata_result.email
                            ad_change_log.reporting_dn = metadata_result.reporting_dn
                            ad_change_log.expiry_date = metadata_result.expiry_date
                            ad_change_log.initial_password = metadata_result.initial_password
                            ad_change_log.created_by = metadata_result.created_by
                            ad_change_log.created_at = metadata_result.created_at
                            ad_change_log.updated_at = metadata_result.updated_at
                            ad_change_log.is_deleted = metadata_result.is_deleted
                            ad_change_log.is_active = metadata_result.is_active
                            ad_change_log.is_manual_entry = metadata_result.is_manual_entry
                            ad_change_log.internet_access = metadata_result.internet_access
                            ad_change_log.is_external_email = metadata_result.is_external_email
                            ad_change_log.is_internal_migration = metadata_result.is_internal_migration
                            ad_change_log.file_id = file_id
                            ad_change_log.log_created_at = datetime.now()
                            ad_change_log.log_created_by = configs.LOG_CREATED_BY
                            ad_change_log.spoc_per_no = metadata_result.spoc_per_no
                            oracle_session.add(ad_change_log)
                            oracle_session.commit()

                            metadata_result.expiry_date = datetime.strptime(attributes['accountExpires'], '%Y-%m-%d %H:%M:%S')
                            metadata_result.updated_at = datetime.now()
                            metadata_result.is_sync_excluded = False
                            metadata_result.is_manual_entry = False
                            metadata_result.is_active = False
                            oracle_session.commit()

                            status = messages.SUCCESS
                            message = None
                    else:
                        print("""Disable Failed""")
                        status = messages.FAILED
                        message = 'AD Failed'
                else:
                    print("""Already Disabled""")
                    operation = messages.SKIP
                    status = messages.SUCCESS
                    message = f'already disabled on {str(metadata_result.expiry_date)[:10]}'
            else:
                print("""UPDATE TO AD""")

                """Update UPN and Email"""
                if not record.imailid:
                    if not update_upn(record.perno,record.comp_code,result['userPrincipalName'],result['mail']):
                        print('UPN Update Failed')
                        upn_status = False
                    else:
                        upn_status = True
                        record.imailid = result['mail']

                operation = messages.UPDATED
                status, attributes = update_employee(result,record)
                if status:
                    metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
                    if metadata_result:
                        ad_change_log = ADChangeLogHistory()
                        ad_change_log.user_id = metadata_result.user_id
                        ad_change_log.person_no = metadata_result.person_no
                        ad_change_log.displayname = metadata_result.displayname
                        ad_change_log.division = metadata_result.division
                        ad_change_log.office = metadata_result.office
                        ad_change_log.department = metadata_result.department
                        ad_change_log.address = metadata_result.address
                        ad_change_log.notes = metadata_result.notes
                        ad_change_log.ou = metadata_result.ou
                        ad_change_log.domain = metadata_result.domain
                        ad_change_log.upn = metadata_result.upn
                        ad_change_log.email = metadata_result.email
                        ad_change_log.reporting_dn = metadata_result.reporting_dn
                        ad_change_log.expiry_date = metadata_result.expiry_date
                        ad_change_log.initial_password = metadata_result.initial_password
                        ad_change_log.created_by = metadata_result.created_by
                        ad_change_log.created_at = metadata_result.created_at
                        ad_change_log.updated_at = metadata_result.updated_at
                        ad_change_log.is_deleted = metadata_result.is_deleted
                        ad_change_log.is_active = metadata_result.is_active
                        ad_change_log.is_manual_entry = metadata_result.is_manual_entry
                        ad_change_log.internet_access = metadata_result.internet_access
                        ad_change_log.is_external_email = metadata_result.is_external_email
                        ad_change_log.is_internal_migration = metadata_result.is_internal_migration
                        ad_change_log.file_id = file_id
                        ad_change_log.log_created_at = datetime.now()
                        ad_change_log.log_created_by = configs.LOG_CREATED_BY
                        ad_change_log.spoc_per_no = metadata_result.spoc_per_no
                        oracle_session.add(ad_change_log)
                        oracle_session.commit()
                        metadata_result.user_id = attributes['samAccountName'].upper() if attributes.get('samAccountName') else None
                        metadata_result.person_no = attributes['EmployeeID'] if attributes.get('EmployeeID') else None
                        metadata_result.displayname = attributes['DisplayName'] if attributes.get('DisplayName') else None
                        metadata_result.division = attributes['division'] if attributes.get('division') else None
                        metadata_result.office = attributes['physicalDeliveryOfficeName'] if attributes.get('physicalDeliveryOfficeName') else None
                        metadata_result.department = attributes['department'] if attributes.get('department') else None
                        metadata_result.address = attributes['streetAddress'] if attributes.get('streetAddress') else None
                        metadata_result.notes = attributes['info'] if attributes.get('info') else None
                        if attributes.get('ou'):
                            metadata_result.ou = attributes['ou']
                        metadata_result.domain = configs.DOMAIN
                        metadata_result.upn = attributes['userPrincipalName'] if attributes.get('userPrincipalName') else None
                        metadata_result.email = attributes['mail'] if attributes.get('mail') else None
                        metadata_result.reporting_dn = attributes['manager'] if attributes.get('manager') else None
                        metadata_result.expiry_date = datetime.strptime(attributes['accountExpires'], '%Y-%m-%d %H:%M:%S')
                        metadata_result.updated_at = datetime.now()
                        metadata_result.is_manual_entry = False
                        metadata_result.is_active = True
                        metadata_result.is_sync_excluded = False
                        oracle_session.commit()

                        status = messages.SUCCESS
                        message = None

                else:
                    print("""Update Failed""")
                    status = messages.FAILED
                    message = 'AD Failed'
        else:
            print("""INSERT TO AD""")
            operation = messages.INSERTED
            is_valid = None
            if record.incident_no is not None:
                is_valid = True
            else:
                is_valid = oracle_session.query(ESubGroup).filter(ESubGroup.esubgroup_code == record.esubgroup, ESubGroup.esubgroup_type == True).first() if record.comp_code != configs.TMML_CO_CODE else oracle_session.query(ESubGroupTMML).filter(ESubGroupTMML.esubgroup_code == record.esubgroup, ESubGroupTMML.esubgroup_type == True).first()
            if is_valid:
                if record.empstatus == "0":
                    print("""Disabled at time of insert""")
                    operation = messages.SKIP
                    status = messages.SUCCESS
                    message = "Disabled at time of insert"
                else:
                    status, attributes = insert_employee(record)
                    if status:
                        metadata_result = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == record.perno).first()
                        is_present = True
                        if not metadata_result:
                            is_present = False
                            metadata_result = ADMetaData()
                        metadata_result.user_id = attributes['samAccountName'] if attributes.get('samAccountName') else None
                        metadata_result.person_no = attributes.get('EmployeeID')  if attributes.get('EmployeeID') else None
                        metadata_result.displayname = attributes['DisplayName'] if attributes.get('DisplayName') else None
                        metadata_result.division = attributes['division'] if attributes.get('division') else None
                        metadata_result.office = attributes['physicalDeliveryOfficeName'] if attributes.get('physicalDeliveryOfficeName') else None
                        metadata_result.department = attributes['department'] if attributes.get('department') else None
                        metadata_result.address = attributes['streetAddress'] if attributes.get('streetAddress') else None
                        metadata_result.notes = attributes['info'] if attributes.get('info') else None
                        metadata_result.ou = attributes['ou'] if attributes.get('ou') else None
                        metadata_result.domain = configs.DOMAIN
                        metadata_result.upn = attributes['userPrincipalName'] if attributes.get('userPrincipalName') else None
                        metadata_result.email = attributes['mail'] if attributes.get('mail') else None
                        metadata_result.reporting_dn = attributes['manager'] if attributes.get('manager') else None
                        metadata_result.expiry_date = datetime.strptime(attributes['accountExpires'], '%Y-%m-%d %H:%M:%S')
                        metadata_result.initial_password = common_functions.encrypt_content(attributes['passwd'])
                        metadata_result.created_by = configs.CREATED_BY if record.created_by is None else record.created_by
                        metadata_result.created_at = datetime.now()
                        metadata_result.updated_at = datetime.now()
                        metadata_result.is_deleted = False
                        metadata_result.is_active = True
                        metadata_result.is_manual_entry = False
                        metadata_result.internet_access = False if record.comp_code == configs.TMML_CO_CODE else True
                        metadata_result.is_external_email = False
                        metadata_result.mailbox_status = False
                        metadata_result.is_internal_migration = False
                        metadata_result.incident_no = record.incident_no
                        if is_present == False:
                            oracle_session.add(metadata_result)
                        oracle_session.commit()

                        status = messages.SUCCESS
                        message = None

                        """Update UPN and Email"""
                        if not record.imailid:
                            if not update_upn(record.perno,record.comp_code,attributes['userPrincipalName'],attributes['mail']):
                                print('UPN Update Failed')
                                upn_status = False
                            else:
                                upn_status = True
                        if record.incident_no is None:
                            if not record.pers_area == 'ERCU' and record.comp_code != configs.TMML_CO_CODE:
                                imac_status = update_imac_logs(oracle_session,attributes['samAccountName'],record.reporting,record.ccodetxt,record.patxt,record.esubgroup)
                                if not imac_status:
                                    print("IMAC failed")
                    else:
                        print("""Insert Failed""")
                        status = messages.FAILED
                        message = 'AD Failed'
            else:
                print("""Invalid Subgroup""")
                operation = messages.SKIP
                status = messages.SUCCESS
                message = f'{messages.INVALID_SUBGROUP} - {record.esubgroup}'
    except Exception as error:
        oracle_session.rollback()
        common_functions.print_error('sync_record_in_active_directory',error)
        oracle_session.rollback()
        status = messages.FAILED
        message = str(error)[:100]
    return operation, status, message, upn_status

def get_ad_data_by_perno(perno):
    try:
        body = {**configs.REQUEST_BODY}
        body['extensionAttribute5'] = perno
        if 'attributes' in body:
            del body['attributes']
        response = requests.get(configs.AD_LDAP_USER_URL,headers=configs.HEADER,params=body)
        if response.status_code == 200:
            ad_data = json.loads(response.content)
            if 'result' in ad_data and len(ad_data['result'])> 0:
                data = ad_data['result'][0]
                ad_data = data
                data['samAccountName'] = ad_data['sAMAccountName'] if ad_data.get('sAMAccountName') else None
                data['EmployeeID'] = ad_data['extensionAttribute5'] if ad_data.get('extensionAttribute5') else None
                data['DisplayName'] = ad_data['displayName'] if ad_data.get('displayName') else None
                data['division'] = ad_data['division'] if ad_data.get('division') else None
                data['physicalDeliveryOfficeName'] = ad_data['physicalDeliveryOfficeName'] if ad_data.get('physicalDeliveryOfficeName') else None
                data['department'] = ad_data['department'] if ad_data.get('department') else None
                data['streetAddress'] = ad_data['streetAddress'] if ad_data.get('streetAddress') else None
                data['info'] = ad_data['info'] if ad_data.get('info') else None
                data['ou'] = generate_ou_from_dn(ad_data.get('distinguishedName'))
                data['userPrincipalName'] = ad_data['userPrincipalName'] if ad_data.get('userPrincipalName') else None
                data['mail'] = ad_data['mail'] if ad_data.get('mail') else None
                data['manager'] = ad_data['manager'] if ad_data.get('manager') else None
                data['accountExpires'], _ = common_functions.format_date(ad_data['accountExpires'] if ad_data.get('accountExpires') else None)
                data['password'] = 'abc1234ABC'
                data['is_active'] = ad_data['is_active'] if ad_data.get('is_active')!= None else None
                return data
            # print('get_ad_data_by_perno body - ',body)
            # print('get_ad_data_by_perno response - ',ad_data)
            return None
        else:
            print('get_ad_data_by_perno status code - ',response.status_code)
            return None
    except Exception as error:
        common_functions.print_error(error)
        raise Exception(error)

def delete_to_ad(adid):
    try:
        body = {**configs.REQUEST_BODY}
        body['sAMAccountName'] = adid
        request = requests.delete(url = configs.AD_LDAP_USER_URL, headers = configs.HEADER, data=json.dumps(body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == messages.RECORD_DELETED_SUCCESSFULLY:
                print(request.status_code,json.loads(request.content))
                return True,'success'
            else:
                return False, json.loads(request.content)['message']
        else:
            return False, request.status_code
    except Exception as error:
        print(error)
        return False, str(error)


def create_ad_attributes(row, old_email=None, samAccountName=None, userPrincipalName=None):
    try:
        attributes = {}
        if row.perno and row.firstname:
            user_id = create_userid(row.perno,row.firstname,row.midnm,row.last_name)
            attributes['EmployeeID'] = row.perno
            attributes['extensionAttribute1'] = row.perno
            attributes['extensionAttribute5'] = row.perno
            attributes['pager'] = row.perno
            attributes['samAccountName'] = samAccountName if samAccountName else user_id
            attributes['givenName'] = row.firstname
            attributes['userPrincipalName'] = userPrincipalName if userPrincipalName else (f'{user_id}@tatamotors.com' if row.comp_code != configs.TMML_CO_CODE else f'{user_id}@tatamotorsbsl.com')
        if row.last_name is not None:
            attributes['sn'] = row.last_name
        if row.midnm is not None:
            attributes['initials'] = row.midnm[0]
        if row.postxt:
            attributes['title'] = row.postxt
        if row.function_text:
            attributes['otherTelePhone'] = row.function_text
        if row.reporting:
            attributes['manager'] = row.reporting

        if row.ccodetxt:
            attributes['company'] = row.ccodetxt
        
        department = create_department(row.comp_code,row.orgtx,row.psatxt,row.jobtxt)
        if department:
            attributes['department'] = department
        
        if row.state:
            attributes['st'] = row.state
        
        if row.country:
            attributes['co'] = row.country
        
        if row.city_town:
            attributes['l'] = row.city_town

        if row.pin_code:
            attributes['postalCode'] = row.pin_code

        if row.off_num:
            attributes['telephoneNumber'] = row.off_num
            
        if not row.esubgroup == 'ED':
            if row.office_mobile is not None:
                attributes['mobile'] = str(row.office_mobile).split(',')[0]

            if row.pers_mobile is not None:
                attributes['homePhone'] = str(row.pers_mobile).split(',')[0]

        if row.imailid:
            attributes['mail'] = row.imailid
        elif old_email:
            attributes['mail'] = old_email
        else:
            attributes['mail'] = f'{user_id}@tatamotors.com' if row.comp_code != configs.TMML_CO_CODE else f'{user_id}@tatamotorsbsl.com'

        if row.costcenter and row.cosctrtxt:
            attributes['info'] = f'Cost Centre: {row.costcenter} - {row.cosctrtxt}'

        if row.costcenter:
            attributes['extensionAttribute3'] = row.costcenter

        if row.esgtxt:
            # attributes['extensionAttribute2'] = row.esgtxt
            attributes['msExchAssistantName'] = row.esgtxt

        # if row.subfunction2_tex:
        #     attributes['telePhoneAssistant'] = row.subfunction2_tex

        if row.title:
            attributes['extensionAttribute10'] = row.title
            ExtensionAttribute9 = identify_gender(row.title)
            if ExtensionAttribute9:
                attributes['extensionAttribute9'] = ExtensionAttribute9

        if row.patxt:
            attributes['extensionAttribute11'] = row.patxt

        if row.psatxt:
            attributes['extensionAttribute12'] = row.psatxt

        if row.loc_desc:
            attributes['extensionAttribute13'] = row.loc_desc
            
        if row.subfunction1_tex:
            attributes['facsimileTelephoneNumber'] = row.subfunction1_tex

        if row.dob and row.esubgroup:
            accountExpires = create_account_expiry(row.dob,row.esubgroup, row.comp_code)
            if accountExpires:
                attributes['accountExpires'] = accountExpires

        physicalDeliveryOfficeName = create_office(row.comp_code ,row.ou_level1_short_txt, row.ou_level2_short_txt, row.ou_level3_long_txt, row.patxt)
        if physicalDeliveryOfficeName:
            attributes['physicalDeliveryOfficeName'] = physicalDeliveryOfficeName
        if row.ou_level2_long_txt:
            attributes['division'] = row.ou_level2_long_txt
        streetAddress = f'{row.add1 if row.add1 else ""} {row.add2 if row.add2 else ""} {row.add3 if row.add3 else ""} {row.add4 if row.add4 else ""} {row.loc_desc if row.loc_desc else ""}'
        if streetAddress:  
            attributes['streetAddress'] = streetAddress


        fullname = create_fullname(row.firstname, row.last_name, row.title)
        if fullname:
            description = create_description(row.costcenter,row.comp_code,row.cosctrtxt,row.ou_level1_short_txt,row.function_text,row.city_town,row.psatxt,row.patxt,row.ou_level2_long_txt)
            if description:
                attributes['DisplayName'] = f'{fullname} [ {description} ]'
        return attributes
    except Exception as error:
        common_functions.print_error("create_ad_attributes",error)
        raise Exception(error)


def create_fullname(firstname, lastname, title):
    if title is not None:
        title = title.replace(" ","")
    fullname = ''
    if firstname:
        fullname = firstname.upper()
    if lastname:
        fullname = f'{fullname} {lastname.upper()}'
    if fullname:
        if 'doctor' in  title.lower() or 'dr' in title.lower():
            return f'Dr {fullname}'
        elif 'mr' in title.lower() or "mrs" in title.lower() or "ms" in title.lower() or title == None:
            return f'{fullname}'
        else:
            return f'{title} {fullname}'
    else:
        print('fullname not created')
        return None

# Marcopolo
# Author : Shriniwas

# def create_city_TMML (comp_code,patxt,psatxt):
#     if comp_code == 14 :
#         return 'Pune'
#     elif comp_code in [21,22] :
#         return patxt.split(' ')[0] if patxt else ''
#     elif "MUMBAI" in patxt.upper().split(' ') :
#         return psatxt
#     elif comp_code == 100:
#         return patxt.split(' ')[0] if patxt else ''
#     elif comp_code == 510:
#         return patxt.split(' ')[1] if patxt else ''
#     else :
#         return ''
    
# def is_sub_group_valid(row,subgroups):
#     if row['esubgroup'] in subgroups and row['comp_code'] not in ['14']:
#         return True
#     elif row['esubgroup'] <= "4B" and row['comp_code'] in ['14']:
#         return True
#     else:
#         return False

def identify_gender(title):
    female = ['Mrs','Ms','Dr.(Ms)']
    male = ['Mr','Doctor','Colonel','Captain','Brigadier','Major','Lieutenent','Sqn.Ldr','LtColonel']
    if title in female:
        return 'F'
    elif title in male:
        return 'M'
    else:
        return ''

def create_valid_sub_group():
    subgroups = []
    for i in range(1,11):
        subgroups.append("E"+str(i))
    for i in string.ascii_uppercase:
        subgroups.append("E"+str(i))
    for i in range(2,5):
        subgroups.append("C"+str(i))
    for i in range(2,9):
        subgroups.append("H"+str(i))
    for i in range(1,5):
        subgroups.append("F"+str(i))
    for i in range(1,10):
        subgroups.append("M"+str(i))
    for i in range(11,13):
        subgroups.append(str(i))
    for i in range(21,24):
        subgroups.append(str(i))
    for i in range(31,34):
        subgroups.append(str(i))
    for i in range(41,44):
        subgroups.append(str(i))
    for i in range(51,55):
        subgroups.append(str(i))
    for i in range(81,84):
        subgroups.append(str(i))
    for i in range(91,99):
        subgroups.append(str(i))
    for i in string.ascii_uppercase:
        subgroups.append("Z"+str(i))
        if str(i)=="N":
            break
    for i in range(1,7):
        subgroups.append("A"+str(i))
    for i in range(1,6):
        subgroups.append("G"+str(i))
    for i in string.ascii_uppercase:
        subgroups.append("N"+str(i))
        if str(i)=="C":
            break
    subgroups.append("XC")
    subgroups.append("FA")
    subgroups.append("FB")
    subgroups.append("TA")
    subgroups.append("TB")
    subgroups.append("Z1")
    return subgroups


def generate_ou_from_dn(dn):
    try:
        if dn is not None:
            OU = dn.split(',')
            data = {}
            for ele in OU:
                split_ele = ele.split('=')
                if len(split_ele)>=2: 
                    data[split_ele[0].lower()] = split_ele[1]
                if 'ou' in data:
                    if not 'Users' in data.get('ou'):
                        return data['ou']
                    else:
                        ou_list =data['ou'].split('Users')
                        return ou_list[0]
        return None
    except Exception as error:
        common_functions.print_error(error)
        raise Exception(error)
    try:
        if dn is not None:
            OU = dn.split(',')
            data = {}
            for ele in OU:
                split_ele = ele.split('=')
                if len(split_ele)>=2: 
                    data[split_ele[0].lower()] = split_ele[1]
                if 'ou' in data:
                    if not 'Users' in data.get('ou'):
                        return data['ou']
                    else:
                        ou_list =data['ou'].split('Users')
                        return ou_list[0]
        return None
    except Exception as error:
        common_functions.print_error(error)
        raise Exception(error)

def create_ou(city,patxt,comp_code = ''):
    if (str(city)).lower() == 'jamshedpur':
        return 'TMJSR'
    elif (str(city)).lower() == 'lucknow':
        return 'TMLKN'
    elif (str(city)).lower() in ['chakan','dighi','maval']:
        return 'TMPNE'
    elif ("pune" in (str(city)).lower()):
        return 'TMPNE'
    elif (str(city)).lower() in ['pantnagar','uttarakhand']:
        return 'TMRDPC'
    elif (str(city)).lower() in ['ahmedabad','sanand']:
        return 'TMSND'  
    elif (str(patxt)).lower() in ['mumbai hire purchase','mumbai tmf']:
        return 'TMF'  
    elif (str(city)).lower() in ['mumbai','thane','navi mumbai']:
        return 'TMMUM'
    elif str(patxt).lower() in ['BRABO ROBOTCS & AUTMN LTD'.lower()]:
        return 'BRAL' 
    elif str(patxt).lower() in ['tal'] and comp_code == configs.TMML_CO_CODE:
        return 'TALPNE'
    else:
        return 'TMMUM' 

# Marcopolo
# Author : Shriniwas

# def create_ou_TMML(city,patxt):
#     if (str(city)).lower() == 'jamshedpur':
#         return 'TMJSR'
#     elif (str(city)).lower() == 'lucknow':
#         return 'TMLKN'
#     elif (str(city)).lower() in ['chakan','dighi','maval']:
#         return 'TMPNE'
#     elif ("pune" in (str(city)).lower()):
#         return 'TMPNE'
#     elif (str(city)).lower() in ['pantnagar','uttarakhand']:
#         return 'TMRDPC'
#     elif (str(city)).lower() in ['ahmedabad','sanand']:
#         return 'TMSND'  
#     elif (str(patxt)).lower() in ['mumbai hire purchase','mumbai tmf']:
#         return 'TMF'  
#     elif 'mumbai' in (str(patxt)).lower():
#         return 'TMMUM'
#     elif str(patxt).lower() in ['tal']:
#         return 'TALPNE' 
#     else:
#         return 'TMMUM' 
    
def create_description(costcenter,comp_code,cosctrtxt,ou_level1_short_txt,function_text,city_town,psatxt,patxt,ou_level2_long_txt):
    bc = ['3400072','3400073','3400322','3400323','3400324','3400325','3400326','3400327','3400328','3400329','3400331','3400332','3400333','3400334','3400335','3400336','3400337','3400338','3400339','3400341','3400342','3400343','3400344','3400351','3400352','3400353','3400354','3400361','3400362','3400363','3400364','3400370','3400371','3400372','3400373','3400380','3400381','3400382','3400383','3400390','3400391','3400392','3400393','3400301','3400302','3400303','3400304','3400311','3400312','3400313','3400314']
    if costcenter is not None:
        cost_center = str(int(costcenter)) if costcenter.isdigit() else costcenter
    else:
        cost_center = ''
    #----------------------------------Company CV-----------------------------------
    description = ''
    if comp_code in ['100','300']:
        if cost_center in bc:
            return 'Global Delivery Centre'
        elif cost_center == '3400221' and cosctrtxt != 'Central Analytics':
            return cosctrtxt
        else:
            if ou_level1_short_txt:
                description = ou_level1_short_txt
            if function_text:
                if not description:
                    description = f'{function_text}'
                else:
                    description = f'{description}, {function_text}'
            if city_town:
                if not description:
                    description = f'{city_town}'
                else:
                    description = f'{description}, {city_town}'
            return description
        
    #----------------------------------Company PV and EV -----------------------------------
    elif comp_code in ['550','650']:
        bc.append('3400221')
        if cost_center == '3400221' and cosctrtxt != 'Central Analytics':
            return cosctrtxt
        elif cost_center in bc:
            return 'Global Delivery Centre'
        else:
            if ou_level1_short_txt:
                description = ou_level1_short_txt
            if function_text:
                if not description:
                    description = f'{function_text}'
                else:
                    description = f'{description}, {function_text}'
            if city_town:
                if not description:
                    description = f'{city_town}'
                else:
                    description = f'{description}, {city_town}'
            return description
    elif comp_code in [configs.TMML_CO_CODE]:
        if cosctrtxt :
            description = cosctrtxt
        if patxt:
            description = f'{description}, {patxt}'
        return description
    elif comp_code in ['1200']:
        if ou_level1_short_txt:
            description = ou_level1_short_txt
        if ou_level2_long_txt:
            if not description:
                description = f'{ou_level2_long_txt}'
            else:
                description = f'{description}, {ou_level2_long_txt}'
        if city_town:
            if not description:
                description = f'{city_town}'
            else:
                description = f'{description}, {city_town}'
        return description
    else:
        if psatxt:
            description = psatxt
        if patxt:
            description = f'{description}, {patxt}'
        return description

def create_office(comp_code ,ou_level1_short_txt, ou_level2_short_txt, ou_level3_long_txt, patxt):
    if comp_code in ['100','300','550','650']:
        ou_level1_short_txt = ou_level1_short_txt if ou_level1_short_txt else ''
        ou_level2_short_txt = f' , {ou_level2_short_txt}' if ou_level2_short_txt else ''
        ou_level3_long_txt = f' , {ou_level3_long_txt}' if ou_level3_long_txt else ''

        return f'{ou_level1_short_txt}{ou_level2_short_txt}{ou_level3_long_txt}'
    else:
        return f'{patxt}'
                   
def create_department(comp_code,orgtx,psatxt,jobtxt=None):
    if comp_code in ['100','300','550','650']:
        return orgtx if orgtx else ""
    elif comp_code in ['1200']:
        return jobtxt if jobtxt else ""
    else:
        return psatxt if psatxt else ""
    
def create_userid(perno,firstname,middlename,lastname):
    try:
        if firstname is not None:
            if lastname is not None:
                if middlename is not None:
                    return f'{firstname[0]}{middlename[0]}{lastname[0]}{perno}'
                else:
                    return f'{firstname[0]}{lastname[0]}{lastname[0]}{perno}'
            elif middlename is not None:
                return f'{firstname[0]}{middlename[0]}{middlename[0]}{perno}'
            else:
                return f'{firstname[0]}{firstname[0]}{firstname[0]}{perno}'
    except Exception as error:
        common_functions.print_error("create_userid",error)
        raise Exception(error)

            

def create_account_expiry(dob,esubgroup, comp_code=''):
    try:
        dob = dob[:10]
        try:
            datetime_object = datetime.strptime(dob, '%Y-%m-%d')
        except:
            datetime_object = datetime.strptime(dob, '%d-%m-%Y')
        if esubgroup in configs.TMML_ESUBGROUP_FOR_ACCOUNT_EXPIRY and configs.TMML_CO_CODE == comp_code:
            account_expiry = datetime_object+relativedelta(years=70)
        elif esubgroup in ['XC','Z1','ED'] and configs.TMML_CO_CODE != comp_code:
            account_expiry = datetime_object+relativedelta(years=70)
        else:
            account_expiry = datetime_object+relativedelta(years=60)
        last_date = calendar.monthrange(account_expiry.year, account_expiry.month)[1]
        return str(account_expiry.replace(day=last_date, hour=23, minute=59, second=59))
    except Exception as error:
        common_functions.print_error("create_account_expiry",error)
        raise Exception(error)
    
def disable_employee(username):
    try:
        request_body = {**configs.REQUEST_BODY}
        request_body['username'] = username
        request_body['attributes'] = {
            "userAccountControl": 514,
            "accountExpires":  str(datetime.now())[:19]
        }
        request = requests.put(configs.AD_LDAP_USER_URL,headers=configs.HEADER,data=json.dumps(request_body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == messages.RECORD_UPDATED_SUCCESSFULLY:
                return True, request_body['attributes']
            else:
                print("disable_employee response_object",json.loads(request.content))
                return False, None
        else:
            print('disable_employee response_code',request.status_code)
            return False, None
    except Exception as error:
        common_functions.print_error('disable_employee',error)
        raise Exception(error)
    
def update_employee(result,record):
    try:
        attributes = create_ad_attributes(record, result.get('mail'), result.get('samAccountName'), result.get('userPrincipalName'), is_create=False)
        if record.comp_code in ['1200','01200']:
            del attributes['department']
        change = common_functions.get_changed_attributes_for_update(attributes,result)
        request_body = {**configs.REQUEST_BODY}
        request_body['username'] = result['samAccountName'] 
        ou = create_ou(record.city_town,record.patxt, record.comp_code)
        if result['ou'] != ou:
            print(f'changed - ou - {result["ou"]} to {ou}')
            request_body['ou'] = f'{ou}Users'
        request_body['attributes'] = change
        request_body['comp_code'] = record.comp_code
        request = requests.put(configs.AD_LDAP_USER_URL,headers=configs.HEADER,data=json.dumps(request_body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == messages.RECORD_UPDATED_SUCCESSFULLY:
                if request_body.get('ou'):
                    attributes['ou'] = request_body['ou']
                    if 'ou' in request_body:
                        del request_body['ou']
                if 'department' in result and record.comp_code in ['1200','01200']:
                    attributes['department'] = result['department']
                return True, attributes
            else:
                print("update_employee response_object",json.loads(request.content))
                return False, None
        else:
            print('update_employee response_code',request.status_code)
            return False, None
    except Exception as error:
        common_functions.print_error('update_employee',error)
        raise Exception(error)
    
def insert_employee(record):
    try:
        attributes = create_ad_attributes(record)
        request_body = {**configs.REQUEST_BODY}
        request_body['samAccountName'] = attributes['samAccountName']
        request_body['ou'] = f'{create_ou(record.city_town,record.patxt, record.comp_code)}Users'
        request_body['password'] = common_functions.generate_password()
        request_body['add_to_internet_group'] = True
        request_body['add_to_wifi_group'] = True
        if not record.pers_area == 'ERCU' and record.comp_code != configs.TMML_CO_CODE:
            request_body['tml_wireless_group'] = True
        request_body['attributes'] = attributes
        request_body['comp_code'] = record.comp_code
        request = requests.post(configs.AD_LDAP_USER_URL,headers=configs.HEADER,data=json.dumps(request_body))
        if request.status_code == 200:
            if json.loads(request.content)['message'] == messages.RECORD_ADDED_SUCCESSFULLY:
                attributes['ou'] = request_body['ou']
                attributes['passwd'] = request_body['password']
                attributes['internet_access'] = request_body['add_to_internet_group']
                return True, attributes
            else:
                print("insert_employee response_object",json.loads(request.content))
                return False, None
        else:
            print('insert_employee response_code',request.status_code)
            return False, None
    except Exception as error:
        common_functions.print_error("insert_employee",error)
        raise Exception(error)
    
def update_upn(perno,comp_code,upn,email):
    try:
        requests_body = {**configs.REQUEST_BODY}
        # if comp_code == '100':
        #     requests_body['company_code']=100
        #     API_URL = configs.SAP_CV_UPDATE_UPN_EMAIL_URL
        # elif comp_code == '550':
        #     requests_body['company_code']=550
        #     API_URL = configs.SAP_PV_UPDATE_UPN_EMAIL_URL
        # elif comp_code == '650':
        #     requests_body['company_code']=650
        #     API_URL = configs.SAP_EV_UPDATE_UPN_EMAIL_URL
        # elif comp_code == '300':
        #     requests_body['company_code']=300
        #     API_URL = configs.SAP_TMLBSL_UPDATE_UPN_EMAIL_URL
        # elif comp_code == configs.TMML_CO_CODE:
        #     requests_body['company_code']=700
        #     API_URL = configs.SAP_TMML_UPDATE_UPN_EMAIL_URL

        if comp_code in ["100","550","650","300","700"]:
            API_URL = configs.SAP_UPDATE_UPN_EMAIL_URL
        
        requests_body['employee_id'] = perno
        requests_body['upn'] = upn
        requests_body['email'] = email
        requests_body['company_code'] = comp_code
        
        request = requests.post(API_URL,headers=configs.HEADER,data=json.dumps(requests_body))

        if request.status_code == 200:
            request = json.loads(request.content)
            if 'result' in request and request['result'] and "msg_mail" in request['result'] and request['result']['msg_mail'] == "Official Mail Updated successfully !" and 'msg_upn' in request['result'] and  request['result']['msg_upn'] == "UPN Updated successfully":
                return True
            else:
                print("update_upn response_object",request)
                return False
        else:
            print('update_upn response_code',request.status_code)
            return False
    except Exception as error:
        common_functions.print_error("update_upn",error)
        return False
    
# def update_imac_logs(oracle_session,user_id,reporting,company_text,pa_text,esubgroup_code):
#     imac_log = IMAC_Logs()
#     imac_log.imac_log_id = ("IMACLOG"+str(int(time.time_ns() * 10)))[:23]
#     imac_log.user_id = user_id
#     reporting_data = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == reporting).first()
#     if reporting_data:
#         imac_log.report_to = reporting_data.user_id
#     else:
#         reporting_data = get_ad_data_by_perno(reporting)
#         print('manager details',reporting_data)
#         if reporting_data:
#             imac_log.report_to = reporting_data.get('samAccountName').upper()
#             metadata_result = ADMetaData()
#             metadata_result.user_id = reporting_data.get('samAccountName').upper()
#             metadata_result.person_no = reporting_data.get('EmployeeID')
#             metadata_result.displayname = reporting_data.get('DisplayName')
#             metadata_result.office = reporting_data.get('physicalDeliveryOfficeName')
#             metadata_result.department = reporting_data.get('department')
#             metadata_result.address = reporting_data.get('streetAddress')
#             metadata_result.notes = reporting_data.get('info')
#             metadata_result.ou = reporting_data.get('ou')
#             metadata_result.domain = configs.DOMAIN
#             metadata_result.upn = reporting_data.get('userPrincipalName')
#             metadata_result.email = reporting_data.get('mail')
#             metadata_result.reporting_dn = reporting_data.get('manager')
#             metadata_result.expiry_date = datetime.strptime(reporting_data['accountExpires'], '%Y-%m-%d %H:%M:%S')
#             metadata_result.initial_password = None
#             metadata_result.created_by = configs.CREATED_BY
#             metadata_result.created_at = datetime.strptime(reporting_data['whenCreated'][:19], '%Y-%m-%d %H:%M:%S')
#             metadata_result.updated_at = datetime.strptime(reporting_data['whenChanged'][:19], '%Y-%m-%d %H:%M:%S')
#             metadata_result.is_deleted = False
#             metadata_result.is_active = reporting_data.get('is_active')
#             metadata_result.is_manual_entry = False
#             metadata_result.internet_access = True
#             metadata_result.is_external_email = False
#             metadata_result.mailbox_status = True
#             metadata_result.is_internal_migration = False
#             oracle_session.add(metadata_result)
#             oracle_session.commit()
#         else:
#             print('Imac - reporting not found')
#             return False
#     imac_log.imac_status = messages.PENDING
#     imac_log.imac_comments = messages.NOT_RAISED
#     papsa_imac_mapping = oracle_session.query(PaPsa).filter(PaPsa.pa_text == pa_text).first()
#     if papsa_imac_mapping:
#         imac_log.imac_location = papsa_imac_mapping.imac_location
#     else:
#         print('Imac - imac location not found')
#         return False
#     imac_log.imac_company_text = company_text
#     imac_log.created_at = datetime.now()
#     imac_log.updated_at = datetime.now()
#     esubgroup_details = oracle_session.query(ESubGroup).filter(ESubGroup.esubgroup_code == esubgroup_code).first()
#     if esubgroup_code:
#         imac_log.asset_type = esubgroup_details.asset_type
#     else:
#         return False
#     imac_log.is_deleted = False
#     oracle_session.add(imac_log)
#     oracle_session.commit()
#     return True

def update_imac_logs(oracle_session, user_id, reporting, company_text, pa_text, esubgroup_code):
    try:
        imac_log = IMAC_Logs()
        imac_log.imac_log_id = ("IMACLOG" + str(int(time.time_ns() * 10)))[:23]
        imac_log.user_id = user_id

        # Fetch reporting data
        reporting_data = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == reporting).first()
        if reporting_data:
            imac_log.report_to = reporting_data.user_id
        else:
            reporting_data = get_ad_data_by_perno(reporting)
            print('manager details', reporting_data)
            if reporting_data:
                imac_log.report_to = reporting_data.get('samAccountName').upper()
                metadata_result = ADMetaData(
                    user_id=reporting_data.get('samAccountName').upper(),
                    person_no=reporting_data.get('EmployeeID'),
                    displayname=reporting_data.get('DisplayName'),
                    division=reporting_data.get('division'),
                    office=reporting_data.get('physicalDeliveryOfficeName'),
                    department=reporting_data.get('department'),
                    address=reporting_data.get('streetAddress'),
                    notes=reporting_data.get('info'),
                    ou=reporting_data.get('ou'),
                    domain=configs.DOMAIN,
                    upn=reporting_data.get('userPrincipalName'),
                    email=reporting_data.get('mail'),
                    reporting_dn=reporting_data.get('manager'),
                    expiry_date=datetime.strptime(reporting_data['accountExpires'], '%Y-%m-%d %H:%M:%S'),
                    initial_password=None,
                    created_by=configs.CREATED_BY,
                    created_at=datetime.strptime(reporting_data['whenCreated'][:19], '%Y-%m-%d %H:%M:%S'),
                    updated_at=datetime.strptime(reporting_data['whenChanged'][:19], '%Y-%m-%d %H:%M:%S'),
                    is_deleted=False,
                    is_active=reporting_data.get('is_active'),
                    is_manual_entry=False,
                    internet_access=True,
                    is_external_email=False,
                    mailbox_status=True,
                    is_internal_migration=False
                )
                oracle_session.add(metadata_result)
                oracle_session.commit()
            else:
                print('Imac - reporting not found')
                return False

        imac_log.imac_status = messages.PENDING
        imac_log.imac_comments = messages.NOT_RAISED

        # Perform a single query to join Pa and Psa and fetch the required details
        papsa_entry = (
            oracle_session.query(Psa)
            .join(Pa, Psa.pa_id == Pa.id)
            .filter(Pa.pa_text == pa_text)
            .first()
        )

        if papsa_entry and papsa_entry.imac_location:
            imac_log.imac_location_id = papsa_entry.imac_location.iloc_id
        else:
            print('Imac - PSA or imac location not found')
            return False

        imac_log.imac_company_text = company_text
        imac_log.created_at = datetime.now()
        imac_log.updated_at = datetime.now()

        # Fetch eSubgroup details
        esubgroup_details = oracle_session.query(ESubGroup).filter(ESubGroup.esubgroup_code == esubgroup_code).first()
        if esubgroup_details:
            imac_log.asset_type = esubgroup_details.asset_type
        else:
            print('Imac - eSubGroup details not found')
            return False

        imac_log.is_deleted = False
        oracle_session.add(imac_log)
        oracle_session.commit()
        return True
    except Exception as error:
        common_functions.print_error('update_imac_logs', error)
        return False