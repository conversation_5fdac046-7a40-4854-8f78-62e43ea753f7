# from sqlalchemy import Column, Inte<PERSON>, String, DateTime, <PERSON>ole<PERSON>, ForeignKey
# from airflow.models.base import Base

# class CVTempTable(Base):
#     __tablename__ = 'cv_temp_table'

#     perno = Column(String, primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)

# class PVTempTable(Base):
#     __tablename__ = 'pv_temp_table'

#     perno = Column(String, primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)

# class EVTempTable(Base):
#     __tablename__ = 'ev_temp_table'

#     perno = Column(String, primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)

# class TMBSLTempTable(Base):
#     __tablename__ = 'tmbsl_temp_table'

#     perno = Column(String, primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)

# class TTLTempTable(Base):
#     __tablename__ = 'ttl_temp_table'

#     perno = Column(String, primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)

# class EDPEmployeeDetails(Base):
#     __tablename__ = 'edp_employee_details'

#     edp_cet_id =  Column(String, primary_key=True)
#     Perno = Column(String)
#     PersArea = Column(String)
#     Patxt = Column(String)
#     Egroup = Column(String)
#     Ptext = Column(String)
#     Esubgroup = Column(String)
#     Esgtxt = Column(String)
#     CompCode = Column(String)
#     Ccodetxt = Column(String)
#     BusArea = Column(String)
#     Bustxt = Column(String)
#     PSubarea = Column(String)
#     Psatxt = Column(String)
#     Payarea = Column(String)
#     Paytxt = Column(String)
#     Costcenter = Column(String)
#     Cosctrtxt = Column(String)
#     Position1 = Column(String)
#     Postxt = Column(String)
#     Job = Column(String)
#     Jobtxt = Column(String)
#     Fkbtx = Column(String)
#     Initials = Column(String)
#     CompName = Column(String)
#     LastName = Column(String)
#     Firstname = Column(String)
#     Midnm = Column(String)
#     Title = Column(String)
#     OffNum = Column(String)
#     Gender = Column(String)
#     Konfe = Column(String)
#     Religion = Column(String)
#     Famst = Column(String)
#     MarStatus = Column(String)
#     Bloodgroup = Column(String)
#     Imailid = Column(String)
#     Exmailid = Column(String)
#     Dobsorp = Column(String)
#     Doj = Column(String)
#     Dob = Column(String)
#     Dsvcvp = Column(String)
#     Dlprom = Column(String)
#     Dosep = Column(String)
#     Seprsn = Column(String)
#     Challenged = Column(String)
#     Reporting = Column(String)
#     Nameofreporting = Column(String)
#     Empstatus = Column(String)
#     Empstattxt = Column(String)
#     Orgeh = Column(String)
#     Orgtx = Column(String)
#     OfficeMobile = Column(String)
#     PersMobile = Column(String)
#     EmrgMobile = Column(String)
#     TransportCode = Column(String)
#     OffAddr = Column(String)
#     Sepcode = Column(String)
#     Fun = Column(String)
#     Funt = Column(String)
#     Mgtxt = Column(String)
#     Flag1 = Column(String)
#     LastProm1 = Column(String)
#     LastProm2 = Column(String)
#     LastProm3 = Column(String)
#     Dummy1 = Column(String)
#     Dummy2 = Column(String)
#     Dummy3 = Column(String)
#     Dummy4 = Column(String)
#     Dummy5 = Column(String)
#     BhrPerno = Column(String)
#     Mat1Mngr = Column(String)
#     Mat2Mngr = Column(String)
#     Mat3Mngr = Column(String)
#     Mat4Mngr = Column(String)
#     Mat5Mngr = Column(String)
#     HeadHrPerno = Column(String)
#     ErHrPerno = Column(String)
#     OuShortTxt = Column(String)
#     RegCod = Column(String)
#     RegCodTxt = Column(String)
#     JobtxtShort = Column(String)
#     OuLevel1 = Column(String)
#     OuLevel1ShortTxt = Column(String)
#     OuLevel1LongTxt = Column(String)
#     OuLevel2 = Column(String)
#     OuLevel2ShortTxt = Column(String)
#     OuLevel2LongTxt = Column(String)
#     OuLevel3 = Column(String)
#     OuLevel3ShortTxt = Column(String)
#     OuLevel3LongTxt = Column(String)
#     FuncId01 = Column(String)
#     FunctionText = Column(String)
#     Subfunction1Id = Column(String)
#     Subfunction1Tex = Column(String)
#     Subfunction2Id = Column(String)
#     Subfunction2Tex = Column(String)
#     LocCod = Column(String)
#     LocDesc = Column(String)
#     Add1 = Column(String)
#     Add2 = Column(String)
#     Add3 = Column(String)
#     Add4 = Column(String)
#     CityTown = Column(String)
#     State = Column(String)
#     PinCode = Column(String)
#     Country = Column(String)
#     A962Perno = Column(String)
#     A962Cname = Column(String)
#     created_datetime = Column(String)
#     modified_datetime = Column(String)

# class NextGenEmployeeDetails(Base):
#     __tablename__ = 'sap_daily_sync_cet_data'

#     perno = Column(String,primary_key=True)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     comp_code = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     hd_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     created_on = Column(DateTime)
#     updated_on = Column(DateTime)

# class AWSEmployeeData(Base):
#     __tablename__ = 'sap_empmaster'

#     pers_no = Column(Integer, primary_key=True, autoincrement=False)
#     co_code = Column(String, primary_key=True)
#     pers_area = Column(String)
#     empl_subgroup = Column(String)
#     pers_subarea = Column(String)
#     cost_center = Column(String)
#     position_code = Column(String)
#     position_text = Column(String)
#     job_code = Column(String)
#     job_text = Column(String)
#     ps_group = Column(String)
#     payroll_area = Column(String)
#     last_name = Column(String)
#     first_name = Column(String)
#     complete_name = Column(String)
#     middle_name = Column(String)
#     known_as = Column(String)
#     form_addr_key = Column(String)
#     employment_status_code = Column(String)
#     email_id = Column(String)
#     cell_no = Column(String)
#     code_sex = Column(String)
#     code_blood_grp = Column(String)
#     code_marital_sta = Column(String)
#     code_relgn = Column(String)
#     date_absorp = Column(String)
#     date_birth = Column(String)
#     date_joining = Column(String)
#     date_last_svpc = Column(String)
#     date_last_prom = Column(String)
#     date_prob_comp = Column(String)
#     date_separation = Column(String)
#     code_del_reason = Column(String)
#     code_handicap = Column(String)
#     code_status = Column(String)
#     smart_card_no = Column(String)
#     report_to = Column(String)
#     code_relgn_text = Column(String)
#     code_marital_sta_text = Column(String)
#     code_status_text = Column(String)
#     empl_subgroup_text = Column(String)
#     function_grp = Column(String)
#     hiring_reason = Column(String)
#     region = Column(String)
#     empl_group = Column(String)
#     empl_group_text = Column(String)
#     location = Column(String)
#     off_num = Column(String)
#     ext_email = Column(String)
#     org_unit = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     fax_num = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     office_mobile = Column(String)
#     location_code = Column(String)
#     pa_code = Column(String)
#     psa_code = Column(String)
#     employment_status_txt = Column(String)
#     ou_l1_id = Column(String)
#     ou_l2_id = Column(String)
#     ou_l3_id = Column(String)
#     subfun1_id = Column(String)
#     subfun2_id = Column(String)
#     upn_id = Column(String)

# class ADMetaData(Base):
#     __tablename__ = 'ad_metadata'
    
#     user_id = Column(String, primary_key=True)
#     person_no = Column(String)
#     displayname = Column(String)
#     office = Column(String)
#     department = Column(String)
#     address = Column(String)
#     notes = Column(String)
#     ou = Column(String)
#     domain = Column(String)
#     upn = Column(String)
#     email = Column(String)
#     reporting_dn = Column(String)
#     expiry_date = Column(DateTime)
#     initial_password = Column(String)
#     created_by = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     is_deleted = Column(Boolean)
#     is_active = Column(Boolean)
#     is_manual_entry = Column(Boolean)
#     internet_access = Column(Boolean)
#     is_external_email = Column(Boolean)
#     mailbox_status = Column(Boolean)
#     is_internal_migration = Column(Boolean)
#     spoc_per_no = Column(String)
#     is_sync_excluded = Column(Boolean, default=False)
#     incident_no = Column(String)
#     is_hidden = Column(Boolean)

# class ADChangeLogHistory(Base):
#     __tablename__ = 'ad_change_log_history'

#     adm_id = Column(Integer,primary_key=True, autoincrement=True)
#     user_id = Column(String)
#     person_no = Column(String)
#     displayname = Column(String)
#     office = Column(String)
#     department = Column(String)
#     address = Column(String)
#     notes = Column(String)
#     ou = Column(String)
#     domain = Column(String)
#     upn = Column(String)
#     email = Column(String)
#     reporting_dn = Column(String)
#     expiry_date = Column(DateTime)
#     initial_password = Column(String)
#     created_by = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     is_deleted = Column(Boolean)
#     is_active = Column(Boolean)
#     is_manual_entry = Column(Boolean)
#     internet_access = Column(Boolean)
#     is_external_email = Column(Boolean)
#     is_internal_migration = Column(Boolean)
#     # additional columns for change history comparison
#     file_id = Column(String)
#     log_created_at = Column(DateTime)
#     log_created_by = Column(String)
#     spoc_per_no = Column(String)

# class ADDeleteLogs(Base):
#     __tablename__ = 'ad_delete_logs'

#     ad_delete_log_id = Column(String, primary_key=True)
#     row_count = Column(String)
#     status = Column(String)
#     reason = Column(String)
#     created_at = Column(DateTime)

# class ADDeleteRowLogs(Base):
#     __tablename__ = 'ad_delete_row_logs'

#     deleted_row_id = Column(String ,primary_key=True)
#     user_id = Column(String)
#     status = Column(String)
#     reason = Column(String)
#     created_at = Column(DateTime)
#     ad_delete_log_id = Column(String)

# class IMAC_logs(Base):
#     __tablename__ = 'imac_logs'

#     imac_log_id = Column(String, primary_key=True)
#     user_id = Column(String)
#     report_to = Column(String)
#     imac_ticket_id = Column(String)
#     imac_status = Column(String)
#     imac_comments = Column(String)
#     imac_ticket_status = Column(String)
#     imac_location = Column(String)
#     imac_company_text = Column(String)
#     asset_type = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     is_deleted = Column(Boolean)

# class ManualTempTable(Base):

#     __tablename__ = 'manual_temp_table'

#     perno = Column(String,primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)

# class FileLogs(Base):

#     __tablename__ = 'file_logs'

#     file_id = Column(String, primary_key=True)
#     job_name = Column(String)
#     file_name = Column(String)
#     file_format = Column(String)
#     row_count =  Column(String)
#     column_count = Column(String)
#     status = Column(String)
#     reason = Column(String)
#     trigger_type = Column(String)
#     created_at = Column(DateTime)
#     trigger_by = Column(String)

# class FileRowLogs(Base):

#     __tablename__ = 'file_row_logs'

#     file_row_id = Column(String, primary_key=True)
#     perno = Column(String)
#     comp_code = Column(String)
#     row_count = Column(String)
#     ad_operation = Column(String)
#     aws_cet_operation = Column(String)
#     azure_cet_operation = Column(String)
#     edp_operation = Column(String)
#     nextgen_operation = Column(String)
#     keycloak_operation = Column(String)
#     ad_status = Column(String)
#     aws_cet_status = Column(String)
#     azure_cet_status = Column(String)
#     edp_status = Column(String)
#     nextgen_status = Column(String)
#     keycloak_status = Column(String)
#     ad_reason = Column(String)
#     aws_cet_reason = Column(String)
#     azure_cet_reason = Column(String)
#     edp_reason = Column(String)
#     nextgen_reason = Column(String)
#     keycloak_reason = Column(String)
#     resolved_file_id = Column(String)
#     resolved_file_row_id = Column(String)
#     is_upn_update = Column(Boolean)
#     file_id = Column(String)
    
# class SapFileRowLogTSCMSL(Base):
    
#     __tablename__ = 'sap_file_row_log_tscmsl'
    
#     sap_file_row_id = Column(String, primary_key=True)
#     file_row_id = Column(String, ForeignKey(FileRowLogs.file_row_id))
#     perno = Column(String)
#     response_stauts = Column(String)
#     response_message = Column(String)

# class AwsCetMetaData(Base):

#     __tablename__ = 'awscet_metadata' 

#     person_no = Column(String, primary_key=True)
#     known_as = Column(String)
#     cell_no = Column(String)
#     date_prob_comp = Column(String)
#     smart_card_no = Column(String)
#     fax_num = Column(String)
#     upn_id = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)

# class AzureCetMetaData(Base):

#     __tablename__ = 'azurecet_metadata' 

#     person_no = Column(String, primary_key=True)
#     known_as = Column(String)
#     cell_no = Column(String)
#     date_prob_comp = Column(String)
#     smart_card_no = Column(String)
#     fax_num = Column(String)
#     upn_id = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)

# class AwsCetChangeLogHistory(Base):

#     __tablename__ = 'aws_change_log_history'

#     aws_id = Column(Integer,primary_key=True, autoincrement=True)
#     person_no = Column(String)
#     known_as = Column(String)
#     cell_no = Column(String)
#     date_prob_comp = Column(String)
#     smart_card_no = Column(String)
#     fax_num = Column(String)
#     upn_id = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     file_id = Column(String)
#     log_created_at = Column(DateTime)

# class AzureCetChangeLogHistory(Base):

#     __tablename__ = 'azure_change_log_history'

#     azure_id = Column(Integer,primary_key=True, autoincrement=True)
#     person_no = Column(String)
#     known_as = Column(String)
#     cell_no = Column(String)
#     date_prob_comp = Column(String)
#     smart_card_no = Column(String)
#     fax_num = Column(String)
#     upn_id = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     file_id = Column(String)
#     log_created_at = Column(DateTime)
# class EDPMetaData(Base):

#     __tablename__ = 'edp_metadata'

#     person_no = Column(String, primary_key=True)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)

# class EDPChangeLogHistory(Base):

#     __tablename__ = 'edp_change_log_history'

#     edp_id = Column(Integer,primary_key=True, autoincrement=True)
#     person_no = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     file_id = Column(String)
#     log_created_at = Column(DateTime)

# class NextGenMetaData(Base):

#     __tablename__ = 'nextgen_metadata'

#     person_no = Column(String, primary_key=True)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)

# class SapCompanyMaster(Base):

#     __tablename__ = 'company_master'

#     com_mast_id = Column(Integer, primary_key=True, autoincrement=True)
#     comp_code = Column(String)
#     company_name = Column(String)
#     belongs_to = Column(String)
#     is_deleted = Column(Boolean)

# class SapEmployeeMaster(Base):

#     __tablename__ = 'sap_employee_master'

#     person_no = Column(String, primary_key=True)
#     company_code = Column(String)
#     company_text = Column(String)
#     pers_area_code = Column(String)
#     pers_area_text = Column(String)
#     pers_subarea_code = Column(String)
#     pers_subarea_text = Column(String)
#     employee_group_code = Column(String)
#     employee_group_text = Column(String)
#     employee_subgroup_code = Column(String)
#     employee_subgroup_text = Column(String)
#     payroll_area_code = Column(String)
#     payroll_area_text = Column(String)
#     cost_center_code = Column(String)
#     cost_center_text = Column(String)
#     position_code = Column(String)
#     position_text = Column(String)
#     job_code = Column(String)
#     job_text = Column(String)
#     bus_area_code = Column(String)
#     bus_area_text = Column(String)
#     fkbtx = Column(String)
#     salutation = Column(String)
#     first_name = Column(String)
#     middle_name = Column(String)
#     last_name = Column(String)
#     full_name = Column(String)
#     initials = Column(String)
#     gender = Column(String)
#     religion_code = Column(String)
#     religion_text = Column(String)
#     maritial_status_code = Column(String)
#     maritial_status_text = Column(String)
#     blood_group = Column(String)
#     dob = Column(String)
#     doj = Column(String)
#     dobsorp = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting_person_no = Column(String)
#     reporting_fullname = Column(String)
#     employee_status_code = Column(String)
#     employee_status_text = Column(String)
#     org_unit_code = Column(String)
#     org_unit_text = Column(String)
#     office_telephone_no = Column(String)
#     office_mobile_no = Column(String)
#     personal_mobile_no = Column(String)
#     emergency_mobile_no = Column(String)
#     company_email = Column(String)
#     personal_email = Column(String)
#     transport_code = Column(String)
#     office_address = Column(String)
#     sep_code = Column(String)
#     function_group_code = Column(String)
#     function_group_text = Column(String)
#     hiring_reason = Column(String)
#     flag1 = Column(String)
#     last_prom1_date = Column(String)
#     last_prom2_date = Column(String)
#     last_prom3_date = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_text = Column(String)
#     region_code = Column(String)
#     region_text = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1_code = Column(String)
#     ou_level1_short_text = Column(String)
#     ou_level1_long_text = Column(String)
#     ou_level2_code = Column(String)
#     ou_level2_short_text = Column(String)
#     ou_level2_long_text = Column(String)
#     ou_level3_code = Column(String)
#     ou_level3_short_text = Column(String)
#     ou_level3_long_text = Column(String)
#     function_id_code = Column(String)
#     function_id_text = Column(String)
#     subfunction_id1_code = Column(String)
#     subfunction_id1_text = Column(String)
#     subfunction_id2_code = Column(String)
#     subfunction_id2_text = Column(String)
#     location_code = Column(String)
#     location_text = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city = Column(String)
#     state = Column(String)
#     pincode = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_fullname = Column(String)

# class SapChangeLogHistory(Base):

#     __tablename__ = 'sap_change_logs_history'

#     chng_hist_id = Column(Integer, primary_key=True, autoincrement=True)
#     person_no = Column(String)
#     company_code = Column(String)
#     company_text = Column(String)
#     pers_area_code = Column(String)
#     pers_area_text = Column(String)
#     pers_subarea_code = Column(String)
#     pers_subarea_text = Column(String)
#     employee_group_code = Column(String)
#     employee_group_text = Column(String)
#     employee_subgroup_code = Column(String)
#     employee_subgroup_text = Column(String)
#     payroll_area_code = Column(String)
#     payroll_area_text = Column(String)
#     cost_center_code = Column(String)
#     cost_center_text = Column(String)
#     position_code = Column(String)
#     position_text = Column(String)
#     job_code = Column(String)
#     job_text = Column(String)
#     bus_area_code = Column(String)
#     bus_area_text = Column(String)
#     fkbtx = Column(String)
#     salutation = Column(String)
#     first_name = Column(String)
#     middle_name = Column(String)
#     last_name = Column(String)
#     full_name = Column(String)
#     intials = Column(String)
#     gender = Column(String)
#     religion_code = Column(String)
#     religion_text = Column(String)
#     maritial_status_code = Column(String)
#     maritial_status_text = Column(String)
#     blood_group = Column(String)
#     dob = Column(String)
#     doj = Column(String)
#     dobsorp = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting_person_no = Column(String)
#     reporting_fullname = Column(String)
#     employee_status_code = Column(String)
#     employee_status_text = Column(String)
#     org_unit_code = Column(String)
#     org_unit_text = Column(String)
#     office_telephone_no = Column(String)
#     office_mobile_no = Column(String)
#     personal_mobile_no = Column(String)
#     emergency_mobile_no = Column(String)
#     company_email = Column(String)
#     personal_email = Column(String)
#     transport_code = Column(String)
#     office_address = Column(String)
#     sep_code = Column(String)
#     funtion_group_code = Column(String)
#     function_group_text = Column(String)
#     hiring_reason = Column(String)
#     flag1 = Column(String)
#     last_prom1_date = Column(String)
#     last_prom2_date = Column(String)
#     last_prom3_date = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_text = Column(String)
#     region_code = Column(String)
#     region_text = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1_code = Column(String)
#     ou_level1_short_text = Column(String)
#     ou_level1_long_text = Column(String)
#     ou_level2_code = Column(String)
#     ou_level2_short_text = Column(String)
#     ou_level2_long_text = Column(String)
#     ou_level3_code = Column(String)
#     ou_level3_short_text = Column(String)
#     ou_level3_long_text = Column(String)
#     function_id_code = Column(String)
#     function_id_text = Column(String)
#     subfunction_id1_code = Column(String)
#     subfunction_id1_text = Column(String)
#     subfunction_id2_code = Column(String)
#     subfunction_id2_text = Column(String)
#     location_code = Column(String)
#     location_text = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city = Column(String)
#     state = Column(String)
#     pincode = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_fullname = Column(String)
#     # additional columns for change history comparison
#     file_id = Column(String)
#     created_at = Column(DateTime)
#     is_manual_entry = Column(Boolean)
#     is_deleted = Column(Boolean)

# class KeyCloakMetaData(Base):

#     __tablename__ = 'keycloak_metadata'

#     person_no = Column(String)
#     username = Column(String, primary_key=True)
#     firstname = Column(String)
#     lastname = Column(String)
#     mobilenumber = Column(String)
#     location = Column(String)
#     enabled = Column(Boolean)
#     created_by = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)


# class KeyCloakChangeLogHistory(Base):

#     __tablename__ = "keycloak_change_log_history"
    
#     keycloak_chng_id = Column(Integer,primary_key=True, autoincrement=True)
#     person_no = Column(String)
#     username = Column(String)
#     firstname = Column(String)
#     lastname = Column(String)
#     mobilenumber = Column(String)
#     location = Column(String)
#     enabled = Column(Boolean)
#     created_by = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     file_id = Column(String)
#     log_created_at = Column(DateTime)
#     log_created_by = Column(String)

# class ESubGroup(Base):

#     __tablename__ = 'subgrp_table'

#     id = Column(Integer, primary_key=True, autoincrement=True)
#     esubgroup_code = Column(String)
#     esubgroup_text = Column(String)
#     asset_type = Column(String)
#     update_telephone = Column(Boolean)
#     is_deleted = Column(Boolean)
#     esubgroup_type = Column(Boolean)
    
# class KeycloakESubGroup(Base):

#     __tablename__ = 'keycloak_subgrp_table'

#     id = Column(Integer, primary_key=True, autoincrement=True)
#     esubgroup_code = Column(String)
#     is_deleted = Column(Boolean)
#     esubgroup_type = Column(Boolean)
#     egroup_code = Column(String)
    
# class ESubGroupTMML(Base):

#     __tablename__ = 'subgrp_table_tmml'

#     id = Column(Integer, primary_key=True, autoincrement=True)
#     esubgroup_code = Column(String)
#     esubgroup_text = Column(String)
#     asset_type = Column(String)
#     update_telephone = Column(Boolean)
#     is_deleted = Column(Boolean)
#     esubgroup_type = Column(Boolean)

# class PaPsa(Base):

#     __tablename__ = 'pa_psa_table'

#     id = Column(Integer, primary_key=True, autoincrement=True)
#     pa_code = Column(String)
#     pa_text = Column(String) 
#     psa_code = Column(String)
#     psa_text = Column(String)
#     company_code = Column(String)
#     imac_location = Column(String)
#     is_deleted = Column(Boolean)

# class SyncExclusionLogs(Base):

#     __tablename__ = 'sync_exclusion_logs'

#     sync_log_id = Column(String, primary_key=True)
#     user_id = Column(String)
#     sap_employee_status = Column(Boolean)
#     sap_expiry_date = Column(DateTime)
#     extended_date = Column(DateTime)
#     is_extended = Column(Boolean)
#     created_by = Column(String)
#     created_at = Column(DateTime)
#     updated_at = Column(DateTime)
#     is_deleted = Column(Boolean)
#     is_sync_enable = Column(Boolean, default=False)

# class SapCompanyMaster1(Base):
#     __tablename__ = 'sap_company_master'

#     co_code = Column(String, primary_key = True)
#     co_code_desc = Column(String)

# class SAPPersAreaMaster(Base):

#     __tablename__ = 'sap_pers_area_master'

#     co_code = Column(String, primary_key=True)
#     pers_area = Column(String, primary_key=True)
#     pers_area_text = Column(String)

# class SAPPersSAreaMaster(Base):

#     __tablename__ = 'sap_pers_sarea_master'

#     co_code = Column(String, primary_key=True)
#     pers_area = Column(String, primary_key=True)
#     pers_subarea = Column(String, primary_key=True)
#     pers_sarea_text = Column(String)

# class SAPOrgUnitMaster(Base):

#     __tablename__ = 'sap_org_unit_master'

#     co_code = Column(String, primary_key=True)
#     org_unit = Column(String, primary_key=True)
#     org_unit_text = Column(String)

# class SAPFunctionGrpMaster(Base):

#     __tablename__ = 'sap_function_grp_master'

#     funcode = Column(String, primary_key=True)
#     fundesc = Column(String)
#     isactive = Column(String)

# class SAPSubFun1GrpMaster(Base):

#     __tablename__ = 'sap_subfun1_grp_master'

#     funcode = Column(String, primary_key=True)
#     subfun1_id = Column(String, primary_key=True)
#     subfun1_txt = Column(String)

# class SAPSubFun2GrpMaster(Base):

#     __tablename__ = 'sap_subfun2_grp_master'

#     funcode = Column(String, primary_key=True)
#     subfun1_id = Column(String, primary_key=True)
#     subfun2_id = Column(String, primary_key=True)
#     subfun2_txt = Column(String)

# class SAPOrgLevel1Master(Base):

#     __tablename__ = 'sap_org_level1_master'

#     co_code = Column(String, primary_key=True)
#     org_unit = Column(String, primary_key=True)
#     ou_l1_id = Column(String, primary_key=True)
#     ou_l1_sht_txt = Column(String)
#     ou_l1_long_txt = Column(String)

# class SAPOrgLevel2Master(Base):

#     __tablename__ = 'sap_org_level2_master'

#     co_code = Column(String, primary_key=True)
#     org_unit = Column(String, primary_key=True)
#     ou_l2_id = Column(String, primary_key=True)
#     ou_l2_sht_txt = Column(String)
#     ou_l2_long_txt = Column(String)

# class SAPOrgLevel3Master(Base):

#     __tablename__ = 'sap_org_level3_master'

#     co_code = Column(String, primary_key=True)
#     org_unit = Column(String, primary_key=True)
#     ou_l3_id = Column(String, primary_key=True)
#     ou_l3_sht_txt = Column(String)
#     ou_l3_long_txt = Column(String)

# class SAPCostCenterMaster(Base):

#     __tablename__ = 'sap_cost_center_master'

#     co_code = Column(String, primary_key=True)
#     cost_center = Column(String, primary_key=True)
#     cost_center_text = Column(String)

# class SAPPayAreaMaster(Base):

#     __tablename__ = 'sap_pay_area_master'

#     co_code = Column(String, primary_key=True)
#     pay_area = Column(String, primary_key=True)
#     pay_area_name = Column(String)

# class TSCMSLTempTable(Base):
#     __tablename__ = 'tscmsl_temp_table'

#     perno = Column(String, primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)

# class TMMLTempTable(Base):
#     __tablename__ = 'tmml_temp_table'

#     perno = Column(String, primary_key=True)
#     comp_code = Column(String)
#     pers_area = Column(String)
#     patxt = Column(String)
#     egroup = Column(String)
#     ptext = Column(String)
#     esubgroup = Column(String)
#     esgtxt = Column(String)
#     ccodetxt = Column(String)
#     bus_area = Column(String)
#     bustxt = Column(String)
#     p_subarea = Column(String)
#     psatxt = Column(String)
#     payarea = Column(String)
#     paytxt = Column(String)
#     costcenter = Column(String)
#     cosctrtxt = Column(String)
#     position1 = Column(String)
#     postxt = Column(String)
#     job = Column(String)
#     jobtxt = Column(String)
#     fkbtx = Column(String)
#     initials = Column(String)
#     comp_name = Column(String)
#     last_name = Column(String)
#     firstname = Column(String)
#     midnm = Column(String)
#     title = Column(String)
#     off_num = Column(String)
#     gender = Column(String)
#     konfe = Column(String)
#     religion = Column(String)
#     famst = Column(String)
#     mar_status = Column(String)
#     bloodgroup = Column(String)
#     imailid = Column(String)
#     exmailid = Column(String)
#     dobsorp = Column(String)
#     doj = Column(String)
#     dob = Column(String)
#     dsvcvp = Column(String)
#     dlprom = Column(String)
#     dosep = Column(String)
#     seprsn = Column(String)
#     challenged = Column(String)
#     reporting = Column(String)
#     nameofreporting = Column(String)
#     empstatus = Column(String)
#     empstattxt = Column(String)
#     orgeh = Column(String)
#     orgtx = Column(String)
#     office_mobile = Column(String)
#     pers_mobile = Column(String)
#     emrg_mobile = Column(String)
#     transport_code = Column(String)
#     off_addr = Column(String)
#     sepcode = Column(String)
#     fun = Column(String)
#     funt = Column(String)
#     mgtxt = Column(String)
#     flag1 = Column(String)
#     last_prom1 = Column(String)
#     last_prom2 = Column(String)
#     last_prom3 = Column(String)
#     dummy1 = Column(String)
#     dummy2 = Column(String)
#     dummy3 = Column(String)
#     dummy4 = Column(String)
#     dummy5 = Column(String)
#     bhr_perno = Column(String)
#     mat1_mngr = Column(String)
#     mat2_mngr = Column(String)
#     mat3_mngr = Column(String)
#     mat4_mngr = Column(String)
#     mat5_mngr = Column(String)
#     head_hr_perno = Column(String)
#     er_hr_perno = Column(String)
#     ou_short_txt = Column(String)
#     reg_cod = Column(String)
#     reg_cod_txt = Column(String)
#     jobtxt_short = Column(String)
#     ou_level1 = Column(String)
#     ou_level1_short_txt = Column(String)
#     ou_level1_long_txt = Column(String)
#     ou_level2 = Column(String)
#     ou_level2_short_txt = Column(String)
#     ou_level2_long_txt = Column(String)
#     ou_level3 = Column(String)
#     ou_level3_short_txt = Column(String)
#     ou_level3_long_txt = Column(String)
#     func_id01 = Column(String)
#     function_text = Column(String)
#     subfunction1_id = Column(String)
#     subfunction1_tex = Column(String)
#     subfunction2_id = Column(String)
#     subfunction2_tex = Column(String)
#     loc_cod = Column(String)
#     loc_desc = Column(String)
#     add1 = Column(String)
#     add2 = Column(String)
#     add3 = Column(String)
#     add4 = Column(String)
#     city_town = Column(String)
#     state = Column(String)
#     pin_code = Column(String)
#     country = Column(String)
#     a962_perno = Column(String)
#     a962_cname = Column(String)
#     incident_no = Column(String)
#     created_by = Column(String)
# class EmployeeSeparation(Base):
    
#     __tablename__ = 'employee_separation'
#     id = Column(Integer, primary_key=True, autoincrement=True)
#     separation_request_id = Column(String)
#     employee_id = Column(String)
#     position_code= Column(String)
#     separation_status = Column(String)
#     employee_comp_code = Column(String)
#     resignation_request_date = Column(DateTime)
#     actual_resignation_date = Column(DateTime)
#     lwd_requested_by_employee = Column(DateTime)
#     lwd_recommended_by_dem = Column(DateTime)
#     lwd_recommended_by_bhr = Column(DateTime)
#     created_by = Column(String)
#     created_datetime = Column(DateTime)
#     modified_by = Column(String)
#     modified_datetime = Column(String)
#     is_deleted = Column(DateTime)

# class IdSeparationLogs(Base):
    
#     __tablename__ = 'id_separation_logs'
#     separation_log_id = Column(String, primary_key=True)
#     row_count =  Column(String)
#     status = Column(String)
#     reason = Column(String)
#     created_at = Column(DateTime)

# class IdSeparationRowLogs(Base):

#     __tablename__ = 'id_separation_row_logs'
#     separation_row_log_id = Column(String, primary_key=True)
#     perno = Column(String)
#     sap_id = Column(String)
#     separation_status = Column(String)
#     separation_date = Column(DateTime)
#     is_deleted = Column(Boolean)
#     separation_log_id = Column(String)
#     status = Column(String)
#     reason = Column(String)

# class EmployeeRegistration(Base):

#     __tablename__ = 'employee_registration'
#     unique_id = Column(String, primary_key=True)
#     employee_id = Column(String)
#     mobile_number = Column(String)
#     is_active = Column(Boolean)
#     is_pin_set = Column(Boolean)
#     consent_text = Column(String)
#     is_deleted = Column(Boolean)
#     created_by = Column(String)
#     created_date_time = Column(DateTime)
#     modified_by = Column(String)
#     modified_date_time = Column(DateTime)

# class ExtAttr13Mappping(Base):
#     __tablename__ = 'ext_attr_13_mapping'
#     id = Column(String, primary_key=True)
#     extattr13 = Column(String)
#     location = Column(String)
#     is_deleted = Column(Boolean)
