# In-built imports...
from datetime import datetime
from operations.active_directory_operations import get_ad_data_by_perno
# Custom imports...
from commons import db_connections, common_functions
from commons import messages
# from commons.models import AWSEmployeeData, AwsCetMetaData, AwsCetChangeLogHistory
from commons.models_v2 import AWSEmployeeData, AwsCetMetaData, AwsCetChangeLogHistory
mssql_session = db_connections.create_AWS_mssql_session()

def date_cleaner(objdatetime):
    if objdatetime is not None:
        if type(objdatetime) == datetime:
            return str(objdatetime)[:10]
        elif objdatetime in ['0000-00-00','00-00-0000']:
            return str(objdatetime)[:10]
        else:
            return str(objdatetime)[:10]
    else:
        return None

def update_to_aws_cet(oracle_session,row,file_id):
    operation = None
    status = None
    message =None
    try:
        if row.comp_code != 'TTL':
            co_code = row.comp_code.rjust(4,'0') if row.comp_code != '300' else '0100'
        else:
            co_code = row.comp_code
        result = mssql_session.query(AWSEmployeeData).filter(AWSEmployeeData.pers_no == row.perno, AWSEmployeeData.co_code == co_code).first()
        if result: # check if exists in AWS-CET table
            operation = messages.UPDATED
            metadata_result = oracle_session.query(AwsCetMetaData).filter(AwsCetMetaData.person_no == row.perno).first()
            if not metadata_result: # check if not exists in AWS-CET metadata table
                metadata_result = AwsCetMetaData() # insert in AWS-CET metadata table from AWS-CET
                metadata_result.person_no = result.pers_no
                metadata_result.known_as = result.known_as
                metadata_result.cell_no = result.cell_no
                metadata_result.date_prob_comp = result.date_prob_comp
                metadata_result.smart_card_no = result.smart_card_no
                metadata_result.fax_num = result.fax_num
                metadata_result.upn_id = result.upn_id
                metadata_result.created_at = datetime.now()
                metadata_result.updated_at = datetime.now()
                oracle_session.add(metadata_result)
                oracle_session.commit()
            result.pers_no = row.perno #update in AWS-CET table
            result.co_code = co_code
            result.pers_area = row.pers_area
            result.empl_subgroup = row.esubgroup
            result.pers_subarea = row.p_subarea
            result.cost_center = row.costcenter
            result.position_code = row.position1
            result.position_text = row.postxt
            result.job_code = row.job
            result.job_text = row.jobtxt
            result.ps_group = row.bus_area
            result.payroll_area = row.payarea
            result.last_name = row.last_name
            result.first_name = row.firstname
            result.complete_name = row.comp_name
            result.middle_name = row.midnm
            result.form_addr_key = row.title
            result.employment_status_code = row.empstatus
            result.email_id = row.imailid
            result.code_sex = row.gender
            result.code_blood_grp = row.bloodgroup
            result.code_marital_sta = row.famst
            result.code_relgn = row.konfe
            result.date_absorp = date_cleaner(row.dobsorp)
            result.date_birth = date_cleaner(row.dob)
            result.date_joining = date_cleaner(row.doj)
            result.date_last_svpc = date_cleaner(row.dsvcvp)
            result.date_last_prom = date_cleaner(row.dlprom)
            result.date_separation = date_cleaner(row.dosep)
            result.code_del_reason = row.sepcode
            result.code_status = row.egroup
            result.report_to = row.reporting
            result.code_relgn_text = row.religion[:20] if row.religion is not None else None
            result.code_marital_sta_text =row.mar_status
            result.code_status_text = row.ptext
            result.empl_subgroup_text = row.esgtxt
            result.function_grp = row.fun
            result.hiring_reason = row.mgtxt
            result.region = row.reg_cod
            result.location = row.loc_desc
            result.off_num = row.off_num
            result.ext_email = row.exmailid
            result.org_unit = row.orgeh
            result.pers_mobile = row.pers_mobile
            result.emrg_mobile = row.emrg_mobile
            result.transport_code = row.transport_code
            result.off_addr = row.off_addr
            result.last_prom1 = date_cleaner(row.last_prom1)
            result.last_prom2 = date_cleaner(row.last_prom2)
            result.last_prom3 = date_cleaner(row.last_prom3)
            result.dummy1 = row.dummy1
            result.dummy2 = row.dummy2
            result.dummy3 = row.dummy3
            result.dummy4 = row.dummy4
            result.dummy5 = row.dummy5
            result.office_mobile = row.office_mobile
            result.location_code = row.loc_cod
            result.pa_code = row.pers_area
            result.psa_code = row.p_subarea
            result.employment_status_txt = row.empstattxt
            result.ou_l1_id = row.ou_level1
            result.ou_l2_id = row.ou_level2
            result.ou_l3_id = row.ou_level3
            result.subfun1_id = row.subfunction1_id
            result.subfun2_id = row.subfunction2_id

            """--------------Update UPN-------------"""
            if not result.upn_id:
                ad_data = get_ad_data_by_perno(row.perno)
                if ad_data:
                    result.upn_id = ad_data.get('sAMAccountName').lower()

            # if not result.upn_id:
            #     ad_data = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == row.perno).first()
            #     if ad_data:
            #         result.upn_id = ad_data.user_id.lower()

            mssql_session.add(result)
            mssql_session.commit()

            metadata_result = oracle_session.query(AwsCetMetaData).filter(AwsCetMetaData.person_no == row.perno).first()
            if metadata_result:
                aws_change_log = AwsCetChangeLogHistory()
                aws_change_log.person_no = metadata_result.person_no
                aws_change_log.known_as = metadata_result.known_as
                aws_change_log.cell_no = metadata_result.cell_no
                aws_change_log.date_prob_comp = metadata_result.date_prob_comp
                aws_change_log.smart_card_no = metadata_result.smart_card_no
                aws_change_log.fax_num = metadata_result.fax_num
                aws_change_log.upn_id = metadata_result.upn_id
                aws_change_log.created_at = metadata_result.created_at
                aws_change_log.updated_at = metadata_result.updated_at
                aws_change_log.file_id = file_id
                aws_change_log.log_created_at = datetime.now()
                oracle_session.add(aws_change_log)
                oracle_session.commit()

                metadata_result.person_no = result.pers_no
                metadata_result.known_as = result.known_as
                metadata_result.cell_no = result.cell_no
                metadata_result.date_prob_comp = result.date_prob_comp
                metadata_result.smart_card_no = result.smart_card_no
                metadata_result.fax_num = result.fax_num
                metadata_result.upn_id = result.upn_id
                metadata_result.updated_at = datetime.now()
                oracle_session.commit()
            status = messages.SUCCESS
            message = None
        else:
            operation = messages.INSERTED
            result = AWSEmployeeData() # insert in AWS-CET table
            result.pers_no = row.perno
            result.co_code = co_code
            result.pers_area = row.pers_area
            result.empl_subgroup = row.esubgroup
            result.pers_subarea = row.p_subarea
            result.cost_center = row.costcenter
            result.position_code = row.position1
            result.position_text = row.postxt
            result.job_code = row.job
            result.job_text = row.jobtxt
            result.ps_group = row.bus_area
            result.payroll_area = row.payarea
            result.last_name = row.last_name
            result.first_name = row.firstname
            result.complete_name = row.comp_name
            result.middle_name = row.midnm
            result.known_as = None
            result.form_addr_key = row.title
            result.employment_status_code = row.empstatus
            result.email_id = row.imailid
            result.cell_no = None
            result.code_sex = row.gender
            result.code_blood_grp = row.bloodgroup
            result.code_marital_sta = row.famst
            result.code_relgn = row.konfe
            result.date_absorp = date_cleaner(row.dobsorp)
            result.date_birth = date_cleaner(row.dob)
            result.date_joining = date_cleaner(row.doj)
            result.date_last_svpc = date_cleaner(row.dsvcvp)
            result.date_last_prom = date_cleaner(row.dlprom)
            result.date_prob_comp = None
            result.date_separation = date_cleaner(row.dosep)
            result.code_del_reason = row.sepcode
            result.code_handicap = row.challenged
            result.code_status = row.egroup
            result.smart_card_no = None
            result.report_to = row.reporting
            result.code_relgn_text = row.religion[:20] if row.religion is not None else None
            result.code_marital_sta_text =row.mar_status
            result.code_status_text = row.ptext
            result.empl_subgroup_text = row.esgtxt
            result.function_grp = row.fun
            result.hiring_reason = row.mgtxt
            result.region = row.reg_cod
            result.location = row.loc_desc
            result.off_num = row.off_num
            result.ext_email = row.exmailid
            result.org_unit = row.orgeh
            result.pers_mobile = row.pers_mobile
            result.emrg_mobile = row.emrg_mobile
            result.transport_code = row.transport_code
            result.off_addr = row.off_addr
            result.last_prom1 = date_cleaner(row.last_prom1)
            result.last_prom2 = date_cleaner(row.last_prom2)
            result.last_prom3 = date_cleaner(row.last_prom3)
            result.dummy1 = row.dummy1
            result.dummy2 = row.dummy2
            result.dummy3 = row.dummy3
            result.dummy4 = row.dummy4
            result.dummy5 = row.dummy5
            result.office_mobile = row.office_mobile
            result.location_code = row.loc_cod
            result.pa_code = row.pers_area
            result.psa_code = row.p_subarea
            result.employment_status_txt = row.empstattxt
            result.ou_l1_id = row.ou_level1
            result.ou_l2_id = row.ou_level2
            result.ou_l3_id = row.ou_level3
            result.subfun1_id = row.subfunction1_id
            result.subfun2_id = row.subfunction2_id

            """--------------Create UPN Details-------------"""
            ad_data = get_ad_data_by_perno(row.perno)
            if ad_data:
                result.upn_id = ad_data.get('sAMAccountName').lower()
                if not row.imailid:
                    ad_data.get('mail').lower()

            # ad_data = oracle_session.query(ADMetaData).filter(ADMetaData.person_no == row.perno).first()
            # if ad_data:
            #     result.upn_id = ad_data.user_id.lower()
            #     if not row.imailid:
            #         result.email_id = ad_data.email
                    
            mssql_session.add(result)
            mssql_session.commit()
            if not oracle_session.query(AwsCetMetaData).filter(AwsCetMetaData.person_no == row.perno).first():
                metadata_result = AwsCetMetaData()
                metadata_result.person_no = row.perno
                metadata_result.known_as = None
                metadata_result.cell_no = None
                metadata_result.date_prob_comp = None
                metadata_result.smart_card_no = None
                metadata_result.fax_num = None
                if ad_data:
                    metadata_result.upn_id = ad_data.get('sAMAccountName').lower()
                metadata_result.created_at = datetime.now()
                metadata_result.updated_at = datetime.now()
                oracle_session.add(metadata_result)
                oracle_session.commit()
            status = messages.SUCCESS
            message = None
    except Exception as error:
        oracle_session.rollback()
        mssql_session.rollback()
        oracle_session.rollback()
        common_functions.print_error('update_to_aws_cet',error)
        status = messages.FAILED
        message = str(error)[:70]
    return operation, status, message

