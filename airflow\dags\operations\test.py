# from commons.connectors import create_AWSCET_MSSQL_session
# from commons.connectors import create_oracle_session
# from sqlalchemy import text 
# from commons.models import ManualTempTable  

# oracle_session = create_oracle_session()
# session = create_AWSCET_MSSQL_session()

# row = oracle_session.query(ManualTempTable).first()

# procedure_call = text(
#             "EXEC USP_Insert_SAP_Employee_Data "
#             ":PERS_NO, :CO_CODE, :PERS_AREA, :EMPL_SUBGROUP, :PERS_SUBAREA, :COST_CENTER, :POSITION_CODE, "
#             ":POSITION_TEXT, :JOB_CODE, :JOB_TEXT, :PS_GROUP, :PAYROLL_AREA, :LAST_NAME, :FIRST_NAME, "
#             ":COMPLETE_NAME, :MIDDLE_NAME, :KNOWN_AS, :FORM_ADDR_KEY, :EMPLOYMENT_STATUS_CODE, :EMAIL_ID, "
#             ":CELL_NO, :CODE_SEX, :CODE_BLOOD_GRP, :CODE_MARITAL_STA, :CODE_RELGN, :DATE_ABSORP, :DATE_BIRTH, "
#             ":DATE_JOINING, :DATE_LAST_SVPC, :DATE_LAST_PROM, :DATE_PROB_COMP, :DATE_SEPARATION, "
#             ":CODE_DEL_REASON, :CODE_HANDICAP, :CODE_STATUS, :SMART_CARD_NO, :REPORT_TO, :CODE_RELGN_TEXT, "
#             ":CODE_MARITAL_STA_TEXT, :CODE_STATUS_TEXT, :EMPL_SUBGROUP_TEXT, :FUNCTION_GRP, :HIRING_REASON, "
#             ":REGION, :LOCATION, :OFF_NUM, :EXT_EMAIL, :ORG_UNIT, :PERS_MOBILE, :EMRG_MOBILE, :FAX_NUM, "
#             ":TRANSPORT_CODE, :OFF_ADDR, :DATE_LAST_PROM1, :DATE_LAST_PROM2, :DATE_LAST_PROM3, :OFFICE_MOBILE, "
#             ":DUMMY1, :DUMMY2, :DUMMY3, :DUMMY4, :DUMMY5, :LOCATION_CODE, :PA_CODE, :PSA_CODE, "
#             ":EMPLOYMENT_STATUS_TXT, :OU_L1_ID, :OU_L2_ID, :OU_L3_ID, :SUBFUN1_ID, :SUBFUN2_ID, :UPN_ID"
#         )

# params = {
#             "PERS_NO":  row.perno, #update in AWS-CET table,
#             "CO_CODE":  row.comp_codee,
#             "PERS_AREA" : row.pers_area,
#             "EMPL_SUBGROUP":    row.esubgroup,
#             "PERS_SUBAREA":     row.p_subarea,
#             "COST_CENTER":      row.costcenter,
#             "POSITION_CODE":    row.position1,
#             "POSITION_TEXT":    row.postxt,
#             "JOB_CODE":           row.job,
#             "JOB_TEXT":           row.jobtxt,
#             "PS_GROUP":           row.bus_area,
#             "PAYROLL_AREA":     row.payarea,
#             "LAST_NAME":          row.last_name,
#             "FIRST_NAME":       row.firstname,
#             "COMPLETE_NAME":    row.comp_name,
#             "MIDDLE_NAME":      row.midnm,
#             # "KNOWN_AS":            # = None
#             "FORM_ADDR_KEY":    row.title,
#             "EMPLOYMENT_STATUS_CODE":row.empstatus,
#             "EMAIL_ID":           row.imailid,
#             # "CELL_NO":            # r= None
#             "CODE_SEX":           row.gender,
#             "CODE_BLOOD_GRP":   row.bloodgroup,
#             "CODE_MARITAL_STA": row.famst,
#             "CODE_RELGN":       row.konfe,
#             "DATE_ABSORP":      date_cleaner(row.dobsorp),
#             "DATE_BIRTH":       date_cleaner(row.dob),
#             "DATE_JOINING":     date_cleaner(row.doj),
#             "DATE_LAST_SVPC":   date_cleaner(row.dsvcvp),
#             "DATE_LAST_PROM":   date_cleaner(row.dlprom),
#             "DATE_PROB_COMP":   None
#             "DATE_SEPARATION":  date_cleaner(row.dosep),
#             "CODE_DEL_REASON":  row.sepcode,
#             "CODE_HANDICAP":    row.egroup,
#             "CODE_STATUS":      None
#             "SMART_CARD_NO":    row.reporting,
#             "REPORT_TO":          row.religion,
#             "CODE_RELGN_TEXT":       =row.mar_status,
#             "CODE_MARITAL_STA_TEXT": = row.ptext,
#             "CODE_STATUS_TEXT": row.esgtxt,
#             "EMPL_SUBGROUP_TEXT":    = row.fun,
#             "FUNCTION_GRP":     row.mgtxt,
#             "HIRING_REASON":    row.reg_cod,
#             "REGION":            # re= None
#             "LOCATION":            # = None
#             "OFF_NUM":            res= row.loc_desc,
#             "EXT_EMAIL":          row.off_num,
#             "ORG_UNIT":           row.exmailid,
#             "PERS_MOBILE":      row.orgeh,
#             "EMRG_MOBILE":      row.pers_mobile,
#             "FAX_NUM":            res= row.emrg_mobile,
#             "TRANSPORT_CODE":   None
#             "OFF_ADDR":           row.transport_code,
#             "DATE_LAST_PROM1":  row.off_addr,
#             "DATE_LAST_PROM2":  date_cleaner(row.last_prom1),
#             "DATE_LAST_PROM3":  date_cleaner(row.last_prom2),
#             "OFFICE_MOBILE":    date_cleaner(row.last_prom3),
#             "DUMMY1":            resu= row.dummy1,
#             "DUMMY2":            resu= row.dummy2,
#             "DUMMY3":            resu= row.dummy3,
#             "DUMMY4":            resu= row.dummy4,
#             "DUMMY5":            resu= row.dummy5,
#             "LOCATION_CODE":    row.office_mobile,
#             "PA_CODE":            res= row.loc_cod,
#             "PSA_CODE":           row.pers_area,
#             "EMPLOYMENT_STATUS_TXT": = row.p_subarea,
#             "OU_L1_ID":           row.empstattxt,
#             "OU_L2_ID":           row.ou_level1,
#             "OU_L3_ID":           row.ou_level2,
#             "SUBFUN1_ID":       row.ou_level3,
#             "SUBFUN2_ID":       row.subfunction1_id,
#             "UPN_ID":            resu= row.subfunction2_id,
#         }

# session.execute(procedure_call, params)
# session.commit()