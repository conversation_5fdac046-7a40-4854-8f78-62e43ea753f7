# In-built imports...
import datetime
import uuid
from datetime import datetime
# Custom imports...
from commons import db_connections
from commons import db_queries
from commons import messages
from commons import common_functions
# from commons.models import EDPEmployeeDetails, EDPMetaData, EDPChangeLogHistory
from commons.models_v2 import EDPEmployeeDetails, EDPMetaData, EDPChangeLogHistory

postgres_session = db_connections.create_EDP_postgres_session()

def update_to_edp_cet(oracle_session,row,file_id):
    try:
        operation = None
        status = None
        message = None
        result = postgres_session.query(EDPEmployeeDetails).filter(EDPEmployeeDetails.Perno == row.perno.rjust(8,'0')).first()
        if result:
            operation = messages.UPDATED
            metadata_result = oracle_session.query(EDPMetaData).filter(EDPMetaData.person_no == row.perno).first()
            if not metadata_result:
                metadata_result = EDPMetaData()
                metadata_result.person_no = row.perno
                metadata_result.created_at = datetime.now()
                metadata_result.updated_at = datetime.now()
                oracle_session.add(metadata_result)
                oracle_session.commit()
            result.Perno = row.perno.rjust(8,'0') if row.perno else None
            result.PersArea = row.pers_area
            result.Patxt = row.patxt
            result.Egroup = row.egroup
            result.Ptext = row.ptext
            result.Esubgroup = row.esubgroup
            result.Esgtxt = row.esgtxt
            result.CompCode = row.comp_code.rjust(4,'0') if row.comp_code else None
            result.Ccodetxt = row.ccodetxt
            result.BusArea = row.bus_area
            result.Bustxt = row.bustxt
            result.PSubarea = row.p_subarea
            result.Psatxt = row.psatxt
            result.Payarea = row.payarea
            result.Paytxt = row.paytxt
            result.Costcenter = row.costcenter.rjust(10,'0') if row.costcenter else None
            result.Cosctrtxt = row.cosctrtxt
            result.Position1 = row.position1.rjust(8,'0') if row.position1 else None
            result.Postxt = row.postxt
            result.Job = row.job.rjust(8,'0') if row.job else None
            result.Jobtxt = row.jobtxt
            result.Fkbtx = row.fkbtx
            result.Initials = row.initials
            result.CompName = row.comp_name
            result.LastName = row.last_name
            result.Firstname = row.firstname
            result.Midnm = row.midnm
            result.Title = row.title
            result.OffNum = row.off_num
            result.Gender = row.gender
            result.Konfe = row.konfe
            result.Religion = row.religion
            result.Famst = row.famst
            result.MarStatus = row.mar_status
            result.Bloodgroup = row.bloodgroup
            result.Imailid = row.imailid
            result.Exmailid = row.exmailid
            result.Dosep = row.dobsorp[:10]
            result.Doj = row.doj[:10]
            result.Dob = row.dob[:10]
            result.Dsvcvp = row.dsvcvp[:10]
            result.Dlprom = row.dlprom[:10]
            result.Dosep = row.dosep[:10]
            result.Seprsn = row.seprsn
            result.Challenged = row.challenged
            result.Reporting = row.reporting.rjust(8,'0') if row.reporting else None
            result.Nameofreporting = row.nameofreporting
            result.Empstatus = row.empstatus
            result.Empstattxt = row.empstattxt
            result.Orgeh = row.orgeh.rjust(8,'0') if row.orgeh else None
            result.Orgtx = row.orgtx
            result.OfficeMobile = row.office_mobile
            result.PersMobile = row.pers_mobile
            result.EmrgMobile = row.emrg_mobile
            result.TransportCode = row.transport_code
            result.OffAddr = row.off_addr
            result.Sepcode = row.sepcode
            result.Fun = row.fun
            result.Funt = row.funt
            result.Mgtxt = row.mgtxt
            result.Flag1 = row.flag1
            result.LastProm1 = row.last_prom1[:10]
            result.LastProm2 = row.last_prom2[:10]
            result.LastProm3 = row.last_prom3[:10]
            result.Dummy1 = row.dummy1
            result.Dummy2 = row.dummy2
            result.Dummy3 = row.dummy3
            result.Dummy4 = row.dummy4
            result.Dummy5 = row.dummy5
            result.BhrPerno = row.bhr_perno.rjust(8,'0') if row.bhr_perno else None
            result.Mat1Mngr = row.mat1_mngr
            result.Mat2Mngr = row.mat2_mngr
            result.Mat3Mngr = row.mat3_mngr
            result.Mat4Mngr = row.mat4_mngr
            result.Mat5Mngr = row.mat5_mngr
            result.HeadHrPerno = row.head_hr_perno.rjust(8,'0') if row.head_hr_perno else None
            result.ErHrPerno = row.er_hr_perno.rjust(8,'0') if row.er_hr_perno else None
            result.OuShortTxt = row.ou_short_txt
            result.RegCod = row.reg_cod
            result.RegCodTxt = row.reg_cod_txt
            result.JobtxtShort = row.jobtxt_short
            result.OuLevel1 = row.ou_level1
            result.OuLevel1ShortTxt = row.ou_level1_short_txt
            result.OuLevel1LongTxt = row.ou_level1_long_txt
            result.OuLevel2 = row.ou_level1
            result.OuLevel2ShortTxt = row.ou_level2_short_txt
            result.OuLevel2LongTxt = row.ou_level2_long_txt
            result.OuLevel3 = row.ou_level3
            result.OuLevel3ShortTxt = row.ou_level3_short_txt
            result.OuLevel3LongTxt = row.ou_level3_long_txt
            result.FuncId01 = row.func_id01
            result.FunctionText = row.function_text
            result.Subfunction1Id = row.subfunction1_id
            result.Subfunction1Tex = row.subfunction1_tex
            result.Subfunction2Id = row.subfunction2_id.rjust(4,'0') if row.subfunction2_id else None
            result.Subfunction2Tex = row.subfunction2_tex
            result.LocCod = row.loc_cod
            result.LocDesc = row.loc_desc
            result.Add1 = row.add1
            result.Add2 = row.add2
            result.Add3 = row.add3
            result.Add4 = row.add4
            result.CityTown = row.city_town
            result.State = row.state
            result.PinCode = row.pin_code
            result.Country = row.country
            result.A962Perno = row.a962_perno.rjust(8,'0')if row.a962_perno else None
            result.A962Cname = row.a962_cname
            result.modified_datetime = datetime.now()
            postgres_session.commit()

            metadata_result = oracle_session.query(EDPMetaData).filter(EDPMetaData.person_no == row.perno).first()
            if metadata_result:
                edp_change_log = EDPChangeLogHistory()
                edp_change_log.person_no = metadata_result.person_no
                edp_change_log.created_at = metadata_result.created_at
                edp_change_log.updated_at = metadata_result.updated_at
                edp_change_log.file_id = file_id
                edp_change_log.log_created_at = datetime.now()
                oracle_session.add(edp_change_log)
                oracle_session.commit()

                metadata_result.person_no = row.perno
                metadata_result.updated_at = datetime.now()
                oracle_session.commit()
            status = messages.SUCCESS
            message = None
        else:
            operation = messages.INSERTED
            result = EDPEmployeeDetails()
            result.edp_cet_id = uuid.uuid4()
            result.Perno = row.perno.rjust(8,'0') if row.perno else None
            result.PersArea = row.pers_area
            result.Patxt = row.patxt
            result.Egroup = row.egroup
            result.Ptext = row.ptext
            result.Esubgroup = row.esubgroup
            result.Esgtxt = row.esgtxt
            result.CompCode = row.comp_code.rjust(4,'0') if row.comp_code else None
            result.Ccodetxt = row.ccodetxt
            result.BusArea = row.bus_area
            result.Bustxt = row.bustxt
            result.PSubarea = row.p_subarea
            result.Psatxt = row.psatxt
            result.Payarea = row.payarea
            result.Paytxt = row.paytxt
            result.Costcenter = row.costcenter.rjust(10,'0') if row.costcenter else None
            result.Cosctrtxt = row.cosctrtxt
            result.Position1 = row.position1.rjust(8,'0') if row.position1 else None
            result.Postxt = row.postxt
            result.Job = row.job.rjust(8,'0') if row.job else None
            result.Jobtxt = row.jobtxt
            result.Fkbtx = row.fkbtx
            result.Initials = row.initials
            result.CompName = row.comp_name
            result.LastName = row.last_name
            result.Firstname = row.firstname
            result.Midnm = row.midnm
            result.Title = row.title
            result.OffNum = row.off_num
            result.Gender = row.gender
            result.Konfe = row.konfe
            result.Religion = row.religion
            result.Famst = row.famst
            result.MarStatus = row.mar_status
            result.Bloodgroup = row.bloodgroup
            result.Imailid = row.imailid
            result.Exmailid = row.exmailid
            result.Dosep = row.dobsorp[:10]
            result.Doj = row.doj[:10]
            result.Dob = row.dob[:10]
            result.Dsvcvp = row.dsvcvp[:10]
            result.Dlprom = row.dlprom[:10]
            result.Dosep = row.dosep[:10]
            result.Seprsn = row.seprsn
            result.Challenged = row.challenged
            result.Reporting = row.reporting.rjust(8,'0') if row.reporting else None
            result.Nameofreporting = row.nameofreporting
            result.Empstatus = row.empstatus
            result.Empstattxt = row.empstattxt
            result.Orgeh = row.orgeh.rjust(8,'0') if row.orgeh else None
            result.Orgtx = row.orgtx
            result.OfficeMobile = row.office_mobile
            result.PersMobile = row.pers_mobile
            result.EmrgMobile = row.emrg_mobile
            result.TransportCode = row.transport_code
            result.OffAddr = row.off_addr
            result.Sepcode = row.sepcode
            result.Fun = row.fun
            result.Funt = row.funt
            result.Mgtxt = row.mgtxt
            result.Flag1 = row.flag1
            result.LastProm1 = row.last_prom1[:10]
            result.LastProm2 = row.last_prom2[:10]
            result.LastProm3 = row.last_prom3[:10]
            result.Dummy1 = row.dummy1
            result.Dummy2 = row.dummy2
            result.Dummy3 = row.dummy3
            result.Dummy4 = row.dummy4
            result.Dummy5 = row.dummy5
            result.BhrPerno = row.bhr_perno.rjust(8,'0') if row.bhr_perno else None
            result.Mat1Mngr = row.mat1_mngr
            result.Mat2Mngr = row.mat2_mngr
            result.Mat3Mngr = row.mat3_mngr
            result.Mat4Mngr = row.mat4_mngr
            result.Mat5Mngr = row.mat5_mngr
            result.HeadHrPerno = row.head_hr_perno.rjust(8,'0') if row.head_hr_perno else None
            result.ErHrPerno = row.er_hr_perno.rjust(8,'0') if row.er_hr_perno else None
            result.OuShortTxt = row.ou_short_txt
            result.RegCod = row.reg_cod
            result.RegCodTxt = row.reg_cod_txt
            result.JobtxtShort = row.jobtxt_short
            result.OuLevel1 = row.ou_level1
            result.OuLevel1ShortTxt = row.ou_level1_short_txt
            result.OuLevel1LongTxt = row.ou_level1_long_txt
            result.OuLevel2 = row.ou_level1
            result.OuLevel2ShortTxt = row.ou_level2_short_txt
            result.OuLevel2LongTxt = row.ou_level2_long_txt
            result.OuLevel3 = row.ou_level3
            result.OuLevel3ShortTxt = row.ou_level3_short_txt
            result.OuLevel3LongTxt = row.ou_level3_long_txt
            result.FuncId01 = row.func_id01
            result.FunctionText = row.function_text
            result.Subfunction1Id = row.subfunction1_id
            result.Subfunction1Tex = row.subfunction1_tex
            result.Subfunction2Id = row.subfunction2_id.rjust(4,'0') if row.subfunction2_id else None
            result.Subfunction2Tex = row.subfunction2_tex
            result.LocCod = row.loc_cod
            result.LocDesc = row.loc_desc
            result.Add1 = row.add1
            result.Add2 = row.add2
            result.Add3 = row.add3
            result.Add4 = row.add4
            result.CityTown = row.city_town
            result.State = row.state
            result.PinCode = row.pin_code
            result.Country = row.country
            result.A962Perno = row.a962_perno.rjust(8,'0')if row.a962_perno else None
            result.A962Cname = row.a962_cname
            result.created_datetime = datetime.now()
            result.modified_datetime = datetime.now()
            postgres_session.add(result)
            postgres_session.commit()

            if not oracle_session.query(EDPMetaData).filter(EDPMetaData.person_no == row.perno).first():
                metadata_result = EDPMetaData()
                metadata_result.person_no = row.perno
                metadata_result.created_at = datetime.now()
                metadata_result.updated_at = datetime.now()
                oracle_session.add(metadata_result)
                oracle_session.commit()

            status = messages.SUCCESS
            message = None
    except Exception as error:
        oracle_session.rollback()
        common_functions.print_error('update_to_edp_cet()',error)
        oracle_session.rollback()
        status = messages.FAILED
        message = str(error)[:100]
    return operation, status, message 
        


# EDP CET Sync
# def sync_record_in_edp_cet(curr, record):
#     status = ""
#     message = ""
#     operation = ""
    
#     if check_if_exists_in_edp_table(curr, record['perno']):
#         operation = messages.UPDATED
#         status, message = update_to_edp_table(record)
#     else:
#         operation = messages.INSERTED
#         status, message = False, "Perno not present in CET"
#     return status, message, operation

# def check_if_exists_in_edp_table(curr,person_no):
#     curr.execute(db_queries.SELECT_EXISTS_EDP_TABLE,[person_no])
#     return curr.fetchone()[0]

# def update_to_edp_table(row):
#     try:
#         edp_curr = connectors.create_EDP_postgres_cursor()
#         edp_row = {k: v or '' for (k, v) in row.items()}
#         # edp_row = row.copy()
#         edp_row['perno'] = edp_row['perno'].rjust(8,'0')
#         edp_row['comp_code'] = edp_row['comp_code'].rjust(4,'0')
#         edp_row['costcenter'] = edp_row['costcenter'].rjust(10,'0')
#         edp_row['position1'] = edp_row['position1'].rjust(8,'0')
#         edp_row['job'] = edp_row['job'].rjust(8,'0')
#         edp_row['reporting'] = edp_row['reporting'].rjust(8,'0')
#         edp_row['orgeh'] = edp_row['orgeh'].rjust(8,'0')
#         edp_row['bhr_perno'] = edp_row['bhr_perno'].rjust(8,'0')
#         edp_row['head_hr_perno'] = edp_row['head_hr_perno'].rjust(8,'0')
#         edp_row['er_hr_perno'] = edp_row['er_hr_perno'].rjust(8,'0')
#         edp_row['subfunction2_id'] = edp_row['subfunction2_id'].rjust(4,'0')
#         edp_row['a962_perno'] = edp_row['a962_perno'].rjust(8,'0')
#         data = list(edp_row.values())
#         data.append(edp_row['perno'])
#         SQL = """
#             UPDATE edp_employee_details_sankey
#             SET "Perno"=%s, "CompCode"=%s, "PersArea"=%s, "Patxt"=%s, "Egroup"=%s, "Ptext"=%s, "Esubgroup"=%s, "Esgtxt"=%s,"Ccodetxt"=%s, "BusArea"=%s, "Bustxt"=%s, "PSubarea"=%s, "Psatxt"=%s, "Payarea"=%s, "Paytxt"=%s, "Costcenter"=%s, "Cosctrtxt"=%s, "Position1"=%s, "Postxt"=%s, "Job"=%s, "Jobtxt"=%s, "Fkbtx"=%s, "Initials"=%s, "CompName"=%s, "LastName"=%s, "Firstname"=%s, "Midnm"=%s, "Title"=%s, "OffNum"=%s, "Gender"=%s, "Konfe"=%s, "Religion"=%s, "Famst"=%s, "MarStatus"=%s, "Bloodgroup"=%s, "Imailid"=%s, "Exmailid"=%s, "Dobsorp"=%s, "Doj"=%s, "Dob"=%s, "Dsvcvp"=%s, "Dlprom"=%s, "Dosep"=%s, "Seprsn"=%s, "Challenged"=%s, "Reporting"=%s, "Nameofreporting"=%s, "Empstatus"=%s, "Empstattxt"=%s, "Orgeh"=%s, "Orgtx"=%s, "OfficeMobile"=%s, "PersMobile"=%s, "EmrgMobile"=%s, "TransportCode"=%s, "OffAddr"=%s, "Sepcode"=%s, "Fun"=%s, "Funt"=%s, "Mgtxt"=%s, "Flag1"=%s, "LastProm1"=%s, "LastProm2"=%s, "LastProm3"=%s, "Dummy1"=%s, "Dummy2"=%s, "Dummy3"=%s, "Dummy4"=%s, "Dummy5"=%s, "BhrPerno"=%s, "Mat1Mngr"=%s, "Mat2Mngr"=%s, "Mat3Mngr"=%s, "Mat4Mngr"=%s, "Mat5Mngr"=%s, "HeadHrPerno"=%s, "ErHrPerno"=%s, "OuShortTxt"=%s, "RegCod"=%s, "RegCodTxt"=%s, "JobtxtShort"=%s, "OuLevel1"=%s, "OuLevel1ShortTxt"=%s, "OuLevel1LongTxt"=%s, "OuLevel2"=%s, "OuLevel2ShortTxt"=%s, "OuLevel2LongTxt"=%s, "OuLevel3"=%s, "OuLevel3ShortTxt"=%s, "OuLevel3LongTxt"=%s, "FuncId01"=%s, "FunctionText"=%s, "Subfunction1Id"=%s, "Subfunction1Tex"=%s, "Subfunction2Id"=%s, "Subfunction2Tex"=%s, "LocCod"=%s, "LocDesc"=%s, "Add1"=%s, "Add2"=%s, "Add3"=%s, "Add4"=%s, "CityTown"=%s, "State"=%s, "PinCode"=%s, "Country"=%s, "A962Perno"=%s, "A962Cname"=%s, modified_datetime=CURRENT_TIMESTAMP
#             WHERE "Perno" = %s;
#             """
#         edp_curr.execute(SQL,data)
#         return True, 'success'
#     except Exception as error:
#         return False, str(error)
