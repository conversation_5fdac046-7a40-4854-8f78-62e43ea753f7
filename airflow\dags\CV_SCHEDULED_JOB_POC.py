from airflow import <PERSON><PERSON>
from datetime import datetime
from airflow.operators.python import PythonOperator

# Custom imports...
from commons.configs import LOAD_TO_TEMP
from scripts.schedule_job_poc import (
    load_to_temp_table,
    create_dynamic_tasks,
    send_success_email_and_update_log
)
# from commons.models import CVTempTable
from commons.models_v2 import CVTempTable
from commons.configs import (
    CV_SFTP_CONN_ID,
    CV_SFTP_ORIGINAL_PATH,
    CV_SFTP_ARCHIVED_PATH,
)

# DAG initialization...
with DAG(
    dag_id="cv_schedule_job_poc",
    start_date=datetime(2023, 7, 21),
    schedule_interval=None,
    catchup=False,
) as dag:

    # Task 1 Initialization...
    load_to_temp_ = PythonOperator(
        task_id=LOAD_TO_TEMP,
        python_callable=load_to_temp_table,
        do_xcom_push=True,
        op_kwargs={
            "sftp_connection_id": CV_SFTP_CONN_ID,
            "sftp_path": CV_SFTP_ORIGINAL_PATH,
            "sftp_archived_path": CV_SFTP_ARCHIVED_PATH,
            "temp_table": CVTempTable,
        },
        dag=dag,
    )

    # Create dynamic tasks
    dynamic_tasks = create_dynamic_tasks(dag, CVTempTable)

    send_email = PythonOperator(
        task_id="send_email",
        python_callable=send_success_email_and_update_log,
        do_xcom_push=True,
        dag=dag,
    )
    # Task sequencing & call...
    for task in dynamic_tasks:
        load_to_temp_ >> task
        task >> send_email
