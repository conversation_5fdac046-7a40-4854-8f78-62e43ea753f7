# In-built imports...
from airflow import D<PERSON>
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.configs import DAG2_DELETE_JOBS, DAG2_DELETE_JOB_SCHEDULER, DELETE_TASK, DISABLE_ENABLED_ID
from scripts.delete_job import dag2_delete_disabled_task
from scripts.sync_exclude_job import sync_exclude_job_script


# DAG initialization...
with DAG(
    dag_id = DAG2_DELETE_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG2_DELETE_JOB_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    delete_task = PythonOperator(
        task_id = DELETE_TASK,
        python_callable = dag2_delete_disabled_task,
        do_xcom_push = True    
    )
    sync_exclude_job = PythonOperator(
        task_id = DISABLE_ENABLED_ID,
        python_callable = sync_exclude_job_script,
        do_xcom_push = True    
    )
    
    # Task 1 Call...
    delete_task>>sync_exclude_job