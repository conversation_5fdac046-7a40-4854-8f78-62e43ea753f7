version: '2.3'
x-airflow-common:
  &airflow-common
  image: airflow
  env_file:
    - development.env
  labels:
    logging: "promtail"
    logging_jobname: "containerlogs"
  environment:
    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:<EMAIL>:5431/airflow
    AIRFLOW__CELERY__RESULT_BACKEND: db+postgresql://airflow:<EMAIL>:5431/airflow

  volumes:
    - ./airflow/dags:/root/airflow/dags
    - ./airflow/logs:/root/airflow/logs
    - ./airflow/plugins:/root/airflow/plugins
    - ./odbc.ini:/etc/odbc.ini
  extra_hosts:
    - "host.docker.internal:host-gateway"
  user: "${AIRFLOW_UID:-0}:1"
  networks:
    - emp_onb

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    container_name: postgres
    ports:
      - 5431:5432
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 10s
      retries: 5
      start_period: 5s
    restart: always
    
  redis:
    image: redis:latest
    labels:
      logging: "promtail"
      logging_jobname: "containerlogs"
    container_name: redis
    expose:
      - 6379
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 30s
      retries: 50
    restart: always
    networks:
     - emp_onb

  airflow-celery:
    <<: *airflow-common
    container_name: airlow-celery
    command: airflow celery worker
    restart: always
    depends_on:
      - airflow-init

  airflow-webserver:
    <<: *airflow-common
    container_name: airlow-webserver
    command: airflow webserver
    ports:
      - 5001:8080
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 10s
      timeout: 10s
      retries: 5
    restart: always
    depends_on:
      - airflow-scheduler

  airflow-scheduler:
    <<: *airflow-common
    container_name: airlow-scheduler
    command: airflow scheduler
    restart: always
    depends_on:
      - airflow-celery

  airflow-init:
    <<: *airflow-common
    container_name: airlow-init
    command: airflow db init
    depends_on:
      - redis
      - postgres

networks:
   emp_onb:
     name: emp_onb

volumes:
  postgres-db-volume:
