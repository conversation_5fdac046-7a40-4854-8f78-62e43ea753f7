# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.configs import DAG4_DAILY_REPORT_JOBS, DAG4_DAILY_REPORT_JOB_SCHEDULER, TRIGGER_EMAILS
from scripts.daily_report_job import trigger_email


# DAG initialization...
with DAG(
    dag_id = DAG4_DAILY_REPORT_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG4_DAILY_REPORT_JOB_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    trigger_email_task = PythonOperator(
        task_id = TRIGGER_EMAILS,
        python_callable = trigger_email,
        do_xcom_push = True    
    )
    
    # Task 1 Call...
    trigger_email_task