# In-built imports...
from airflow.providers.ssh.hooks.ssh import <PERSON><PERSON><PERSON><PERSON>
from io import BytesIO
import os
from datetime import datetime , timedelta
import requests , json
# Custom imports...
from operations.active_directory_tscmsl_operations import bulk_update_upn_tscmsl
from commons.messages import  DIRECTORY_IS_EMPTY, FILE_LOG_NOT_FOUND, FILE_NOT_FOUND, SCHEDULED_JOB, <PERSON><PERSON>ED, FA<PERSON>ED, SKIP, <PERSON>UCCE<PERSON>, <PERSON>ILED, RUNNING , INTERRUPTED, FILE_ID_NOT_FOUND
from commons.configs import EV_SFTP_CONN_ID, EV_SFTP_ORIGINAL_PATH, EV_SFTP_ARCHIVED_PATH, PV_SFTP_CONN_ID, PV_SFTP_ORIGINAL_PATH, PV_SFTP_ARCHIVED_PATH, CV_SFTP_CONN_ID, CV_SFTP_ORIGINAL_PATH, CV_SFTP_ARCHIVED_PATH, LOAD_TO_TEM<PERSON>, SAP_TSCMSL_USER_DETAILS_BY_DATE, TMLBSL_SFTP_ORIGINAL_PATH, TMLBSL_SFTP_ARCHIVED_PATH, TMLBSL_SFTP_CONN_ID , SAP_TTL_USER_DETAILS_BY_DATE, TMLDA_SFTP_ARCHIVED_PATH, TMLDA_SFTP_CONN_ID, TMLDA_SFTP_ORIGINAL_PATH, TMML_CO_CODE, TSCMSL_COMP, TSCMSL_FILE_NAME, TSCMSL_SFTP_ARCHIVED_PATH, TSCMSL_SFTP_CONN_ID, TSCMSL_SFTP_ORIGINAL_PATH, TMML_SFTP_CONN_ID, TMML_SFTP_ORIGINAL_PATH, TMML_SFTP_ARCHIVED_PATH
from commons.db_connections import create_new_oracle_session, create_oracle_session
from commons.common_functions import update_file_row_log, validate_fields, validate_file, load_data_to_temp,load_data_to_temp_ttl, update_file_log, print_error, update_file_log, update_file_log, update_file_log, create_file_log
from commons.common_functions_tscmsl import create_file_log_tscmsl, update_file_log_tscmsl, load_data_to_temp_tscmsl, validate_file_tscmsl, load_data_to_temp_for_tscmsl
from commons.notifications import  send_failed_notification, send_success_email, send_failed_notification
from commons.mains import perform_operations, perform_operations_tscmsl, update_upn_in_cet
# from commons.models import FileLogs, ESubGroup , CVTempTable , EVTempTable, FileRowLogs , PVTempTable , TMBSLTempTable ,TTLTempTable, TSCMSLTempTable, TMMLTempTable
from commons.models_v2 import ESubGroupTMML, ProcessLogs, ESubGroup , CVTempTable , EVTempTable, ProcessRowLogs , PVTempTable , TMBSLTempTable, TMDATempTable, TTLTempTable, TSCMSLTempTable, TMMLTempTable
from commons.configs import REQUEST_BODY, HEADER , PARTNER_ONBOARDING_ID_CREATIONS , NDA_HEADER


def extract_and_load_common(sftp_conn_id, sftp_path, sftp_archived_path, temp_table, job_name):
    try:
        sftp_hook = SSHHook(ssh_conn_id = sftp_conn_id)
        sftp_client = sftp_hook.get_conn().open_sftp()
        files = sftp_client.listdir(sftp_path)
        # Connect to local DB...
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        # Check for file...
        if not files:
            file_log_object = {
                "trigger_type": SCHEDULED_JOB,
                "status": FAILED,
                "job_name":job_name
            }
            file_log = create_file_log(new_oracle_session, file_log_object)
            raise Exception('Directory is empty')
        # fetch file ...
        file = sorted(
            [file_path for file_path in files if ('TMGSL' in file_path or 'TML' in file_path or'TMBSL' in file_path or 'TMDA' in file_path) and 'CET' in file_path and 'Full' not in file_path and 'CSV' in file_path],
            key=(lambda file_path: sftp_client.stat(sftp_path + file_path).st_mtime)
        )
        
        if not file:
            file_log_object = {
                "trigger_type": SCHEDULED_JOB,
                "status": FAILED,
                "job_name":job_name
            }
            file_log = create_file_log(new_oracle_session, file_log_object)
            raise Exception('File not Found')
        fp = BytesIO()
        file = file[0]
        sftp_client.getfo(sftp_path + file, fp)
        data = fp.getvalue().decode("unicode_escape")
        file_log_object = {
            "job_name":job_name,
            "file_name": file,
            "trigger_type": SCHEDULED_JOB,
            "file_format": os.path.splitext(file)[1],
            "status": RUNNING,
            "row_count": "",
            "column_count": "",
        }
        status, file_log, file_data = validate_file(file_log_object, data)
        file_log = create_file_log(new_oracle_session, file_log)
        if not status:
            if file_log.status == FAILED:
                sftp_client.rename(sftp_path+file,sftp_archived_path+file)
                pass
            raise Exception(file_log.reason)
        status, reason = load_data_to_temp(new_oracle_session,file_data ,temp_table)
        if not status:
            file_log['reason'] = reason
            raise Exception(file_log['reason'])
        else:
            # ----------------------Move file to archival-------------------------------
            sftp_client.rename(sftp_path+file,sftp_archived_path+file)
            #pass
        file_log = update_file_log(new_oracle_session,file_log=file_log,commit=True)
        return file_log.file_id
    except Exception as error:
        print_error("extract_and_load_",error)
        file_log = update_file_log(new_oracle_session,file_log=file_log,reason=str(error)[:60],status=FAILED,commit=True)
        send_failed_notification(file_logs=file_log)
        raise Exception(error)

# DAG 5 : Schedule Jobs - Task 1...
def extract_and_load_EV():
    try:
        file_id = extract_and_load_common(EV_SFTP_CONN_ID, EV_SFTP_ORIGINAL_PATH, EV_SFTP_ARCHIVED_PATH , EVTempTable, "EV")
        return file_id
    except Exception as error:
        print_error("extract_and_load_EV",error)
        raise Exception(error)

# DAG 5 : Schedule Jobs - Task 1...
def extract_and_load_PV():
    try:
        file_id = extract_and_load_common(PV_SFTP_CONN_ID, PV_SFTP_ORIGINAL_PATH, PV_SFTP_ARCHIVED_PATH , PVTempTable, "PV")
        return file_id
    except Exception as error:
        print_error("extract_and_load_PV",error)
        raise Exception(error)

# DAG 5 : Schedule Jobs - Task 1...
def extract_and_load_CV():
    try:
        file_id = extract_and_load_common(CV_SFTP_CONN_ID, CV_SFTP_ORIGINAL_PATH, CV_SFTP_ARCHIVED_PATH , CVTempTable, "CV")
        return file_id
    except Exception as error:
        print_error("extract_and_load_CV",error)
        raise Exception(error)

# DAG 5 : Schedule Jobs - Task 1...
def extract_and_load_TMLBSL():
    try:
        file_id = extract_and_load_common(TMLBSL_SFTP_CONN_ID, TMLBSL_SFTP_ORIGINAL_PATH, TMLBSL_SFTP_ARCHIVED_PATH ,TMBSLTempTable, "TMLBSL")
        return file_id
    except Exception as error:
        print_error("extract_and_load_CV",error)
        raise Exception(error)

    
# DAG 5 : Schedule Jobs - Task 1...
def extract_and_load_TMML():
    try:
        file_id = extract_and_load_common(TMML_SFTP_CONN_ID, TMML_SFTP_ORIGINAL_PATH, TMML_SFTP_ARCHIVED_PATH ,TMMLTempTable)
        return file_id
    except Exception as error:
        print_error("extract_and_load_TMML",error)
        raise Exception(error)
    
def extract_and_load_TMLDA():
    try:
        file_id = extract_and_load_common(TMLDA_SFTP_CONN_ID, TMLDA_SFTP_ORIGINAL_PATH, TMLDA_SFTP_ARCHIVED_PATH , TMDATempTable, "TMDA")
        return file_id
    except Exception as error:
        print_error("extract_and_load_TMLDA",error)
        raise Exception(error)
    
def extract_and_load_TTL():
    try:
        today_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        file_log_object = {
            "file_name": f"TTL_{today_date.replace('-','')}_JOB",
            "trigger_type": SCHEDULED_JOB,
            "file_format": "",
            "job_name":"TTL"
        }
        file_log = create_file_log(new_oracle_session, file_log_object)
        requests_body = {**REQUEST_BODY}
        requests_body["date"] = today_date
        requests_body["company_code"] = 'TTL'
        response = requests.get(SAP_TTL_USER_DETAILS_BY_DATE , headers=HEADER , params=requests_body)
        if response.status_code == 200 :
            response = json.loads(response.content)
            if 'result' in response and response['result']:
                data = response['result']
                status, reason = load_data_to_temp_ttl(new_oracle_session,data)
                if not status:
                    raise Exception(reason)
                else :
                    file_log = update_file_log(new_oracle_session,file_log=file_log,row_count=len(data),column_count=len(data[0].keys()),status=RUNNING,commit=True)
            else:
                load_data_to_temp_ttl(new_oracle_session,response['result'])
                print("extract_and_load_TTL",response)
            return file_log.file_id
        else:
            print("extract_and_load_TTL",response.status_code) 
        return file_log.file_id     
    except Exception as error:
        print_error("extract_and_load_TTL",error)
        file_log = update_file_log(new_oracle_session,file_log=file_log,reason=response["message"],status=FAILED,commit=True)
        raise Exception(error)

def schedule_load_to_cet(ti , temp_table = None, cet=None, comp_code = None):
    try:
        # Connect to local DB...
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        # Fetch data from tamp table
        file_id = ti.xcom_pull(task_ids = LOAD_TO_TEMP)
        if not file_id:
            raise Exception('File ID not found')
        file_log = new_oracle_session.query(ProcessLogs).filter(ProcessLogs.file_id == file_id).first()
        if not file_log:
            raise Exception('File Log not found')
        # Process row log exists
        
        disabled_data = new_oracle_session.query(temp_table).filter(temp_table.empstatus == 0)
        for item in disabled_data:
            sync_data_from_temp_table(new_oracle_session, item, file_log.file_id, cet)
            
        white_collar_data = new_oracle_session.query(temp_table).join(ESubGroup, temp_table.esubgroup == ESubGroup.esubgroup_code).filter(temp_table.empstatus != 0) if comp_code != TMML_CO_CODE else new_oracle_session.query(temp_table).join(ESubGroupTMML, ESubGroupTMML.esubgroup_type==True).filter(ESubGroupTMML.esubgroup_code == temp_table.esubgroup).filter(temp_table.empstatus != 0)
        for item in white_collar_data:
            sync_data_from_temp_table(new_oracle_session, item, file_log.file_id, cet)
            
        blue_coller_data = new_oracle_session.query(temp_table).filter(~temp_table.esubgroup.in_(new_oracle_session.query(ESubGroup.esubgroup_code))).filter(temp_table.empstatus != 0) if comp_code != TMML_CO_CODE else new_oracle_session.query(temp_table).join(ESubGroupTMML, ESubGroupTMML.esubgroup_type==False).filter(ESubGroupTMML.esubgroup_code == temp_table.esubgroup).filter(temp_table.empstatus != 0)
        for item in blue_coller_data:
            sync_data_from_temp_table(new_oracle_session, item, file_log.file_id, cet)
            
    except Exception as error:
        raise Exception(error)

def sync_data_from_temp_table(new_oracle_session, item, file_id, cet):
    file_row_log = new_oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno==item.perno,ProcessRowLogs.file_id == file_id).first()
    if not file_row_log:
        file_row_log = update_file_row_log(
            new_oracle_session, 
            perno = item.perno, 
            comp_code = item.comp_code, 
            is_upn_update = False, 
            file_id = file_id,
            commit = False
        )
    status, error = validate_fields(
        new_oracle_session, 
        item
    )
    if status == False and error == SKIP:
        error = "Deployed Employee"
        file_row_log = update_file_row_log(
            new_oracle_session,
            file_row_log = file_row_log,
            ad_operation=SKIP,
            aws_cet_operation=SKIP,
            azure_cet_operation=SKIP,
            edp_operation=SKIP,
            nextgen_operation=SKIP,
            keycloak_operation=SKIP,
            edp_status = "success",
            edp_reason = error,
            aws_cet_status = "success",
            aws_cet_reason = error,
            azure_cet_status = "success",
            azure_cet_reason = error,
            ad_status = "success",
            ad_reason = error,
            keycloak_status = "success",
            keycloak_reason = error,
            nextgen_status = "success",
            nextgen_reason = error,
            is_upn_update = False,
            commit = True
        )
        
    elif status == False:
        file_row_log = update_file_row_log(
            new_oracle_session,
            file_row_log = file_row_log,
            edp_status = FAILED,
            edp_reason = error,
            nextgen_status = FAILED,
            nextgen_reason = error,
            aws_cet_status = FAILED,
            aws_cet_reason = error,
            azure_cet_status = FAILED,
            azure_cet_reason = error,
            ad_status = FAILED,
            ad_reason = error,
            keycloak_status = FAILED,
            keycloak_reason = error,
            is_upn_update = False,
            commit = True
        )
    else:
        perform_operations(new_oracle_session, item, file_id, file_row_log, cet)

# DAG 5 : Schedule Jobs - Task 2...
def schedule_load_to_cet_cv_(ti):
    try:
        result = schedule_load_to_cet( ti,CVTempTable)
    except Exception as error:
        print_error("extract_and_load_cv",error)
        raise Exception(error)

def schedule_load_to_cet_ev_(ti):
    try:
        result = schedule_load_to_cet(ti,EVTempTable)
    except Exception as error:
        print_error("extract_and_load_ev",error)
        raise Exception(error)

def schedule_load_to_cet_pv_(ti):
    try:
        result = schedule_load_to_cet(ti,PVTempTable)
    except Exception as error:
        print_error("extract_and_load_pv",error)
        raise Exception(error)

def schedule_load_to_cet_tmbsl_(ti):
    try:
        result = schedule_load_to_cet(ti,TMBSLTempTable)
    except Exception as error:
        print_error("extract_and_load_tmbsl",error)
        raise Exception(error)

def schedule_load_to_cet_ttl_(ti):
    try:
        result = schedule_load_to_cet(ti,TTLTempTable)
    except Exception as error:
        print_error("extract_and_load_ttl",error)
        raise Exception(error)
    
def schedule_load_to_cet_tmml_(ti):
    try:
        result = schedule_load_to_cet(ti,TMMLTempTable)
    except Exception as error:
        print_error("extract_and_load_tmml",error)
        raise Exception(error)
    # -------------------------------- NDA Domain ID creation  --------------------------------

def nda_domain_id_creation():
    try:
        response = requests.post(PARTNER_ONBOARDING_ID_CREATIONS , headers=NDA_HEADER , data=json.dumps({}))
        if response.status_code == 200 :
            data = response.json()
            if data['status'] == 1:
                print(f'nad_domain_id_success data - {data["data"]}')
            else :
                print(f'nad_domain_id_success - message - {data["message"]}')
        else :
            print("nda_domain_id_creation", response)
    except Exception as error:
        print_error("nda_domain_id_creation",error)
        raise Exception(error)


# -------------------------------------------Smart City--------------------------------------------
def schedule_load_to_cet_tscml(ti):
    try:
        # Connect to local DB...
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        temp_table = TSCMSLTempTable
        # Fetch data from tamp table
        file_id = ti.xcom_pull(task_ids = LOAD_TO_TEMP)
        if not file_id:
            raise Exception(FILE_ID_NOT_FOUND)
        file_log = new_oracle_session.query(ProcessLogs).filter(ProcessLogs.file_id == file_id).first()
        if not file_log:
            raise Exception(FILE_LOG_NOT_FOUND)
        disabled_data = new_oracle_session.query(temp_table).filter(temp_table.empstatus == 0)
        for items in disabled_data:
            perform_operations_tscmsl(new_oracle_session, items, file_log.file_id)
        white_collar_data = new_oracle_session.query(temp_table).filter(temp_table.empstatus != 0)
        for items in white_collar_data:
            perform_operations_tscmsl(new_oracle_session, items, file_log.file_id)
        if bulk_update_upn_tscmsl(file_log.file_id, new_oracle_session):
            print("All UPN Updated..")
            new_oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.file_id==file_id).filter(ProcessRowLogs.ad_status=='success').filter((ProcessRowLogs.ad_operation == 'insert') | (ProcessRowLogs.ad_operation == 'update')).update({ProcessRowLogs.is_upn_update : True})
        else : 
            print("Bulk UPN Update Failed")
            
        update_file_log_tscmsl(new_oracle_session, file_log = file_log, status = SUCCESS, reason = None, commit = True)
        send_success_email(file_log.file_id)
    except Exception as error:
        update_file_log_tscmsl(new_oracle_session, file_log = file_log, status = FAILED, reason = str(error)[:50], commit = True)
        send_failed_notification(file_log)
        print_error("schedule_load_to_cet_tscml",error)
        raise Exception(error)

def extract_and_load_TSCMSL():
    try:
        # extract and load data from file
        file_id = extract_and_load_by_file_TSCMSL(TSCMSL_SFTP_CONN_ID, TSCMSL_SFTP_ORIGINAL_PATH, TSCMSL_SFTP_ARCHIVED_PATH , TSCMSLTempTable, "TMLSCMSL")
        # extract and load data from api
        # file_id = extract_and_load_by_api_TSCMSL()
        return file_id
    except Exception as error:
        print_error("extract_and_load_TSCMSL",error)
        raise Exception(error)

def extract_and_load_by_api_TSCMSL():
    try:
        today_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        file_log_object = {
            "file_name": f"TSCMSL_{today_date.replace('-','')}_JOB",
            "trigger_type": SCHEDULED_JOB,
            "file_format": "",
        }
        file_log = create_file_log_tscmsl(new_oracle_session, file_log_object)
        requests_body = {**REQUEST_BODY}
        requests_body["date"] = today_date
        response = requests.get(SAP_TSCMSL_USER_DETAILS_BY_DATE , headers=HEADER , params=requests_body)
        if response.status_code == 200 :
            response = json.loads(response.content)
            if 'result' in response and response['result']:
                data = response['result']
                status, reason = load_data_to_temp_tscmsl(new_oracle_session,data)
                if not status:
                    raise Exception(reason)
                else :
                    file_log = update_file_log_tscmsl(new_oracle_session,file_log=file_log,row_count=len(data),column_count=len(data[0].keys()),status=RUNNING,commit=True)
            else:
                print("extract_and_load_by_api_TSCMSL",response)
            return file_log.file_id
        else:
            print("extract_and_load_by_api_TSCMSL",response.status_code) 
        return file_log.file_id     
    except Exception as error:
        print_error("extract_and_load_by_api_TSCMSL",error)
        file_log = update_file_log_tscmsl(new_oracle_session,file_log=file_log,reason=response["message"],status=FAILED,commit=True)
        raise Exception(error)
    
def extract_and_load_by_file_TSCMSL(sftp_conn_id, sftp_path, sftp_archived_path , temp_table, job_name):
    try:
        # Connect to local DB...
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        sftp_hook = SSHHook(ssh_conn_id = sftp_conn_id)
        sftp_client = sftp_hook.get_conn().open_sftp()
        files = sftp_client.listdir(sftp_path)
        # Check for file...
        if not files:
            file_log_object = {
                "trigger_type": SCHEDULED_JOB,
                "status": FAILED,
                "job_name": job_name
            }
            file_log = create_file_log_tscmsl(new_oracle_session, file_log_object)
            raise Exception(DIRECTORY_IS_EMPTY)
        # fetch file ...
        file = sorted(
            [file_path for file_path in files if TSCMSL_FILE_NAME in file_path],
            key=(lambda file_path: sftp_client.stat(sftp_path + file_path).st_mtime)
        )
        if not file:
            file_log_object = {
                "trigger_type": SCHEDULED_JOB,
                "status": FAILED,
                "job_name": job_name
            }
            file_log = create_file_log_tscmsl(new_oracle_session, file_log_object)
            raise Exception(FILE_NOT_FOUND)
        fp = BytesIO()
        file=file[0]
        sftp_client.getfo(sftp_path + file, fp)
        data = fp.getvalue().decode("unicode_escape")
        file_log_object = {
            "file_name": file,
            "trigger_type": SCHEDULED_JOB,
            "file_format": os.path.splitext(file)[1],
            "status": RUNNING,
            "row_count": 0,
            "column_count": "",
            "job_name": job_name
        }
        status, file_log, file_data = validate_file_tscmsl(file_log_object, data)
        file_log = create_file_log_tscmsl(new_oracle_session, file_log)
        if not status:
            if file_log.status == FAILED:
                sftp_client.rename(sftp_path+file,sftp_archived_path+file)
            raise Exception(file_log.reason)
        status, reason = load_data_to_temp_for_tscmsl(new_oracle_session,file_data ,temp_table)
        
        if not status:
            file_log['reason'] = reason
            raise Exception(file_log['reason'])
        else:
            # ----------------------Move file to archival-------------------------------
            sftp_client.rename(sftp_path+file,sftp_archived_path+file)
            pass
        file_log = update_file_log_tscmsl(new_oracle_session,file_log=file_log,commit=True)
        return file_log.file_id
    except Exception as error:
        print_error("extract_and_load_by_file_TSCMSL",error)
        file_log = update_file_log_tscmsl(new_oracle_session,file_log=file_log,reason=str(error)[:60],status=FAILED,commit=True)
        # send_failed_notification(file_logs=file_log)
        raise Exception(error)


def update_upn_and_send_email(ti):
    try:
        # Connect to local DB...
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        # Fetch data from tamp table
        file_id = ti.xcom_pull(task_ids = LOAD_TO_TEMP)
        if not file_id:
            raise Exception('File ID not found')
        file_log = new_oracle_session.query(ProcessLogs).filter(ProcessLogs.file_id == file_id).first()
        if not file_log:
            raise Exception('File Log not found')
        update_upn_in_cet(new_oracle_session, file_id)
        update_file_log(new_oracle_session, file_log = file_log, status = SUCCESS, reason = None, commit = True)
        send_success_email(file_log.file_id)
    except Exception as error:
        update_file_log(new_oracle_session, file_log = file_log, status = FAILED, reason = str(error)[:50], commit = True)
        send_failed_notification(file_log)
        raise Exception(error)
