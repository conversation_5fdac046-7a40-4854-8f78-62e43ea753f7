FROM python:3.9
ENV PYTHONUNBUFFERED 1
RUN mkdir /opt/oracle
WORKDIR /opt/oracle
RUN apt-get update && apt-get install unixodbc -y && apt-get install tdsodbc -y && apt-get install nano && apt-get install -y libx11-6
COPY odbc.ini /etc/
RUN wget https://download.oracle.com/otn_software/linux/instantclient/214000/instantclient-basic-linux.x64-********.0dbru.zip
RUN unzip instantclient-basic-linux.x64-********.0dbru.zip
RUN apt install libaio1
RUN sh -c "echo /opt/oracle/instantclient_21_4 > /etc/ld.so.conf.d/oracle-instantclient.conf"
RUN ldconfig
RUN curl -LO https://curl.se/download/curl-7.88.1.tar.gz && \
    tar -xvf curl-7.88.1.tar.gz

RUN curl -L https://github.com/GNOME/librsvg/archive/2.56.93.tar.gz -o librsvg-2.56.93.tar.gz \
  && tar -xvzf librsvg-2.56.93.tar.gz

RUN curl -L https://storage.googleapis.com/downloads.webmproject.org/releases/webp/libwebp-1.3.2.tar.gz -o libwebp-1.3.2.tar.gz \
  && tar -xvzf libwebp-1.3.2.tar.gz

RUN curl -L https://ftp.gnu.org/gnu/glibc/glibc-2.36.tar.gz -o glibc-2.36.tar.gz \
  && tar -xvzf glibc-2.36.tar.gz
 
RUN ls
WORKDIR /
COPY requirements.txt /
RUN pip install -r requirements.txt