# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator

from commons.db_connections import create_AZURE_mssql_session
from commons.configs import (
    DAG9_SYNC_AZURE_MASTERS,
    DAG9_SYNC_AWS_CET_MASTER_SCHEDULAR,
    SAP_PERSONAL_AREA,
    SAP_PERSONAL_SAREA,
    SAP_ORG_UNIT,
    SAP_FUNCTION_GRP,
    SAP_SUB_FUNCTION1_GRP,
    SAP_SUB_FUNCTION2_GRP,
    SAP_ORG_LEVEL1,
    SAP_ORG_LEVEL2,
    SAP_ORG_LEVEL3,
    SAP_COST_CENTER,
    SAP_PAY_AREA,
)

from operations.sync_cet_masters_operations import (
    sync_sap_personal_area_master,
    sync_sap_personal_subarea,
    sync_sap_org_unit,
    sync_sap_function_grp,
    sync_sap_sub_function1_grp,
    sync_sap_sub_function2_grp,
    sync_sap_org_level1,
    sync_sap_org_level2,
    sync_sap_org_level3,
    sync_sap_cost_center,
    sync_sap_pay_area,
)


# DAG initialization...
with DAG(
    dag_id=DAG9_SYNC_AZURE_MASTERS,
    start_date=datetime(2023, 7, 21),
    schedule_interval=DAG9_SYNC_AWS_CET_MASTER_SCHEDULAR,
    catchup=False,
) as dag:

    # Task 1 Initialization...
    sap_personal_area = PythonOperator(
        task_id=SAP_PERSONAL_AREA,
        python_callable=sync_sap_personal_area_master,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    # Task 2 Initialization...
    sap_personal_sarea = PythonOperator(
        task_id=SAP_PERSONAL_SAREA,
        python_callable=sync_sap_personal_subarea,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_org_unit = PythonOperator(
        task_id=SAP_ORG_UNIT,
        python_callable=sync_sap_org_unit,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_function_grp = PythonOperator(
        task_id=SAP_FUNCTION_GRP,
        python_callable=sync_sap_function_grp,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_sub_function1_grp = PythonOperator(
        task_id=SAP_SUB_FUNCTION1_GRP,
        python_callable=sync_sap_sub_function1_grp,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_sub_function2_grp = PythonOperator(
        task_id=SAP_SUB_FUNCTION2_GRP,
        python_callable=sync_sap_sub_function2_grp,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_org_level1 = PythonOperator(
        task_id=SAP_ORG_LEVEL1,
        python_callable=sync_sap_org_level1,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_org_level2 = PythonOperator(
        task_id=SAP_ORG_LEVEL2,
        python_callable=sync_sap_org_level2,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_org_level3 = PythonOperator(
        task_id=SAP_ORG_LEVEL3,
        python_callable=sync_sap_org_level3,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_cost_center = PythonOperator(
        task_id=SAP_COST_CENTER,
        python_callable=sync_sap_cost_center,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    sap_pay_area = PythonOperator(
        task_id=SAP_PAY_AREA,
        python_callable=sync_sap_pay_area,
        do_xcom_push=True,
        op_kwargs={
            "session": create_AZURE_mssql_session,
        },
    )
    # Task 1 Call...
    (
        sap_personal_area
        >> sap_personal_sarea
        >> sap_org_unit
        >> sap_function_grp
        >> sap_sub_function1_grp
        >> sap_sub_function2_grp
        >> sap_org_level1
        >> sap_org_level2
        >> sap_org_level3
        >> sap_cost_center
        >> sap_pay_area
    )
