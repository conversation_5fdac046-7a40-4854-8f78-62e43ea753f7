from commons.db_connections import create_new_oracle_session, create_oracle_session
from commons.common_functions import update_file_log, get_total_count, print_error
from scripts.scheduled_job import extract_and_load_common
from airflow.operators.python import PythonOperator
from commons.mains import perform_operations
from commons.configs import LOAD_TO_TEMP, TMML_CO_CODE
# from commons.models import ESubGroup, ESubGroupTMML, FileLogs
from commons.models_v2 import ESubGroup, ESubGroupTMML, ProcessLogs
from commons.notifications import send_success_email
from commons.messages import SUCCESS

def load_to_temp_table(sftp_connection_id, sftp_path, sftp_archived_path, temp_table, job_name):
    try:
        return extract_and_load_common(
            sftp_conn_id=sftp_connection_id,
            sftp_path=sftp_path,
            sftp_archived_path=sftp_archived_path,
            temp_table=temp_table,
            job_name=job_name
        )
    except Exception as error:
        print_error("load_to_temp_table", error)


def load_to_cets(limit, offset, temp_table, comp_code, **kwargs):
    try:
        ti = kwargs["ti"]
        file_id = ti.xcom_pull(task_ids=LOAD_TO_TEMP)
        if not file_id:
            raise Exception('File ID not found')
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        file_log = new_oracle_session.query(ProcessLogs).filter(ProcessLogs.file_id == file_id).first()
        if not file_log:
            raise Exception('File Log not found')
        white_collar_data = (
            new_oracle_session.query(temp_table)
            .join(ESubGroup, ESubGroup.esubgroup_type==True)
            .filter(ESubGroupTMML.esubgroup_code == temp_table.esubgroup)
            .order_by(temp_table.perno)
            .limit(limit)
            .offset(offset) 
            if comp_code != TMML_CO_CODE 
            else new_oracle_session.query(temp_table)
            .join(ESubGroupTMML, ESubGroupTMML.esubgroup_type==True)
            .filter(ESubGroupTMML.esubgroup_code == temp_table.esubgroup)
            .order_by(temp_table.perno)
            .limit(limit)
            .offset(offset)
            ).all()
        for items in white_collar_data:
            perform_operations(new_oracle_session, items, file_log.file_id)
        blue_coller_data = (
            new_oracle_session.query(temp_table)
            .join(ESubGroup, ESubGroup.esubgroup_type==False)
            .filter(ESubGroupTMML.esubgroup_code == temp_table.esubgroup)
            .order_by(temp_table.perno)
            .limit(limit)
            .offset(offset)
            if comp_code != TMML_CO_CODE 
            else new_oracle_session.query(temp_table)
            .join(ESubGroupTMML, ESubGroupTMML.esubgroup_type==False)
            .filter(ESubGroupTMML.esubgroup_code == temp_table.esubgroup)
            .order_by(temp_table.perno)
            .limit(limit)
            .offset(offset)
            ).all()
        for items in blue_coller_data:
            perform_operations(new_oracle_session, items, file_log.file_id)
        update_file_log(
            new_oracle_session, file_log=file_log,reason=None, commit=True
        )
    except Exception as error:
        print("load_to_cets", error)

# Function to create dynamic tasks
def create_dynamic_tasks(dag, temp_table, comp_code = ""):
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        data_count = get_total_count(table=temp_table, session=new_oracle_session)
        workers = 4
        limit = data_count // workers
        remaining = data_count % workers

        tasks = []
        for i in range(workers):
            offset = i * limit
            if remaining and i == workers - 1:
                limit += remaining

            task_id = f"worker_{i+1}"
            task = PythonOperator(
                task_id=task_id,
                python_callable=load_to_cets,
                op_kwargs={"limit": limit, "offset": offset, "temp_table": temp_table, "comp_code":comp_code},
                dag=dag,
            )
            tasks.append(task)

        return tasks

    except Exception as error:
        print(error)
        return []

def send_success_email_and_update_log(ti):
    try:
        file_log_id = ti.xcom_pull(task_ids=LOAD_TO_TEMP)
        session = create_oracle_session()
        file_log = session.query(ProcessLogs).filter(ProcessLogs.file_id == file_log_id).first()
        update_file_log(session, file_log=file_log, status=SUCCESS, commit=True)
        send_success_email(file_log_id)
    except Exception as error:
        print_error("send_success_email", error)
