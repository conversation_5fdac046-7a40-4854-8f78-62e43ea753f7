from os import getenv
from typing import Literal

BACKEND_BASE_URL         = getenv('BACKEND_BASE_URL','')
BACKEND_BASE_URL_HEADERS = getenv('BACKEND_BASE_URL_HEADERS','')
MAILBOX_SERVER_DOMAIN    = getenv('MAILBOX_SERVER_DOMAIN','')
MAILBOX_PORT             = getenv('MAILBOX_PORT','')
STANDARD_IV              = getenv('STANDARD_IV', '')
DKEY                     = getenv('DKEY', '')
# Partner onboarding
PARTNER_ONBOARDING_URL   = getenv('PARTNER_ONBOARDING_URL','')
PARTNER_ONBOARDING_BASE_URL_HEADERS = getenv('PARTNER_ONBOARDING_BASE_URL_HEADERS','')
# EDP PostgreSQL
EDP_CONN_ID              = "EDP_CONN"
EDP_SCHEMA_NAME          = getenv('EDP_SCHEMA_NAME', '')
EDP_BC_SCHEMA_NAME       = getenv('EDP_BC_SCHEMA_NAME', '')
EDP_BC_CV = "EDP_BC_CV"
EDP_BC_PV = "EDP_BC_PV"
EDP_BC_EV = "EDP_BC_EV"
# MSSQL
MSSQL_CONN_ID            = "AWS_CET_CONN"
MSSQL_SCHEMA_NAME        = 'WebSystem_Dev'
AWS_SERVER               = getenv('AWS_SERVER','')
AWS_DATABASE             = getenv('AWS_DATABASE','')
AWS_USERNAME             = getenv('AWS_USERNAME','')
AWS_DRIVER               = 'FreeTDS'
#NEXTGEN
NEXTGEN_CONN_ID          = "NEXTGEN_CONN"
#ORACLE 
ORACLE_CONN_ID           = "INTERNAL_DB"
NEW_ORACLE_CONN_ID       = "NEW_INTERNAL_DB"


# %40 refers @ in below string with encodeing mechanism...
AWS_PASSWORD             = getenv('AWS_PASSWORD','')



# AZURE CONN
AZURE_CONN_ID            = "AZURE_CET_CONN"
AZURE_SCHEMA_NAME        = 'WebSystem'
AZURE_SERVER               = getenv('AZURE_SERVER','')
AZURE_DATABASE             = getenv('AZURE_DATABASE','')
AZURE_USERNAME             = getenv('AZURE_USERNAME','')
AZURE_PASSWORD             = getenv('AZURE_PASSWORD','').replace('@','%40').replace('_','#')
AZURE_DRIVER               = 'FreeTDS'
# DAG Labels
DAG1_MANUAL_JOBS = "manual_job"
DAG2_DELETE_JOBS = "delete_job"
DAG3_IMAC_JOBS   = "imac_Job"
DAG4_DAILY_REPORT_JOBS = "24_hours_manual_adid_report_job" #"Manual ADID Daily Update Jobs"
DAG5_EV_SCHEDULED_JOBS = "ev_schedule_job"
DAG6_PV_SCHEDULED_JOBS = "pv_schedule_job"
DAG7_CV_SCHEDULED_JOBS = "cv_schedule_job"
DAG8_MAILBOX_AUTOFIXER_JOBS = "mailbox_autofixer_job"
DAG9_SYNC_AWS_MASTERS = "sync_aws_masters"
DAG10_TMLBSL_SCHEDULED_JOBS = "tmlbsl_schedule_job"
DAG11_TTL_SCHEDULED_JOBS  = "ttl_schedule_job"
DAG12_NDA_SCHEDULED_JOBS = "nda_schedule_job"
DAG13_TSCMSL_SCHEDULED_JOBS = "tscmsl_schedule_job"
DAG14_DISABLE_DELETE_NDA_DOMAIN_ID_JOBS = "disable_delete_nda_domain_id_schedule_job"
DAG15_SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN_ID_JOBS = "reminder_email_for_disable_nda_domain_id_schedule_job"
DAG17_SEND_WELCOME_EMAIL = "welcome_email_send_schedule_job"
DAG18_UNHIDE_DOMAINID_FROM_ADDRESS_LIST = "unhide_domainId_from_address_list"
DAG9_SYNC_AZURE_MASTERS = "sync_azure_masters"
DAG16_AZURE_CET_MASTERS_SYNC = "sync_azure_cet_masters"
DAG19_TMML_SCHEDULED_JOBS = "tmml_schedule_job"
DAG20_BULK_UPDATE_JOB = "bulk_update_job"
DAG21_DUMP_TRIGGER_JOB = "dump_trigger_job"
DAG22_TMLDA_SCHEDULED_JOBS = "tmlda_schedule_job"

# Task Labels
TRIGGER_EMAILS = 'trigger_email'
TRIGGER_IMAC   = "trigger_imac"
LOAD_TO_CET_AD = 'load-to-cet_ad'
DELETE_TASK    = 'delete_task'
DISABLE_ENABLED_ID = 'disable_manually_enabled_id'
SUCCESS_EMAIL  = "success_email"
SEND_IMAC_NOTIFICATION = "success_email"
SEND_JOB_FAILED_NOTIFICATION = 'success_email'
LOAD_TO_TEMP = 'load-to-temp'
LOAD_TO_SAP = 'load-to-sap'
LOAD_TO_AD = 'load-to-ad'
LOAD_TO_AD_CET = 'load-to-ad-cet'
LOAD_TO_AWS_CET = 'load-to-aws-cet'
LOAD_TO_EDP_CET = 'load-to-edp-cet'
LOAD_TO_AZURE_CET = 'load-to-azure-cet'
LOAD_TO_NEXTGEN_CET = 'load-to-nextgen-cet'
LOAD_TO_BLUECOLLAR_KEYCLOACK = 'load-to-bluecollar-keycloak'
UPDATE_UPN_AND_SEND_EMAIL = 'update-upn-and-send-email'
SEND_MAIL = "send-mail"
TRIGGER_ID = "trigger_id"
DELETE_JOB = "delete_job"
CHECK_CONNECTION_MAILBOX = "check_mailbox_conection"
PRINT_CMD_OUTPUT = 'print_output_task'
TRIGGER_MAILBOX_AUTOFIXER_API = "trigger_mailbox_autofixer_api"
CLEAN_XCOM = "clean_xcom"
NDA_ID_CREATION = "nda_id_creation"
DISABLE_NDA_DOMAIN = 'disable-nda-domain'
DELETE_NDA_DOMAIN  = 'delete-nda-domain'
NDA_SYNC_JOB = 'nda_sync_job'
SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN = 'send-email-before-disable-nda-domain'
SEND_EMAIL_BEFORE_DISABLE_NDA_DOMAIN_TO_EMPLOYEE = 'send-email-before-disable-nda-domain-to-employee'
SEND_WELCOME_EMAIL = 'send-weclome-email'
ID_SEPARATION = 'id-separation'
UNHIDE_DOMAIN_ID = 'unhide-domainId'
SAP_PERSONAL_AREA = "sap_personal_area"
SAP_PERSONAL_SAREA = "sap_personal_sarea"
SAP_ORG_UNIT = "sap_org_unit"
SAP_FUNCTION_GRP = "sap_function_grp"
SAP_SUB_FUNCTION1_GRP = "sap_sub_function1_grp"
SAP_SUB_FUNCTION2_GRP = "sap_sub_function2_grp"
SAP_ORG_LEVEL1 = "sap_org_level1"
SAP_ORG_LEVEL2 = "sap_org_level2"
SAP_ORG_LEVEL3 = "sap_org_level3"
SAP_COST_CENTER = "sap_cost_center"
SAP_PAY_AREA = "sap_pay_area"


# Bash commands
CHECK_MAILBOX_CONN_TELNET_CMD = f"telnet {MAILBOX_SERVER_DOMAIN} {MAILBOX_PORT}"

# Scheduler Event-Times
DAG1_MANUAL_JOB_SCHEDULER        = None
DAG2_DELETE_JOB_SCHEDULER        = None
DAG3_IMAC_JOB_SCHEDULER          = "00 02 * * *"
DAG4_DAILY_REPORT_JOB_SCHEDULER  = "30 18 * * *"
DAG5_EV_SAP_AUTOSYNC_SCHEDULER      = "30 21 * * *"                   #"0 3 * * *"       
DAG6_PV_SAP_AUTOSYNC_SCHEDULER      = "45 21 * * *"        #"30 13 * * *"
DAG7_CV_SAP_AUTOSYNC_SCHEDULER      = "00 23 * * *"        #"30 13 * * *"
DAG8_MAILBOX_AUTOFIXER_SCHEDULER = None        #"30 13 * * *"
DAG9_SYNC_AWS_CET_MASTER_SCHEDULAR = "30 15 * * *"
DAG10_TMLBSL_SAP_AUTOSYNC_SCHEDULER      = "15 22 * * *"
DAG11_TTL_SAP_AUTOSYNC_SCHEDULER = "30 18 * * *"
DAG12_NDA_NDA_AUTOSYNC_SCHEDULER = None
DAG13_TSCMSL_SAP_AUTOSYNC_SCHEDULER = "30 22 * * *"        #"4 AM"
DAG14_DISABLE_DELETE_NDA_AUTOSYNC_SCHEDULER = "30 18 * * *"
DAG15_SEND_EMAIL_BEFORE_DISABLE_NDA_AUTOSYNC_SCHEDULER = "30 18 * * *"
DAG17_SEND_WELCOME_EMAIL_SCHEDULER          = "30 18 * * *"
DAG18_UNHIDE_DOMAINID_FROM_ADDREES_LIST_AUTOSYNC_SCHEDULER = None
DAG19_SYNC_AZURE_CET_MASTER_SCHEDULAR = "30 15 * * *"
DAG19_TMML_SAP_AUTOSYNC_SCHEDULER = "30 22 * * *"
DAG16_SYNC_AZURE_CET_MASTERS_SCHEDULAR      = None 
DAG20_BULK_UPDATE_SCHEDULER = None
DAG22_TMLDA_SAP_AUTOSYNC_SCHEDULER      = "00 23 * * *" 
# Send notification to
SEND_IMAC_JOB_NOTIFICATION_TO = '<EMAIL>'
SUBJECT_IMAC_JOB_NOTIFICATION = 'IMAC job Ran Successfully'
BODY_IMAC_JOB_NOTIFICATION    = "IMAC job Ran Successfully"

SEND_JOB_FAILED_NOTIFICATION_TO = '<EMAIL>'
SUBJECT_JOB_FAILED_NOTIFICATION = 'Update about task for Failed Jobs'
SEND_IMAC_JOB_NOTIFICATION_TO = '<EMAIL>'

SEND_TREMINATION_TO = ['<EMAIL>','<EMAIL>','<EMAIL>']
SUBJECT_TERMINATION = 'DEV - Job Execution was Terminated'

SEND_EMAIL_TO = '<EMAIL>'
PROD_EMAIL_TO = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'


# Companies
CV = "CV"
PV = "PV"
EV = "EV"
TMBSL = "TMBSL"
TMML = "TMML"
TMDA = "TMDA"
CV_CO_CODE = "100"
PV_CO_CODE = "550"
EV_CO_CODE = "650"
TMBSL_CO_CODE = "300"
TMML_CO_CODE = "700"
TMML_CO_CODE_0 = "0700"
TMML_CO_DEP_CODE = "701"
TMML_CO_DEP_CODE_0 = "0701"
TMLDA_CO_CODE = "1200"

# AD URLS
# AD_LDAP_DELETE_URL                    = BACKEND_BASE_URL + "/api/emp-onb/ad-ldap-interface/delete-ad-user"
# AD_LDAP_UPDATE_AD_ATTRIBUTE_URL       = BACKEND_BASE_URL + "/api/emp-onb/ad-ldap-interface/update-ad-user-attributes"
# AD_LDAP_INSERT_AD_ATTRIBUTE_URL       = BACKEND_BASE_URL + "/api/emp-onb/ad-ldap-interface/add-new-user-to-group"
# AD_LDAP_GET_AD_ATTRIBUTE_BY_PERNO_URL = BACKEND_BASE_URL + "/api/emp-onb/ad-ldap-interface/get-ad-users-detail-by-perno"
AD_LDAP_USER_URL                      = BACKEND_BASE_URL + "/api/ad/user"

# SAP UPN EMAIL UPDATE URLS
SAP_UPDATE_UPN_EMAIL_URL = BACKEND_BASE_URL + "/api/sap/update-details"
# SAP_CV_UPDATE_UPN_EMAIL_URL           = BACKEND_BASE_URL + "/api/emp-onb/sap-services/update-upn-email-cv-users"
# SAP_PV_UPDATE_UPN_EMAIL_URL           = BACKEND_BASE_URL + "/api/emp-onb/sap-services/update-upn-email-pv-users"
# SAP_EV_UPDATE_UPN_EMAIL_URL           = BACKEND_BASE_URL + "/api/emp-onb/sap-services/update-upn-email-ev-users"
# SAP_TMLBSL_UPDATE_UPN_EMAIL_URL       = BACKEND_BASE_URL + "/api/emp-onb/sap-services/update-upn-email-tmlbsl-users"
# SAP_TMML_UPDATE_UPN_EMAIL_URL         = BACKEND_BASE_URL + "/api/emp-onb/sap-services/update-upn-email-tmml-users"

#SAP GET EMPLOYEE_DETAILS
SAP_USER_DETAILS_BY_PERSON_NO = BACKEND_BASE_URL + "/api/sap/user"
# SAP_CV_USER_DETAILS_BY_PERSON_NO      = BACKEND_BASE_URL + "/api/emp-onb/sap-services/get-cv-user-details"
# SAP_PV_USER_DETAILS_BY_PERSON_NO      = BACKEND_BASE_URL + "/api/emp-onb/sap-services/get-pv-user-details"
# SAP_EV_USER_DETAILS_BY_PERSON_NO      = BACKEND_BASE_URL + "/api/emp-onb/sap-services/get-ev-user-details"
# SAP_TMBSL_USER_DETAILS_BY_PERSON_NO   = BACKEND_BASE_URL + "/api/emp-onb/sap-services/get-tmlbsl-user-details"
# SAP_TMML_USER_DETAILS_BY_PERSON_NO   = BACKEND_BASE_URL + "/api/emp-onb/sap-services/get-tmml-user-details"
# SAP_TTL_USER_DETAILS_BY_DATE          = BACKEND_BASE_URL + "/api/emp-onb/sap-services/get-all-ttl-user-details"
SAP_TTL_USER_DETAILS_BY_DATE          = BACKEND_BASE_URL + "/api/sap/get-details-ttl"
# IMAC URLS
# IMAC_CREATE_TICKET                    = BACKEND_BASE_URL + "/api/emp-onb/external-interface/create-ticket-for-catalog-itsm"
IMAC_CREATE_TICKET                    = BACKEND_BASE_URL + "/api/itsm/create-itsm-ticket-catalog"

# IMAC_SEND_NOTIFICATION_EMAIL          = BACKEND_BASE_URL + "/api/emp-onb/send-imac-success-email"
IMAC_SEND_NOTIFICATION_EMAIL          = BACKEND_BASE_URL + "/api/alerts/send-imac-success-mail"

# Notifications
# SEND_SUCCESS_EMAIL                    = BACKEND_BASE_URL + "/api/emp-onb/send-success-email"
SEND_SUCCESS_EMAIL                    = BACKEND_BASE_URL + "/api/alerts/send-success-mail"
# SEND_EMP_WELCOME_EMAIL                    = BACKEND_BASE_URL + "/api/emp-onb/send-welcome-email"
SEND_EMP_WELCOME_EMAIL                    = BACKEND_BASE_URL + "/api/alerts/send-welcome-mail"
# SEND_NOTIFICATION                     = BACKEND_BASE_URL + "/api/emp-onb/send-notification"
SEND_NOTIFICATION                     = BACKEND_BASE_URL + "/api/alerts/send-notification"

# Manual Job daily update notification
# SEND_DAILY_MANUAL_JOB_DETAILS         = BACKEND_BASE_URL + "/api/emp-onb/send-daily-manual-job-details"
SEND_DAILY_MANUAL_JOB_DETAILS         = BACKEND_BASE_URL + "/api/alerts/send-daily-manual-job-details"

# Mailbox Autofixer backend API
# AUTOFIX_MAILBOX_FOR_CREATED_USERS     = BACKEND_BASE_URL + "/api/emp-onb/ad-ldap-interface/autofix-mailbox-for-created-users"
AUTOFIX_MAILBOX_FOR_CREATED_USERS     = BACKEND_BASE_URL + "/api/adid/autofix-mailbox-for-created-users"

# Domain ID Creation
PARTNER_ONBOARDING_ID_CREATIONS       = PARTNER_ONBOARDING_URL + "/api/nda/create-pending-domainids/"

# People strong Api
# SAP_TSCMSL_UPDATE_UPN_EMAIL_URL      = BACKEND_BASE_URL + "/api/emp-onb/sap-services/update-upn-email-tscmsl-users"
SAP_TSCMSL_UPDATE_UPN_EMAIL_URL      = BACKEND_BASE_URL + "/api/sap/update-details-tscmsl"
SAP_TSCMSL_USER_DETAILS_BY_DATE      = ""
SAP_TSCMSL_USER_DETAILS_BY_PERSON_NO = ""
# SAP_TSCMSL_USER_ACCESS_REVOCATION    = BACKEND_BASE_URL + "/api/emp-onb/sap-services/sap-tscmsl-user-access-revocation"
SAP_TSCMSL_USER_ACCESS_REVOCATION    = BACKEND_BASE_URL + "/api/sap/tscmsl-access-revocation"

# NDA DLETE DISABLE
PARTNER_ONBOARDING_DELETE_DOMAIN_ID_URL                                    = PARTNER_ONBOARDING_URL + "/api/nda/delete-employee-details"
PARTNER_ONBOARDING_DISABLE_DOMAIN_ID_URL                                   = PARTNER_ONBOARDING_URL + "/api/nda/disable-employee-details"
PARTNER_ONBOARDING_DELETE_DOMAIN_ID_URL_V2                                 = PARTNER_ONBOARDING_URL + "/api/nda/delete-employee-details-v2"
PARTNER_ONBOARDING_DISABLE_DOMAIN_ID_URL_V2                                = PARTNER_ONBOARDING_URL + "/api/nda/disable-employee-details-v2"
PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DELETE_URL                            = PARTNER_ONBOARDING_URL + "/api/nda/get-domain-id-for-delete"
PARTNER_ONBOARDING_GET_DOMAIN_ID_FOR_DISABLE_URL                           = PARTNER_ONBOARDING_URL + "/api/nda/get-domain-id-for-disable"
PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_URL                 = PARTNER_ONBOARDING_URL + "/api/nda/send-email-before-disable-employee-details"
PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_URL_V2              = PARTNER_ONBOARDING_URL + "/api/nda/send-email-before-disable-employee-details-v2"
PARTNER_ONBOARDING_SEND_EMAILTO_MANAGER_SPOC_BEFORE_DISABLE_DOMAIN_ID_URL  = PARTNER_ONBOARDING_URL + "/api/nda/send-email-to-manager-spoc-before-disable-employee"
PARTNER_ONBOARDING_SEND_EMAIL_BEFORE_DISABLE_DOMAIN_ID_TO_EMPLOYEE_URL     = PARTNER_ONBOARDING_URL + "/api/nda/send-email-employee-before-disable-employee-details"
PARTNER_ONBOARDING_GET_TML_MANAGER_TO_SEND_DOMAIN_ID_DISABLE_REMINDER_URL  = PARTNER_ONBOARDING_URL + "/api/nda/get-tml-manager-to-send-domain-ids-disable-remimder"
PARTNER_ONBOARDING_SYNC_DOMAIN_IDS                                         = PARTNER_ONBOARDING_URL + "/api/nda/update-ad-validity-sync"
PARTNER_ONBOARDING_BULK_REQUEST_DATA_TO_UPDATE_AD                          = PARTNER_ONBOARDING_URL + "/api/nda/bulk-update-request-data-to-update-ad"

# NDA DUMP URLS
PARTNER_ONBOARDING_EMP_SPOC_DUMP                                           = PARTNER_ONBOARDING_URL + "/api/nda/get-emp-dump"
PARTNER_ONBOARDING_VENDOR_DUMP                                             = PARTNER_ONBOARDING_URL + "/api/nda/get-vendor-dump"

PARTNER_ONBOARDING_DUMP_SCHEDULER = "0 */3 * * *"

# KEYCLOAK URLS
# GET_TOKEN_FROM_KEYCLOAK = BACKEND_BASE_URL + "/api/emp-onb/sso-services/get-token"
# GET_USER_FROM_KEYCLOAK = BACKEND_BASE_URL + "/api/emp-onb/sso-services/get-user"
GET_USER_FROM_KEYCLOAK = BACKEND_BASE_URL + "/api/blue-collar/get-blue-collar-keycloak-user"
# REGISTER_USER_IN_KEYCLOAK = BACKEND_BASE_URL + "/api/emp-onb/sso-services/register-user"
REGISTER_USER_IN_KEYCLOAK = BACKEND_BASE_URL + "/api/blue-collar/register-blue-collar-keycloak-user"
# UPDATE_USER_IN_KEYCLOAK = BACKEND_BASE_URL + "/api/emp-onb/sso-services/update-user"
UPDATE_USER_IN_KEYCLOAK = BACKEND_BASE_URL + "/api/blue-collar/update-blue-collar-keycloak-user"

# SFTP
EV_SFTP_CONN_ID = 'EV_SAP_SFTP'
PV_SFTP_CONN_ID = 'PV_SAP_SFTP'
CV_SFTP_CONN_ID = 'CV_SAP_SFTP'
TMLBSL_SFTP_CONN_ID = 'TMLBSL_SAP_SFTP'
TSCMSL_SFTP_CONN_ID = "TSCMSL_SAP_SFTP"
TMML_SFTP_CONN_ID = "TMML_SAP_SFTP"
TMLDA_SFTP_CONN_ID = 'TMDA_SAP_SFTP'
EV_SFTP_ORIGINAL_PATH = getenv('EV_SFTP_ORIGINAL_PATH','')
PV_SFTP_ORIGINAL_PATH = getenv('PV_SFTP_ORIGINAL_PATH','')
CV_SFTP_ORIGINAL_PATH = getenv('CV_SFTP_ORIGINAL_PATH','')
TMLBSL_SFTP_ORIGINAL_PATH = getenv('TMLBSL_SFTP_ORIGINAL_PATH','')
TSCMSL_SFTP_ORIGINAL_PATH = getenv('TSCMSL_SFTP_ORIGINAL_PATH','')
TMLDA_SFTP_ORIGINAL_PATH = getenv('TMLDA_SFTP_ORIGINAL_PATH','')
TMML_SFTP_ORIGINAL_PATH = getenv('TMML_SFTP_ORIGINAL_PATH','')
EV_SFTP_ARCHIVED_PATH = getenv('EV_SFTP_ARCHIVED_PATH','')
PV_SFTP_ARCHIVED_PATH = getenv('PV_SFTP_ARCHIVED_PATH','')
CV_SFTP_ARCHIVED_PATH = getenv('CV_SFTP_ARCHIVED_PATH','')
TMLBSL_SFTP_ARCHIVED_PATH = getenv('TMLBSL_SFTP_ARCHIVED_PATH','')
TSCMSL_SFTP_ARCHIVED_PATH = getenv('TSCMSL_SFTP_ARCHIVED_PATH','')
TMML_SFTP_ARCHIVED_PATH = getenv('TMML_SFTP_ARCHIVED_PATH','')
TMLDA_SFTP_ARCHIVED_PATH = getenv('TMLDA_SFTP_ARCHIVED_PATH','')
# SFTP_ARCHIVED_PATH = '/HRCET/Dev/ARCHIVAL/'
# SFTP_ORIGINAL_PATH = '/HRCET/Dev/ORIGINAL/'
FILE_PATTERN = 'TML_CET_20221228276767_TEST'

# MESSAGES
CREATED_BY = "System"
DATA_NOT_FOUND = 'Data not found'
DOMAIN = getenv('DOMAIN','')

HEADER = {
    'content-type': 'application/json',
    'unsecured': 'true',
    'origin': BACKEND_BASE_URL_HEADERS
}

NDA_HEADER = {
    'content-type': 'application/json',
    'origin': PARTNER_ONBOARDING_BASE_URL_HEADERS
}

REQUEST_BODY =  {
    "auser_name": "airflow"
}
COLUMN_COUNT = 108

REQUIRED_COLUMNS = ['PERNO', 'COMP_CODE', 'PERS_AREA', 'PATXT', 'EGROUP', 'PTEXT', 'ESUBGROUP', 'ESGTXT', 'CCODETXT', 'BUS_AREA', 'BUSTXT', 'P_SUBAREA', 'PSATXT', 'PAYAREA', 'PAYTXT', 'COSTCENTER', 'COSCTRTXT', 'POSITION1', 'POSTXT', 'JOB', 'JOBTXT', 'FKBTX', 'INITIALS', 'COMP_NAME', 'LAST_NAME', 'FIRSTNAME', 'MIDNM', 'TITLE', 'OFF_NUM', 'GENDER', 'KONFE', 'RELIGION', 'FAMST', 'MAR_STATUS', 'BLOODGROUP', 'IMAILID', 'EXMAILID', 'DOBSORP', 'DOJ', 'DOB', 'DSVCVP', 'DLPROM', 'DOSEP', 'SEPRSN', 'CHALLENGED', 'REPORTING', 'NAMEOFREPORTING', 'EMPSTATUS', 'EMPSTATTXT', 'ORGEH', 'ORGTX', 'OFFICE_MOBILE', 'PERS_MOBILE', 'EMRG_MOBILE', 'TRANSPORT_CODE', 'OFF_ADDR', 'SEPCODE', 'FUN', 'FUNT', 'MGTXT', 'FLAG1', 'LAST_PROM1', 'LAST_PROM2', 'LAST_PROM3', 'DUMMY1', 'DUMMY2', 'DUMMY3', 'DUMMY4', 'DUMMY5', 'BHR_PERNO', 'MAT1_MNGR', 'MAT2_MNGR', 'MAT3_MNGR', 'MAT4_MNGR', 'MAT5_MNGR', 'HEAD_HR_PERNO', 'ER_HR_PERNO', 'OU_SHORT_TXT', 'REG_COD', 'REG_COD_TXT', 'JOBTXT_SHORT', 'OU_LEVEL1', 'OU_LEVEL1_SHORT_TXT', 'OU_LEVEL1_LONG_TXT', 'OU_LEVEL2', 'OU_LEVEL2_SHORT_TXT', 'OU_LEVEL2_LONG_TXT', 'OU_LEVEL3', 'OU_LEVEL3_SHORT_TXT', 'OU_LEVEL3_LONG_TXT', 'FUNC_ID01', 'FUNCTION_TEXT', 'SUBFUNCTION1_ID', 'SUBFUNCTION1_TEX', 'SUBFUNCTION2_ID', 'SUBFUNCTION2_TEX', 'LOC_COD', 'LOC_DESC', 'ADD1', 'ADD2', 'ADD3', 'ADD4', 'CITY_TOWN', 'STATE', 'PIN_CODE', 'COUNTRY', 'A962_PERNO', 'A962_CNAME']


# Backend APIs

# Internal Oracle Database

# External configurations

## AWS CET

## EDP CET

## AD


MANDATORY_AD_FIELDS = [
    'userprincipalname',
    'samaccountname',
]

COMMA = ","
VALID_CSV_FORMATS = ['.csv','.CSV']
LOG_CREATED_BY = 'System'
SPECIAL_CHAR = 'ï»¿'
SPEC_CHARACTERS = "!#$@"
TSCMSL_COMP = "TSCMSL"
AD_PASSWORD = 'abc1234ABC'
STATUS_CODE = '104'
REQUEST_RECEIVED = 'REQUEST_RECEIVED'
REQUEST_RECIEVED_SUCCESSFULLY = 'Request received successfully!'
# Smart city Keys Array
TSCMSL_FILE_NAME = 'Employeemaster'
TSCMSL_FILE_ARRAY = ['person_no', 'company_code', 'company_text', 'pers_area_text', 'pers_subarea_text', 'employee_subgroup_code', 'employee_subgroup_text', 'cost_center_code', 'cost_center_text', 'position_text', 'salutation', 'first_name', 'middle_name', 'last_name', 'dob', 'reporting_person_no', 'org_unit_text', 'office_telephone_no', 'office_mobile_no', 'personal_mobile_no', 'company_email', 'ou_level1_short_text', 'ou_level2_short_text', 'ou_level3_long_text', 'function_id_text', 'subfunction_id1_text', 'location_text', 'add1', 'add2', 'add3', 'add4', 'city', 'state', 'pincode', 'country', 'pers_area_code', 'pers_subarea_code', 'employee_group_code', 'employee_group_text', 'payroll_area_code', 'payroll_area_text', 'position_code', 'job_code', 'job_text', 'bus_area_code', 'bus_area_text', 'fkbtx', 'full_name', 'initials', 'gender', 'religion_code', 'religion_text', 'maritial_status_code', 'maritial_status_text', 'blood_group', 'doj', 'dobsorp', 'dsvcvp', 'dlprom', 'dosep', 'seprsn', 'challenged', 'reporting_fullname', 'employee_status_code', 'employee_status_text', 'org_unit_code', 'emergency_mobile_no', 'personal_email', 'transport_code', 'office_address', 'sep_code', 'function_group_code', 'function_group_text', 'hiring_reason', 'flag1', 'last_prom1_date', 'last_prom2_date', 'last_prom3_date', 'dummy1', 'dummy2', 'dummy3', 'dummy4', 'dummy5', 'bhr_perno', 'mat1_mngr', 'mat2_mngr', 'mat3_mngr', 'mat4_mngr', 'mat5_mngr', 'head_hr_perno', 'er_hr_perno', 'ou_short_text', 'region_code', 'region_text', 'jobtxt_short', 'ou_level1_code', 'ou_level1_long_text', 'ou_level2_code', 'ou_level2_long_text', 'ou_level3_code', 'ou_level3_short_text', 'function_id_code', 'subfunction_id1_code', 'subfunction_id2_code', 'subfunction_id2_text', 'location_code', 'a962_perno', 'a962_fullname']

TEMP_TABLE = ['perno','comp_code','ccodetxt','patxt','psatxt','esubgroup' ,'esgtxt','costcenter', 'cosctrtxt', 'postxt', 'title','firstname', 'midnm', 'last_name', 'dob', 'reporting', 'orgtx', 'off_num', 'office_mobile','pers_mobile','imailid', 'ou_level1_short_txt', 'ou_level2_short_txt', 'ou_level3_long_txt','function_text', 'subfunction1_tex', 'loc_desc',  'add1', 'add2', 'add3', 'add4',"city_town", 'state', 'pin_code', 'country', 'pers_area', 'p_subarea','egroup', 'ptext', 'payarea', 'paytxt', 'position1', 'job', 'jobtxt', 'bus_area','bustxt', 'fkbtx', 'comp_name', 'initials', 'gender', 'konfe', 'religion', 'famst','mar_status', 'bloodgroup', 'doj', 'dobsorp', 'dsvcvp', 'dlprom', 'dosep', 'seprsn', 'challenged','nameofreporting', 'empstatus', 'empstattxt', 'orgeh', 'emrg_mobile', 'exmailid','transport_code', 'off_addr', 'sepcode', 'fun', 'funt', 'mgtxt', 'flag1', 'last_prom1','last_prom2', 'last_prom3', 'dummy1', 'dummy2', 'dummy3', 'dummy4', 'dummy5', 'bhr_perno','mat1_mngr', 'mat2_mngr', 'mat3_mngr', 'mat4_mngr', 'mat5_mngr', 'head_hr_perno', 'er_hr_perno','ou_short_txt', 'reg_cod', 'reg_cod_txt', 'jobtxt_short', 'ou_level1', 'ou_level1_long_txt','ou_level2', 'ou_level2_long_txt', 'ou_level3', 'ou_level3_short_txt', 'func_id01','subfunction1_id', 'subfunction2_id', 'subfunction2_tex', 'loc_cod', 'a962_perno','a962_cname']

FEMALE = ['Mrs','Ms','Dr.(Ms)']
MALE = ['Mr','Doctor','Colonel','Captain','Brigadier','Major','Lieutenent','Sqn.Ldr','LtColonel']

# ID SEPARATION

DAG16_ID_SEPARATION_SCHEDULE_JOB = "id_separation_schedule_job"
DAG16_ID_SEPARATION_AUTOSYNC_SCHEDULER = "30 18 * * *"

# SAP_GET_EMPLOYEE_DETAILS_BY_PERNO = BACKEND_BASE_URL + "/api/emp-onb/sap-services/get-employee-details-by-perno"
SAP_GET_EMPLOYEE_DETAILS_BY_PERNO = BACKEND_BASE_URL + "/api/sap/id-separation"
# SAP_UPDATE_DATE_SEPARATION = BACKEND_BASE_URL + "/api/emp-onb/sap-services/update-date-separation"
SAP_UPDATE_DATE_SEPARATION = BACKEND_BASE_URL + "/api/sap/update-id-separation"

REVOKED_SEPARATION_DATE = "9999-12-31"

AD_ATTRIBUTES = [
    'EmployeeID', 'extensionAttribute1', 'extensionAttribute5', 'pager', 'givenName', 'sn', 'initials', 'title', 'otherTelePhone', 'manager', 'company', 'department', 'st', 'co', 'l', 'postalCode', 'telephoneNumber', 'mobile', 'homePhone', 'info', 'extensionAttribute3', 'extensionAttribute2', 'msExchAssistantName', 'telePhoneAssistant', 'extensionAttribute10', 'extensionAttribute9', 'extensionAttribute11', 'extensionAttribute12', 'extensionAttribute13', 'facsimileTelephoneNumber', 'physicalDeliveryOfficeName', 'streetAddress', 'DisplayName', 'division'
]

# Marcopolo
TMML_ESUBGROUP_FOR_ACCOUNT_EXPIRY = ['E6','E7','E8','E9','E10','EA' ,'EB','EC','ED','EE','EF','EG','EH','EI','EJ','EK','EL','EM','EN','EO','EP','EQ','ER','ES','ET','EU','EV','EW','EX','EY','EZ']

AD_ALL = "*"
AD_WHENCREATED = "whencreated"
AD_WHENCHANGED = "whenchanged"

UAC_RESET_PASSWORD_ON_LOGIN = 544
UTF_16_LE = "utf-16-le"

AD_DISTINGUISHED_NAME = "distinguishedName"   #DistinguishedName
AD_EMPLOYEE_ID = "EmployeeID"
AD_SAM_ACCOUNT_NAME = "sAMAccountName"
AD_MAIL = "mail"
AD_USER_ACCOUNT_CONTROL = "userAccountControl"
AD_UNICODE_PWD = "unicodePwd"
AD_GIVEN_NAME = "givenName"
AD_SN = "sn"
AD_CN = "cn"
AD_EXT_ATTR_1 = "extensionAttribute1"
AD_EXT_ATTR_2 = "extensionAttribute2"
AD_EXT_ATTR_3 = "extensionAttribute3"
AD_EXT_ATTR_4 = "extensionAttribute4"
AD_EXT_ATTR_5 = "extensionAttribute5"  #extensionAttribute5
AD_EXT_ATTR_6 = "extensionAttribute6"
AD_EXT_ATTR_7 = "extensionAttribute7"
AD_EXT_ATTR_8 = "extensionAttribute8"
AD_EXT_ATTR_9 = "extensionAttribute9"
AD_EXT_ATTR_10 = "extensionAttribute10"
AD_EXT_ATTR_11 = "extensionAttribute11"
AD_EXT_ATTR_12 = "extensionAttribute12"
AD_EXT_ATTR_13 = "extensionAttribute13"
AD_EXT_ATTR_14 = "extensionAttribute14"
AD_EXT_ATTR_15 = "extensionAttribute15"
AD_EXT_ATTR_16 = "extensionAttribute16"

MODIFY_ADD:       Literal["MODIFY_ADD"]
MODIFY_DELETE:    Literal["MODIFY_DELETE"]
MODIFY_REPLACE:   Literal["MODIFY_REPLACE"]
MODIFY_INCREMENT: Literal["MODIFY_INCREMENT"]

EQUIVALENT_TIMESTAMP = ***********
# AD_DOMAIN_NAME                 = getenv('AD_DOMAIN_NAME','').upper()
# LDAP_SERVER_HOST               = getenv('LDAP_SERVER_HOST','')
# LDAP_USER_NAME                 = getenv('LDAP_USER_NAME','')
# LDAP_USER_PASSWORD             = getenv('LDAP_USER_PASSWORD','').replace('.','$').replace('_','#')
# BASE_OU_DOMAIN                 = getenv('BASE_OU_DOMAIN','').replace('/','=')
# OBJECT_CLASS                   = getenv('OBJECT_CLASS','')
# LDAP_USER_EMAIL                = getenv('LDAP_USER_EMAIL','').replace('/','=')

SAP = "sap"
AD = "ad"
AWS = "aws"
AZURE = "azure"
EDP = "edp"
NEXTGEN = "nextgen"
BLUECOLLAR = "blue collar"