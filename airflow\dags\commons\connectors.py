# In-built imports...
from airflow.providers.oracle.hooks.oracle import OracleH<PERSON>
from airflow.hooks.postgres_hook import PostgresHook
from airflow.providers.microsoft.mssql.hooks.mssql import Ms<PERSON>ql<PERSON>ook
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine import Engine
from sqlalchemy import create_engine
# Custom imports...
from commons.configs import EDP_CONN_ID, EDP_SCHEMA_NAME, MSSQL_CONN_ID, MSSQL_SCHEMA_NAME, AWS_USERNAME, AWS_PASSWORD, AWS_SERVER, AWS_DATABASE, AWS_DRIVER , AZURE_CONN_ID, AZURE_DATABASE, AZURE_DRIVER, AZURE_PASSWORD, AZURE_SCHEMA_NAME, AZURE_SERVER, AZURE_USERNAME, EDP_BC_CV, EDP_BC_PV, EDP_BC_EV, EDP_BC_SCHEMA_NAME
from commons.common_functions import print_error

#---------------session-----------------
def create_oracle_session():
    try:
        hook = OracleHook(oracle_conn_id='INTERNAL_DB')
        engine: Engine = hook.get_sqlalchemy_engine()
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('sync_record_in_active_directory',error)
        raise Exception(error)
    
def create_NEXTGEN_oracle_session():
    try:
        hook = OracleHook(oracle_conn_id='NEXTGEN_CONN') #### connection id will set in airflow
        engine: Engine = hook.get_sqlalchemy_engine()
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('sync_record_in_active_directory',error)
        raise Exception(error)

def create_EDP_postgres_session():
    try:
        hook = PostgresHook(postgres_conn_id = EDP_CONN_ID, schema = EDP_SCHEMA_NAME)
        engine: Engine = hook.get_sqlalchemy_engine()
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('sync_record_in_active_directory',error)
        raise Exception(error)


def create_AWSCET_MSSQL_session():
    try:
        connection_string = f"mssql+pyodbc://{AWS_USERNAME}:{AWS_PASSWORD}@{AWS_SERVER}/{AWS_DATABASE}?driver={AWS_DRIVER}"
        engine = create_engine(connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('sync_record_in_active_directory',error)
        raise Exception(error)

def create_AZURECET_MSSQL_session():
    try:
    # hook = MsSqlHook(mssql_conn_id = MSSQL_CONN_ID, schema = MSSQL_SCHEMA_NAME)
        connection_string = f"mssql+pyodbc://{AZURE_USERNAME}:{AZURE_PASSWORD}@{AZURE_SERVER}/{AZURE_DATABASE}?driver={AZURE_DRIVER}"
        engine = create_engine(connection_string)
        # engine = create_engine(connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('sync_record_in_active_directory',error)
        raise Exception(error)
#--------------cursor-------------------------------

def create_oracle_cursor():
    hook = OracleHook(oracle_conn_id = 'INTERNAL_DB')
    conn = hook.get_conn()
    conn.autocommit = True
    cursor = conn.cursor()
    return cursor


def create_EDP_postgres_cursor():
    pg_hook = PostgresHook(postgres_conn_id = EDP_CONN_ID, schema = EDP_SCHEMA_NAME)
    get_pg_conn = pg_hook.get_conn()
    get_pg_conn.autocommit = True
    curr = get_pg_conn.cursor()
    return curr

def create_AWSCET_MSSQL_cursor():
    hook = MsSqlHook(mssql_conn_id = MSSQL_CONN_ID, schema = MSSQL_SCHEMA_NAME)
    conn = hook.get_conn()
    curr = conn.cursor()
    return curr, conn

def create_AZURECET_MSSQL_cursor():
    hook = MsSqlHook(mssql_conn_id = AZURE_CONN_ID, schema = AZURE_SCHEMA_NAME)
    conn = hook.get_conn()
    # conn.autocommit = True
    curr = conn.cursor()
    return curr, conn

def create_EDP_BC_CV_postgres_session():
    try:
        hook = PostgresHook(postgres_conn_id = EDP_BC_CV, schema = EDP_BC_SCHEMA_NAME)
        engine: Engine = hook.get_sqlalchemy_engine()
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('create_EDP_BC_CV_postgres_session',error)
        raise Exception(error)
    
def create_EDP_BC_PV_postgres_session():
    try:
        hook = PostgresHook(postgres_conn_id = EDP_BC_PV, schema = EDP_BC_SCHEMA_NAME)
        engine: Engine = hook.get_sqlalchemy_engine()
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('create_EDP_BC_PV_postgres_session',error)
        raise Exception(error)
    
def create_EDP_BC_EV_postgres_session():
    try:
        hook = PostgresHook(postgres_conn_id = EDP_BC_EV, schema = EDP_BC_SCHEMA_NAME)
        engine: Engine = hook.get_sqlalchemy_engine()
        Session = sessionmaker(bind=engine)
        session = Session()
        return session
    except Exception as error:
        print_error('create_EDP_BC_EV_postgres_session',error)
        raise Exception(error)