# In-built imports...
# Custom imports...
from commons.configs import LOAD_TO_TEMP
from commons.messages import MANUAL_TRIGGER, FAILED, MANUAL, RUNNING
from commons.db_connections import create_new_oracle_session
from commons.common_functions import create_file_log, print_error, update_file_row_log, validate_fields
from commons.mains import perform_operations
# from commons.models import ManualTempTable
from commons.models_v2 import ManualTempTable, ProcessLogs, ProcessRowLogs

# DAG 1 : Manual JOb trigger...
def manual_load_to_cet_ad_(ti, cet = None):
    try:
        # Connect to local DB...
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        # Fetch data from tamp table
        temp_table_data = new_oracle_session.query(ManualTempTable)
        file_id = ti.xcom_pull(task_ids = LOAD_TO_TEMP)
        if not file_id:
            raise Exception('File ID not found')
        file_log = new_oracle_session.query(ProcessLogs).filter(ProcessLogs.file_id == file_id).first()
        if not file_log:
            raise Exception('File Log not found')
        for items in temp_table_data:
            file_row_log = new_oracle_session.query(ProcessRowLogs).filter(ProcessRowLogs.perno==items.perno,ProcessRowLogs.file_id == file_log.file_id).first()
            if not file_row_log:
                file_row_log = update_file_row_log(
                    new_oracle_session, 
                    perno = items.perno, 
                    comp_code = items.comp_code, 
                    is_upn_update = False, 
                    file_id = file_log.file_id,
                    commit = False
                )
            status, error = validate_fields(
                new_oracle_session, 
                items
            )
            if status == False:
                file_row_log = update_file_row_log(
                    new_oracle_session,
                    file_row_log = file_row_log,
                    edp_status = FAILED,
                    edp_reason = error,
                    nextgen_status = FAILED,
                    nextgen_reason = error,
                    aws_cet_status = FAILED,
                    aws_cet_reason = error,
                    azure_cet_status = FAILED,
                    azure_cet_reason = error,
                    ad_status = FAILED,
                    ad_reason = error,
                    keycloak_status = FAILED,
                    keycloak_reason = error,
                    is_upn_update = False,
                    commit = True
                )
            else:
                perform_operations(new_oracle_session, items, file_log.file_id, file_row_log, cet)
        # update_file_log(new_oracle_session, file_log = file_log, status = SUCCESS, reason = None, commit = True)
        # send_success_email(file_log.file_id)
    except Exception as error:
        # update_file_log(new_oracle_session,file_log = file_log, status = FAILED, reason = str(error)[:50], commit = True)
        # send_failed_notification(file_log)
        print_error("manual_load_to_cet_ad_", error)
        raise Exception(error)
    

def create_manual_file_log():
    try :
        new_oracle_session = create_new_oracle_session()
        # Fetch data from tamp table
        temp_table_data = new_oracle_session.query(ManualTempTable)
        first_row = temp_table_data[0]
        file_log_object = {
            "file_name": MANUAL_TRIGGER,
            "trigger_type": MANUAL,
            "file_format":None,
            "status":RUNNING,
            "row_count": temp_table_data.count(),
            "column_count": "108",
            "trigger_by" : first_row.created_by if first_row.created_by != "System" else "System",
            "job_name":"Manual"
        }
        file_log = create_file_log(new_oracle_session, file_log_object)
        return file_log.file_id
    except Exception as error:
        print_error("create_manual_file_log",error)
        raise Exception(error)