# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
# from scripts.nda_delete_disable_job import nda_send_email_before_disable_domain_id
from commons.notifications import send_emp_welcome_email
from commons.configs import DAG17_SEND_WELCOME_EMAIL_SCHEDULER, DAG17_SEND_WELCOME_EMAIL, SEND_WELCOME_EMAIL


# DAG initialization...
with DAG(
    dag_id = DAG17_SEND_WELCOME_EMAIL,
    start_date = datetime(2024, 3, 1),
    schedule_interval = DAG17_SEND_WELCOME_EMAIL_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    send_welcome_email = PythonOperator(
        task_id = SEND_WELCOME_EMAIL,
        python_callable = send_emp_welcome_email,
        do_xcom_push = True    
    )

    # Task sequencing & call...
    send_welcome_email