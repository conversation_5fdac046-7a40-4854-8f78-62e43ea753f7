import requests
from commons.configs import PARTNER_ONBOARDING_EMP_SPOC_DUMP, PARTNER_ONBOARDING_VENDOR_DUMP, HEADER

def emp_spoc_dump_trigger():
    response = requests.get(url=PARTNER_ONBOARDING_EMP_SPOC_DUMP, headers=HEADER)
    print("EmpSPOCDump Status Code ------",response.status_code)
    print("EmpSPOCDump Response ------",response.content)
        
def vendor_dump_trigger():
    response = requests.get(url=PARTNER_ONBOARDING_VENDOR_DUMP, headers=HEADER)
    print("VendorDump Status Code ------",response.status_code)
    print("VendorDump Response ------",response.content)