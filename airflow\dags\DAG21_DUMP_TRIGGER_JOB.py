from airflow import DAG
from datetime import datetime
from airflow.operators.python import Python<PERSON>perator
from commons.configs import DAG21_DUMP_TRIGGER_JOB, PARTNER_ONBOARDING_DUMP_SCHEDULER
from scripts.hourly_dump_job import emp_spoc_dump_trigger, vendor_dump_trigger


with DAG(
    dag_id = DAG21_DUMP_TRIGGER_JOB,
    start_date = datetime(2025, 4, 15),
    schedule_interval = PARTNER_ONBOARDING_DUMP_SCHEDULER,
    catchup = False,
) as dag:
    
    emp_spoc_dump_task = PythonOperator(
        task_id = 'emp_spoc_dump_trigger',
        python_callable = emp_spoc_dump_trigger
    )
    
    vendor_dump_task = PythonOperator(
        task_id = 'vendor_dump_trigger',
        python_callable= vendor_dump_trigger
    )

emp_spoc_dump_task >> vendor_dump_task