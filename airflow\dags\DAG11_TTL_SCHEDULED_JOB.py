# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.configs import  DAG11_TTL_SCHEDULED_JOBS, DAG11_TTL_SAP_AUTOSYNC_SCHEDULER, LOAD_TO_TEMP, AWS, AZURE, LOAD_TO_AWS_CET, LOAD_TO_AZURE_CET, LOAD_TO_SAP, LOAD_TO_TEMP, SAP, UPDATE_UPN_AND_SEND_EMAIL
from commons.models_v2 import TTLTempTable
from scripts.scheduled_job import extract_and_load_TTL, schedule_load_to_cet, update_upn_and_send_email

# DAG initialization...
with DAG(
    dag_id = DAG11_TTL_SCHEDULED_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG11_TTL_SAP_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    load_to_temp_ = PythonOperator(
        task_id = LOAD_TO_TEMP,
        python_callable = extract_and_load_TTL,
        do_xcom_push = True    
    )
    
    # Task 2 Initialization...
    load_to_sap = PythonOperator(
        task_id = LOAD_TO_SAP,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TTLTempTable, "cet": SAP},
        do_xcom_push = True    
    )
    
    
    # Task 2 Initialization...
    load_to_azure_cet = PythonOperator(
        task_id = LOAD_TO_AZURE_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': TTLTempTable, "cet": AZURE},
        do_xcom_push = True    
    )
    
    update_upn_and_send_email_task = PythonOperator(
        task_id = UPDATE_UPN_AND_SEND_EMAIL,
        python_callable = update_upn_and_send_email,
        do_xcom_push = True    
    )
    
    # Task sequencing & call...
    load_to_temp_ >> load_to_sap >> [ load_to_azure_cet] >> update_upn_and_send_email_task