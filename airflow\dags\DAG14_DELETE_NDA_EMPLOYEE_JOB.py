# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from scripts.nda_delete_disable_job import nda_delete_domain_id, nda_delete_domain_id_v2, nda_disable_domain_id, nda_disable_domain_id_v2, sync_before_disable_delete_email
from commons.configs import DAG14_DISABLE_DELETE_NDA_AUTOSYNC_SCHEDULER, DAG14_DISABLE_DELETE_NDA_DOMAIN_ID_JOBS, DELETE_NDA_DOMAIN, DISABLE_NDA_DOMAIN, NDA_SYNC_JOB


# DAG initialization...
with DAG(
    dag_id = DAG14_DISABLE_DELETE_NDA_DOMAIN_ID_JOBS,
    start_date = datetime(2024, 3, 1),
    schedule_interval = DAG14_DISABLE_DELETE_NDA_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    sync_before_disable_nda_domain = PythonOperator(
        task_id = NDA_SYNC_JOB,
        python_callable = sync_before_disable_delete_email
    )

    # Task 1 Initialization...
    disable_nda_domain = PythonOperator(
        task_id = DISABLE_NDA_DOMAIN,
        python_callable = nda_disable_domain_id_v2,
        do_xcom_push = True    
    )
    
    # Task 2 Initialization...
    delete_nda_domain = PythonOperator(
        task_id = DELETE_NDA_DOMAIN,
        python_callable = nda_delete_domain_id_v2,
        do_xcom_push = True
    )

    # Task sequencing & call...
    sync_before_disable_nda_domain >> disable_nda_domain >> delete_nda_domain