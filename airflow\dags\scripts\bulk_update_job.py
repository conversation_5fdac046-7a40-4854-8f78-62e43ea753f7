# In-built imports...
import requests
# Custom imports...
from commons.configs import PARTNER_ONBOARDING_BULK_REQUEST_DATA_TO_UPDATE_AD, NDA_HEADER


def fetch_and_push_details():
    try: 
        response = requests.get(PARTNER_ONBOARDING_BULK_REQUEST_DATA_TO_UPDATE_AD, headers=NDA_HEADER, verify=False)
        print(response)
        
        response.raise_for_status()

        if response.status_code==200:
            data = response.json()

            get_data = [
                {
                    'bulk_id' : record['bulk_id'],
                    'approvedBy' : record['approvedBy'],
                    'managerId' : record['managerId'],
                    'approvalDate' : record['approvalDate'],
                    'nda_req' : nda['id']
                }
                for record in data['data']
                for nda in record['nda_req']
                ]
            for payload in get_data:
                push_response = requests.post(PARTNER_ONBOARDING_BULK_REQUEST_DATA_TO_UPDATE_AD, headers=NDA_HEADER, json=payload,verify=False)
                print(f"updated record : {push_response.json()}")
    except Exception as error:
        print(f"Failed to fetch data : {str(error)}")