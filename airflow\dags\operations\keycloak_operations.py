import requests, json, uuid
from datetime import datetime
from sqlalchemy import and_
from commons.common_functions import print_error,  validate_user_esg
from commons.configs import (
    REQUEST_BODY,
    HEADER,
    GET_USER_FROM_KEYCLOAK,
    REGISTER_USER_IN_KEYCLOAK,
    CREATED_BY,
    UPDATE_USER_IN_KEYCLOAK,
)
from commons.messages import MOBILE_NUMBER_ALREADY_EXISTS, MOBILE_NUMBER_NOT_FOUND, SUCCESS, FAILED, SKIP, INVALID_SUBGROUP, UPDATED, INSERTED, DIS<PERSON><PERSON>
from commons.models_v2 import KeyCloakMetaData, KeyCloakChangeLogHistory, EmployeeRegistration
from commons.db_connections import get_postgres_session


def sync_records_in_keycloak(oracle_session, record, file_id):
    try:
        operation = None
        status = None
        message = None
        is_valid = validate_user_esg(oracle_session=oracle_session, esubgroup=record.esubgroup, egroup=record.egroup)
        if not is_valid:
            operation, status, message = SKIP, SUCCESS, f'{INVALID_SUBGROUP} - {record.esubgroup}'
            return operation, status, message
        session = get_postgres_session(record.comp_code)
        user = get_user_data_by_employee_id(record.perno)
        engine = session.get_bind()
        url = engine.url
        print("  Database:", url.database)
        print("-" * 50)
        keycloak_metadata_exists = (
            oracle_session.query(KeyCloakMetaData)
            .filter(and_(
                KeyCloakMetaData.mobilenumber == record.pers_mobile,
                KeyCloakMetaData.person_no != record.perno
            ))
            .first()
        )
        employee_registration_exists = (
            session.query(EmployeeRegistration)
            .filter(and_(
                EmployeeRegistration.mobile_number == record.pers_mobile,
                EmployeeRegistration.employee_id != record.perno.rjust(8,'0')
            ))
            .first()
        )
        if user is None:
            if record.empstatus != "0":
                operation = INSERTED
                enabled = True
                if record.pers_mobile == None:
                    operation, status, message = operation, FAILED, MOBILE_NUMBER_NOT_FOUND
                    return operation, status, message
                if keycloak_metadata_exists or employee_registration_exists:
                    operation, status, message = operation, FAILED, MOBILE_NUMBER_ALREADY_EXISTS
                    return operation, status, message
                operation_status, operation_message = insert_user_data(record)
                if operation_status:
                    present = True
                    employee_registration = session.query(EmployeeRegistration).filter(EmployeeRegistration.employee_id == record.perno.rjust(8,'0')).first()
                    if not employee_registration:
                        employee_registration = EmployeeRegistration()
                        present = False
                        employee_registration.unique_id = uuid.uuid4()
                        employee_registration.is_pin_set = False
                        employee_registration.consent_text = None
                        employee_registration.is_deleted = False
                        employee_registration.created_by = 'System'
                        employee_registration.created_date_time = datetime.now()
                        employee_registration.employee_id = record.perno.rjust(8,'0') if record.perno else None
                    employee_registration.mobile_number = record.pers_mobile
                    employee_registration.is_active = enabled
                    employee_registration.modified_by = 'System'
                    employee_registration.modified_date_time = datetime.now()
                    if not present:
                        session.add(employee_registration)
                    session.commit()
                    keycloak_metadata = (
                        oracle_session.query(KeyCloakMetaData)
                        .filter(KeyCloakMetaData.person_no == record.perno)
                        .first()
                    )
                    if not keycloak_metadata:
                        keycloak_metadata = KeyCloakMetaData()
                        keycloak_metadata.username = record.perno.rjust(8,'0') if record.perno else None
                        keycloak_metadata.person_no = record.perno
                        keycloak_metadata.firstname = record.firstname
                        keycloak_metadata.lastname = record.last_name
                        keycloak_metadata.mobilenumber = record.pers_mobile
                        keycloak_metadata.location = record.loc_desc
                        keycloak_metadata.enabled = True
                        keycloak_metadata.created_by = (
                            CREATED_BY
                            if record.created_by is None
                            else record.created_by
                        )
                        keycloak_metadata.created_at = datetime.now()
                        keycloak_metadata.updated_at = datetime.now()
                        oracle_session.add(keycloak_metadata)
                        oracle_session.commit()
                    else :
                        change_log = KeyCloakChangeLogHistory()
                        change_log.username = keycloak_metadata.username
                        change_log.person_no = keycloak_metadata.person_no
                        change_log.firstname = keycloak_metadata.firstname
                        change_log.lastname = keycloak_metadata.lastname
                        change_log.mobilenumber = keycloak_metadata.mobilenumber
                        change_log.location = keycloak_metadata.location
                        change_log.enabled = keycloak_metadata.enabled
                        change_log.created_by = keycloak_metadata.created_by
                        change_log.created_at = keycloak_metadata.created_at
                        change_log.updated_at = keycloak_metadata.updated_at
                        change_log.file_id = file_id
                        change_log.log_created_by = (
                            CREATED_BY if record.created_by is None else record.created_by
                        )
                        change_log.log_created_at = datetime.now()
                        oracle_session.add(change_log)
                        oracle_session.commit()

                        # metadata_result.username = record.perno
                        keycloak_metadata.person_no = record.perno
                        keycloak_metadata.firstname = record.firstname
                        keycloak_metadata.lastname = record.last_name
                        keycloak_metadata.mobilenumber = record.pers_mobile
                        keycloak_metadata.location = record.loc_desc
                        keycloak_metadata.enabled = enabled
                        keycloak_metadata.created_by = (
                            CREATED_BY if record.created_by is None else record.created_by
                        )
                        keycloak_metadata.updated_at = datetime.now()
                        oracle_session.commit()
                    status = SUCCESS
                    message = None
                    return operation, status, message
                else:
                    status = FAILED
                    message = operation_message
                    return operation, status, message
            else:
                operation, status, message = SKIP, FAILED, "Disabled at time of insert"
                return operation, status, message
        else:
            if record.empstatus != "0":
                operation = UPDATED
                enabled = True
            else:
                operation = DISABLE
                enabled = False
            if record.pers_mobile == None:
                operation, status, message = operation, FAILED, MOBILE_NUMBER_NOT_FOUND
                return operation, status, message
            if keycloak_metadata_exists or employee_registration_exists:
                operation, status, message = operation, FAILED, MOBILE_NUMBER_ALREADY_EXISTS
                return operation, status, message
            metadata_result = (
                    oracle_session.query(KeyCloakMetaData)
                    .filter(KeyCloakMetaData.person_no == record.perno)
                    .first()
                )
            if not metadata_result:
                metadata_result = KeyCloakMetaData()
                metadata_result.username = user.get("username")
                metadata_result.person_no = (
                    int(user.get("username"))
                    if user.get("username").isdigit()
                    else user.get("username")
                )
                metadata_result.firstname = user.get("firstName")
                metadata_result.lastname = user.get("lastName")
                if user.get('attributes'):
                    metadata_result.mobile = user["attributes"].get("username")
                    metadata_result.location = user["attributes"].get("location")[0] if user["attributes"].get("location") else None
                metadata_result.created_at = datetime.fromtimestamp(
                    user.get("createdTimestamp") / 1000
                )
                metadata_result.updated_at = datetime.fromtimestamp(
                    user.get("createdTimestamp") / 1000
                )
                metadata_result.created_by = (
                    CREATED_BY if record.created_by is None else record.created_by
                )
                metadata_result.enabled = user.get("enabled")
                oracle_session.add(metadata_result)
                oracle_session.commit()
            
            operation_status, operation_message = update_user_data(
                record=record, enabled=enabled
            )
            if operation_status:
                present = True
                employee_registration = session.query(EmployeeRegistration).filter(EmployeeRegistration.employee_id == record.perno.rjust(8,'0')).first()
                if not employee_registration:
                    employee_registration = EmployeeRegistration()
                    present = False
                    employee_registration.unique_id = uuid.uuid4()
                    employee_registration.is_pin_set = False
                    employee_registration.consent_text = None
                    employee_registration.is_deleted = False
                    employee_registration.created_by = 'System'
                    employee_registration.created_date_time = datetime.now()
                    employee_registration.employee_id = record.perno.rjust(8,'0') if record.perno else None
                employee_registration.mobile_number = record.pers_mobile
                employee_registration.is_active = enabled
                employee_registration.modified_by = 'System'
                employee_registration.modified_date_time = datetime.now()
                if not present:
                    session.add(employee_registration)
                session.commit()
                metadata_result = (
                    oracle_session.query(KeyCloakMetaData)
                    .filter(KeyCloakMetaData.person_no == record.perno)
                    .first()
                )
                if metadata_result:
                    change_log = KeyCloakChangeLogHistory()
                    change_log.username = metadata_result.username
                    change_log.person_no = metadata_result.person_no
                    change_log.firstname = metadata_result.firstname
                    change_log.lastname = metadata_result.lastname
                    change_log.mobilenumber = metadata_result.mobilenumber
                    change_log.location = metadata_result.location
                    change_log.enabled = metadata_result.enabled
                    change_log.created_by = metadata_result.created_by
                    change_log.created_at = metadata_result.created_at
                    change_log.updated_at = metadata_result.updated_at
                    change_log.file_id = file_id
                    change_log.log_created_by = (
                        CREATED_BY if record.created_by is None else record.created_by
                    )
                    change_log.log_created_at = datetime.now()
                    oracle_session.add(change_log)
                    oracle_session.commit()

                    # metadata_result.username = record.perno
                    metadata_result.person_no = record.perno
                    metadata_result.firstname = record.firstname
                    metadata_result.lastname = record.last_name
                    metadata_result.mobilenumber = record.pers_mobile
                    metadata_result.location = record.loc_desc
                    metadata_result.enabled = enabled
                    metadata_result.created_by = (
                        CREATED_BY if record.created_by is None else record.created_by
                    )
                    metadata_result.updated_at = datetime.now()
                    oracle_session.commit()

                    status = SUCCESS
                    message = None
                    return operation, status, message
            else:
                status = FAILED
                message = operation_message
                return operation, status, message
    except Exception as error:
        print_error("sync_records_in_keycloak", error)
        status = FAILED
    return operation, status, message


def get_user_data_by_employee_id(employee_id):
    try:
        body = {**REQUEST_BODY}
        body['employee_id'] = employee_id
        response = requests.get(
            url = GET_USER_FROM_KEYCLOAK,
            headers = HEADER,
            params = body
        )
        if not response.status_code == 200:
            print("Status code - ", response.status_code)
            print("Content - ", response.content)
            return None
        data = json.loads(response.content)
        if data.get('message') != "Data retrieved successfully.":
            return None
        return data.get("result")
    except Exception as error:
        print_error("get_data_by_employee_id", error)

def insert_user_data(record):
    try:
        body = {**REQUEST_BODY}
        body['employee_id'] = record.perno
        body['first_name'] = record.firstname
        body['last_name'] = record.last_name
        body['mobile'] = record.pers_mobile
        body['location'] = record.loc_desc
        body['enabled'] = True
        response = requests.post(
            url = REGISTER_USER_IN_KEYCLOAK,
            headers = HEADER,
            json = body
        )
        if not response.status_code == 200:
            print("Status code - ", response.status_code)
            print("Content - ", response.content)
            return False, f"response - {response.status_code, response.content}"
        data = json.loads(response.content)
        if data['message'] == "Record added successfully":
            return True, data.get("message")
        return False, f"response - {data}"
    except Exception as error:
        print_error("insert_user_data", error)


def update_user_data(record, enabled):
    try:
        body = {**REQUEST_BODY}
        body["employee_id"] = record.perno
        body["first_name"] = record.firstname
        body["last_name"] = record.last_name
        body["mobile"] = record.pers_mobile
        body["location"] = record.loc_desc
        body["enabled"] = enabled
        response = requests.post(url=UPDATE_USER_IN_KEYCLOAK, headers=HEADER, json=body)
        if not response.status_code == 200:
            print("Status code - ", response.status_code)
            print("Content - ", response.content)
            return False, f"response - {response.status_code, response.content}"
        data = json.loads(response.content)
        if data['message'] == "Record updated successfully":
            return True, data.get("message")
        return False, f"response - {data}"
    except Exception as error:
        print_error("update_user_data", error)
