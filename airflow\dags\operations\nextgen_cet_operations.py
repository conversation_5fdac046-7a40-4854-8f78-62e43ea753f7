import datetime
from datetime import datetime
# Custom imports...
# from commons import connectors
from commons.db_connections import create_NEXTGEN_oracle_session
from commons import db_queries
from commons import messages
from commons import common_functions
# from commons.models import NextGenEmployeeDetails, NextGenMetaData
from commons.models_v2 import NextGenEmployeeDetails, NextGenMetaData

oracle_session_nextgen = create_NEXTGEN_oracle_session()

def update_to_nextgen_cet(oracle_session,row,file_id):
    try:
        operation = None
        status = None
        message = None
        operation = messages.INSERTED
        result = NextGenEmployeeDetails()
        result.perno = row.perno if row.perno else None
        result.pers_area = row.pers_area
        result.patxt = row.patxt
        result.egroup = row.egroup
        result.ptext = row.ptext
        result.esubgroup = row.esubgroup
        result.esgtxt = row.esgtxt
        result.comp_code = row.comp_code.rjust(4,'0') if row.comp_code != '300' else '0100'
        result.ccodetxt = row.ccodetxt
        result.bus_area = row.bus_area
        result.bustxt = row.bustxt
        result.p_subarea = row.p_subarea
        result.psatxt = row.psatxt
        result.payarea = row.payarea
        result.paytxt = row.paytxt
        result.costcenter = row.costcenter.rjust(10,'0') if row.costcenter else None
        result.cosctrtxt = row.cosctrtxt
        result.position1 = row.position1.rjust(8,'0') if row.position1 else None
        result.postxt = row.postxt
        result.job = row.job.rjust(8,'0') if row.job else None
        result.jobtxt = row.jobtxt
        result.fkbtx = row.fkbtx
        result.initials = row.initials
        result.comp_name = row.comp_name
        result.last_name = row.last_name
        result.firstname = row.firstname
        result.midnm = row.midnm
        result.title = row.title
        result.off_num = row.off_num
        result.gender = row.gender
        result.konfe = row.konfe
        result.religion = row.religion
        result.famst = row.famst
        result.mar_status = row.mar_status
        result.bloodgroup = row.bloodgroup
        result.imailid = row.imailid
        result.exmailid = row.exmailid
        result.dobsorp = row.dobsorp[:10]
        result.doj = row.doj[:10]
        result.dob = row.dob[:10]
        result.dsvcvp = row.dsvcvp[:10]
        result.dlprom = row.dlprom[:10]
        result.dosep = row.dosep[:10]
        result.seprsn = row.seprsn
        result.challenged = row.challenged
        result.reporting = row.reporting if row.reporting else None
        result.nameofreporting = row.nameofreporting
        result.empstatus = row.empstatus
        result.empstattxt = row.empstattxt
        result.orgeh = row.orgeh.rjust(8,'0') if row.orgeh else None
        result.orgtx = row.orgtx
        result.office_mobile = row.office_mobile
        result.pers_mobile = row.pers_mobile
        result.emrg_mobile = row.emrg_mobile
        result.transport_code = row.transport_code
        result.off_addr = row.off_addr
        result.sepcode = row.sepcode
        result.fun = row.fun
        result.funt = row.funt
        result.mgtxt = row.mgtxt
        result.flag1 = row.flag1
        result.last_prom1 = row.last_prom1[:10]
        result.last_prom2 = row.last_prom2[:10]
        result.last_prom3 = row.last_prom3[:10]
        result.dummy1 = row.dummy1
        result.dummy2 = row.dummy2
        result.dummy3 = row.dummy3
        result.dummy4 = row.dummy4
        result.dummy5 = row.dummy5
        result.bhr_perno = row.bhr_perno.rjust(8,'0') if row.bhr_perno else None
        result.mat1_mngr = row.mat1_mngr
        result.mat2_mngr = row.mat2_mngr
        result.mat3_mngr = row.mat3_mngr
        result.mat4_mngr = row.mat4_mngr
        result.mat5_mngr = row.mat5_mngr
        result.hd_hr_perno = row.head_hr_perno.rjust(8,'0') if row.head_hr_perno else None
        result.er_hr_perno = row.er_hr_perno.rjust(8,'0') if row.er_hr_perno else None
        result.ou_short_txt = row.ou_short_txt
        result.reg_cod = row.reg_cod
        result.reg_cod_txt = row.reg_cod_txt
        result.jobtxt_short = row.jobtxt_short
        result.ou_level1 = row.ou_level1
        result.ou_level1_short_txt = row.ou_level1_short_txt
        result.ou_level1_long_txt = row.ou_level1_long_txt
        result.ou_level2 = row.ou_level1
        result.ou_level2_short_txt = row.ou_level2_short_txt
        result.ou_level2_long_txt = row.ou_level2_long_txt
        result.ou_level3 = row.ou_level3
        result.ou_level3_short_txt = row.ou_level3_short_txt
        result.ou_level3_long_txt = row.ou_level3_long_txt
        result.func_id01 = row.func_id01
        result.function_text = row.function_text
        result.subfunction1_id = row.subfunction1_id
        result.subfunction1_tex = row.subfunction1_tex
        result.subfunction2_id = row.subfunction2_id.rjust(4,'0') if row.subfunction2_id else None
        result.subfunction2_tex = row.subfunction2_tex
        result.loc_cod = row.loc_cod
        result.loc_desc = row.loc_desc
        result.add1 = row.add1
        result.add2 = row.add2
        result.add3 = row.add3
        result.add4 = row.add4
        result.city_town = row.city_town
        result.state = row.state
        result.pin_code = row.pin_code
        result.country = row.country
        result.a962_perno = row.a962_perno.rjust(8,'0')if row.a962_perno else None
        result.a962_cname = row.a962_cname
        result.updated_on = datetime.now()
        result.created_on = datetime.now()
        oracle_session_nextgen.add(result)
        oracle_session_nextgen.commit()

        if not oracle_session.query(NextGenMetaData).filter(NextGenMetaData.person_no == row.perno).first():
            metadata_result = NextGenMetaData()
            metadata_result.person_no = row.perno
            metadata_result.created_at = datetime.now()
            metadata_result.updated_at = datetime.now()
            oracle_session.add(metadata_result)
            oracle_session.commit()

        status = messages.SUCCESS
        message = None
    except Exception as error:
        common_functions.print_error('update_to_nextgen_cet()',error)
        oracle_session.rollback()
        status = messages.FAILED
        message = str(error)[:100]
    return operation, status, message 
        
# nextgen CET Sync
# def sync_record_in_nextgen_cet(curr, record):
#     status = ""
#     message = ""
#     operation = ""
    
#     if check_if_exists_in_nextgen_table(curr, record['perno']):
#         operation = messages.UPDATED
#         status, message = update_to_nextgen_table(record)
#     else:
#         operation = messages.INSERTED
#         # status, message = insert_into_edp_table(record)
#         status, message = False, "Perno not present in CET"
#     return status, message, operation

# def check_if_exists_in_nextgen_table(curr,person_no):
#     curr.execute(db_queries.SELECT_EXISTS_NEXTGEN_TABLE,[person_no])
#     return curr.fetchone()[0]

# def update_to_nextgen_table(row):
#     try:
#         nextgen_curr = connectors.create_NEXTGEN_oracle_cursor()
#         nextgen_row = {k: v or '' for (k, v) in row.items()}
#         # nextgen_row = row.copy()
#         nextgen_row['perno'] = nextgen_row['perno'].rjust(8,'0')
#         nextgen_row['comp_code'] = nextgen_row['comp_code'].rjust(4,'0')
#         nextgen_row['costcenter'] = nextgen_row['costcenter'].rjust(10,'0')
#         nextgen_row['position1'] = nextgen_row['position1'].rjust(8,'0')
#         nextgen_row['job'] = nextgen_row['job'].rjust(8,'0')
#         nextgen_row['reporting'] = nextgen_row['reporting'].rjust(8,'0')
#         nextgen_row['orgeh'] = nextgen_row['orgeh'].rjust(8,'0')
#         nextgen_row['bhr_perno'] = nextgen_row['bhr_perno'].rjust(8,'0')
#         nextgen_row['head_hr_perno'] = nextgen_row['head_hr_perno'].rjust(8,'0')
#         nextgen_row['er_hr_perno'] = nextgen_row['er_hr_perno'].rjust(8,'0')
#         nextgen_row['subfunction2_id'] = nextgen_row['subfunction2_id'].rjust(4,'0')
#         nextgen_row['a962_perno'] = nextgen_row['a962_perno'].rjust(8,'0')
#         data = list(nextgen_row.values())
#         data.append(nextgen_row['perno'])
#         SQL = """
#             UPDATE sap_daily_sync_cet_data
#             SET "Perno"=%s, "CompCode"=%s, "PersArea"=%s, "Patxt"=%s, "Egroup"=%s, "Ptext"=%s, "Esubgroup"=%s, "Esgtxt"=%s,"Ccodetxt"=%s, "Bus_Area"=%s, "Bustxt"=%s, "PSubarea"=%s, "Psatxt"=%s, "Payarea"=%s, "Paytxt"=%s, "Costcenter"=%s, "Cosctrtxt"=%s, "Position1"=%s, "Postxt"=%s, "Job"=%s, "Jobtxt"=%s, "Fkbtx"=%s, "Initials"=%s, "CompName"=%s, "Last_Name"=%s, "Firstname"=%s, "Midnm"=%s, "Title"=%s, "Off_Num"=%s, "Gender"=%s, "Konfe"=%s, "Religion"=%s, "Famst"=%s, "Mar_Status"=%s, "Bloodgroup"=%s, "Imailid"=%s, "Exmailid"=%s, "Dobsorp"=%s, "Doj"=%s, "Dob"=%s, "Dsvcvp"=%s, "Dlprom"=%s, "Dosep"=%s, "Seprsn"=%s, "Challenged"=%s, "Reporting"=%s, "Nameofreporting"=%s, "Empstatus"=%s, "Empstattxt"=%s, "Orgeh"=%s, "Orgtx"=%s, "Office_Mobile"=%s, "Pers_Mobile"=%s, "Emrg_Mobile"=%s, "Transport_Code"=%s, "Off_Addr"=%s, "Sepcode"=%s, "Fun"=%s, "Funt"=%s, "Mgtxt"=%s, "Flag1"=%s, "Last_Prom1"=%s, "Last_Prom2"=%s, "Last_Prom3"=%s, "Dummy1"=%s, "Dummy2"=%s, "Dummy3"=%s, "Dummy4"=%s, "Dummy5"=%s, "Bhr_Perno"=%s, "Mat1_Mngr"=%s, "Mat2_Mngr"=%s, "Mat3_Mngr"=%s, "Mat4_Mngr"=%s, "Mat5_Mngr"=%s, "Hd_Hr_Perno"=%s, "Er_Hr_Perno"=%s, "Ou_Short_Txt"=%s, "Reg_Cod"=%s, "Reg_Cod_Txt"=%s, "Jobtxt_Short"=%s, "Ou_Level1"=%s, "Ou_Level1_Short_Txt"=%s, "Ou_Level1_Long_Txt"=%s, "Ou_Level2"=%s, "Ou_Level2_Short_Txt"=%s, "Ou_Level2_Long_Txt"=%s, "Ou_Level3"=%s, "Ou_Level3_Short_Txt"=%s, "Ou_Level3_Long_Txt"=%s, "Func_Id01"=%s, "Function_Text"=%s, "Subfunction1_Id"=%s, "Subfunction1_Tex"=%s, "Subfunction2_Id"=%s, "Subfunction2_Tex"=%s, "Loc_Cod"=%s, "Loc_Desc"=%s, "Add1"=%s, "Add2"=%s, "Add3"=%s, "Add4"=%s, "City_Town"=%s, "State"=%s, "PinCode"=%s, "Country"=%s, "A962_Perno"=%s, "A962_Cname"=%s, modified_datetime=CURRENT_TIMESTAMP
#             WHERE "Perno" = %s;
#             """
#         nextgen_curr.execute(SQL,data)
#         return True, 'success'
#     except Exception as error:
#         return False, str(error)
