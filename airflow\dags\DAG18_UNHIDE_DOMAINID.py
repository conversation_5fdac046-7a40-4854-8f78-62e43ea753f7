# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from scripts.unhide_dominId_job import unhide_domainId_from_address_list
from commons.configs import DAG18_UNHIDE_DOMAINID_FROM_ADDREES_LIST_AUTOSYNC_SCHEDULER, DAG18_UNHIDE_DOMAINID_FROM_ADDRESS_LIST, UNHIDE_DOMAIN_ID


# DAG initialization...
with DAG(
    dag_id = DAG18_UNHIDE_DOMAINID_FROM_ADDRESS_LIST,
    start_date = datetime(2024, 7, 3),
    schedule_interval = DAG18_UNHIDE_DOMAINID_FROM_ADDREES_LIST_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    unhide_domainId = PythonOperator(
        task_id = UNHIDE_DOMAIN_ID,
        python_callable = unhide_domainId_from_address_list,
        do_xcom_push = True    
    )

    # Task sequencing & call...
    unhide_domainId