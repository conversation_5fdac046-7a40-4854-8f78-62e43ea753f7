# In-built imports...
import time
from datetime import datetime

# Custom imports...
from commons.db_connections import create_new_oracle_session, create_oracle_session
# from commons.models import ADMetaData, SyncExclusionLogs, SapEmployeeMaster
from commons.models_v2 import ADMetaData, SyncExclusionLog, SapEmployeeMaster
from commons.common_functions import print_error
from operations.sync_exclusion_job_operations import sync_id_after_exclusion_ends, get_employee_details, emp_mapping_function


def sync_exclude_job_script():
    try:
        # oracle_session = create_oracle_session()
        new_oracle_session = create_new_oracle_session()
        sync_excluded_ids = new_oracle_session.query(SyncExclusionLog).filter(SyncExclusionLog.extended_date <= datetime.now(), SyncExclusionLog.is_deleted == False).all()
        if sync_excluded_ids:
            object_list = create_object_list(new_oracle_session,sync_excluded_ids)
            if object_list:
                    sync_id_after_exclusion_ends(object_list)

        else:
            print(f'------------------Nothing to include in sync-------------------')
    except Exception as error:
        print_error(error)
        raise Exception(error)
    
def create_object_list(oracle_session, sync_excluded_ids):
    try:
        # employee_object_list = []
        # for data in sync_excluded_ids:
        #     metadata = oracle_session.query(ADMetaData).filter(ADMetaData.user_id == data.user_id, ADMetaData.is_sync_excluded == True).first()
        #     if metadata:
        #         comp_code = oracle_session.query(SapEmployeeMaster.company_code).filter(SapEmployeeMaster.person_no == metadata.person_no).first()
        #         if comp_code:
        #             employee_details = get_employee_details(metadata.person_no,comp_code=comp_code[0])
        #             if employee_details:
        #                 employee_object = emp_mapping_function(employee_details)
        #                 if employee_object:
        #                     metadata.is_sync_excluded = False
        #                     oracle_session.commit()
        #                     employee_object_list.append(employee_object)
        #                 else:
        #                     print(f'{data.user_id} - object not created')
        #             else:
        #                 print(f'{data.user_id} - not found in SAP')
        #         else:
        #             print(f'{data.user_id} - not found in sap employee master')
        #     else:
        #         print(f'{data.user_id} - not found in metadata')
        # return employee_object_list

        employee_object_list = []
        for data in sync_excluded_ids:
            result = (
                oracle_session.query(ADMetaData, SapEmployeeMaster)
                .join(SapEmployeeMaster, SapEmployeeMaster.person_no == ADMetaData.person_no)
                .filter(
                    ADMetaData.user_id == data.user_id,
                    ADMetaData.is_sync_excluded == True
                )
                .first()
            )
            if result:
                metadata, sap_record = result
                employee_details = get_employee_details(metadata.person_no, comp_code=sap_record.company_code)
                if employee_details:
                    employee_object = emp_mapping_function(employee_details)
                    if employee_object:
                        metadata.is_sync_excluded = False
                        oracle_session.commit()
                        employee_object_list.append(employee_object)
                    else:
                        print(f'{data.user_id} - object not created')
                else:
                    print(f'{data.user_id} - not found in SAP')
            else:
                metadata = (
                    oracle_session.query(ADMetaData)
                    .filter(ADMetaData.user_id == data.user_id, ADMetaData.is_sync_excluded == True)
                    .first()
                )
                if metadata:
                    print(f'{data.user_id} - not found in sap employee master')
                else:
                    print(f'{data.user_id} - not found in metadata')

        return employee_object_list
    except Exception as error:
        print_error('perform_operation',error)
