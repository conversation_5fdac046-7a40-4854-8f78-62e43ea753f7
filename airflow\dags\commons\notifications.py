# In-built imports...
from json import loads, dumps
from requests import post
# Custom imports...
from commons.configs import REQUEST_BODY, SEND_EMAIL_TO, SEND_SUCCESS_EMAIL, HEADER, IMAC_SEND_NOTIFICATION_EMAIL, SEND_JOB_FAILED_NOTIFICATION,  SEND_JOB_FAILED_NOTIFICATION_TO, SUBJECT_JOB_FAILED_NOTIFICATION, SEND_NOTIFICATION,SEND_EMP_WELCOME_EMAIL
from commons.messages import EMAIL_SEND_SUCCESSFULLY
from commons.email_templates import FAILED_EMAIL_TEMPLATE, FAILED_EMAIL_TEMPLATE

# ====================================== Notifications : Start =============================================== #

# Send success notification...
def send_success_email(file_log_id):
    request_body = REQUEST_BODY
    request_body['file_log_id'] = file_log_id
    request_body['to_email'] = SEND_EMAIL_TO
    # del body['attributes']
    response_body = post(url = SEND_SUCCESS_EMAIL, headers = HEADER, data = dumps(request_body))
    if response_body.status_code == 200:
        response_body = response_body.content.decode('utf-8')
        response_body = loads(response_body)
        if 'message' in response_body and response_body['message'] == EMAIL_SEND_SUCCESSFULLY:
            return True
        else:
            print(f'Email Status response - {response_body}')
    else:
        print(f'Email Status Code - {response_body.status_code}')
    
# Trigger IMAC notification... 
def send_imac_notification(imac_log_id):
    request_object = REQUEST_BODY
    request_object['imac_log_id'] = imac_log_id
    response_body = post(url = IMAC_SEND_NOTIFICATION_EMAIL, headers = HEADER, data = dumps(request_object))
    if response_body.status_code == 200:
        response_body = response_body.content.decode('utf-8')
        response_body = loads(response_body)
        if 'message' in response_body and response_body['message'] == EMAIL_SEND_SUCCESSFULLY:
            return True
        else:
            print(f'Email Status response - {response_body}')
    else:
        print(f'Email Status Code - {response_body.status_code}')

# Send failed notification...
def send_failed_notification(file_logs):
    request_object = REQUEST_BODY
    request_object['to_email'] = SEND_JOB_FAILED_NOTIFICATION_TO
    request_object['subject'] = f"{file_logs.job_name} | {SUBJECT_JOB_FAILED_NOTIFICATION}"
    request_object['body'] = FAILED_EMAIL_TEMPLATE%(file_logs.file_name,file_logs.started_at,file_logs.row_count,file_logs.column_count,file_logs.status,file_logs.reason)
    response = post(url =SEND_NOTIFICATION, headers=HEADER, data = dumps(request_object))
    if response.status_code == 200:
        response_body = response.content.decode('utf-8')
        response_body = loads(response_body)  
        print(f'Send Failed Email Notification | Response - {response_body}')
    else:
        print(f'Failed | Send Failed Email Notification | Status Code - {response.status_code}')

def send_email_notification(to_email,subject,body):
    request_object = REQUEST_BODY
    request_object['to_email'] = to_email
    request_object['subject'] = subject
    request_object['body'] = body
    response = post(url =SEND_NOTIFICATION, headers=HEADER, data = dumps(request_object))
    if response.status_code == 200:
        response_body = response.content.decode('utf-8')
        response_body = loads(response_body)  
        print(f'Send Failed Email Notification | Response - {response_body}')
    else:
        print(f'Failed | Send Failed Email Notification | Status Code - {response.status_code}')

# Send success notification...
def send_emp_welcome_email():
    request_body = REQUEST_BODY
    response_body = post(url = SEND_EMP_WELCOME_EMAIL, headers = HEADER, data = dumps(request_body))
    if response_body.status_code == 200:
        response_body = response_body.content.decode('utf-8')
        response_body = loads(response_body)
        if 'message' in response_body and response_body['message'] == EMAIL_SEND_SUCCESSFULLY:
            print(f'Email Success Status response - {response_body}')
            return True
        else:
            print(f'Email Status response - {response_body}')
    else:
        print(f'Email Status Code - {response_body.status_code}')
# ====================================== Notifications : End =============================================== #