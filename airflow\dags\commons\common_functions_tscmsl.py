# In-built imports...
from datetime import datetime
import os
import sys
import time, csv, string, random
from io import String<PERSON>
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from base64 import b64encode
# Custom imports...
from commons.configs import DKEY, SPEC_CHARACTERS, SPECIAL_CHAR, STANDARD_IV, TEMP_TABLE, TSCMSL_FILE_ARRAY, VALID_CSV_FORMATS, COLUMN_COUNT, MANDATORY_AD_FIELDS, COMMA, AD_ATTRIBUTES
from commons.messages import DOB_IS_EMPTY, DOJ_IS_EMPTY, FAILED, NO_DATA_FOUND, EMPTY_FILE, INCORRECT_COLUMN_COUNT, COLUMN_NAME_DID_NOT_MATCHED, INVALID_FILE, SUCCESS
from commons.notifications import send_failed_notification
# from commons.models import FileLogs, FileRowLogs, SapCompanyMaster, SapFileRowLogTSCMSL, TSCMSLTempTable
from commons.models_v2 import ProcessLogs, ProcessRowLogs, SapCompanyMaster, SapFileRowLogTSCMSL, TSCMSLTempTable

def generate_password_tscmsl(length=10):
    uppercase_letters = string.ascii_uppercase
    lowercase_letters = string.ascii_lowercase
    special_characters = SPEC_CHARACTERS
    password = [
        random.choice(uppercase_letters),
        random.choice(lowercase_letters),
        random.choice(special_characters)
    ]
    remaining_length = length - len(password)
    all_characters = uppercase_letters + lowercase_letters + string.digits + special_characters
    password += [random.choice(all_characters) for _ in range(remaining_length)]
    random.shuffle(password)
    password_str = ''.join(password)

    return password_str

def print_error(func_name, error = ""):
    try:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb:
            file_name = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            error = f'Error - {error} \n Exception - {exc_type} \n File Name - {file_name} \n Function Name - {func_name} \n Line No - {exc_tb.tb_lineno}'
            print(error)
            return error
        else:
            error = f'{str(func_name)} : {str(error)}'
            print(error)
            return error
    except Exception as error:
        print(f'print_error(): {error}')

def encrypt_content_tscmsl(data):
    try:
        key = DKEY 
        iv = STANDARD_IV
        key = key.encode('utf-8') #16 char for AES128
        iv =  iv.encode('utf-8') #16 char for AES128
        data = pad(data.encode(),16)
        cipher = AES.new(key,AES.MODE_CBC,iv)
        cipher_data = cipher.encrypt(data)
        cipher_data = b64encode(cipher_data)
        cipher_data = cipher_data.decode("utf-8")
        return cipher_data
    except Exception as error:
        print(f'encrypt_content(): {error}')
        return None

def cleaner_tscmsl(param):
    try:
        if param == None or param == 'None':
            param = ""
        param = param.replace('.','')
        if type(param) == str:
            param = param.strip()
        if param.isnumeric():
            param = str(int(param))
        if param == "":
            param = None
        return param
    except Exception as error:
        print(f'cleaner(): {error}')
        return None

def cleaner_perno(param):
    try:
        if param == None or param == 'None':
            param = ""
        param = param.replace('.','')
        if type(param) == str:
            param = param.strip()
        if param.isnumeric():
            param = str(int(param))
        if (len(param) >= 6) and (len(param) <= 8):
            param = param
        if param == "":
            param = None
        return param
    except Exception as error:
        print(f'cleaner_perno(): {error}')
        return None

def name_cleaner_tscmsl(param):
    try:
        if param == None:
            param = ""
        # param = param.replace(' ','')
        param = param.strip()
        return cleaner_tscmsl(param)
    except Exception as error:
        print("name_cleaner(): ", error)
        return None

def add_cleaner_tscmsl(param):
    try: 
        if param == None or param == 'None':
            param = ""
        if type(param) == str:
            param = param.strip()
        if param == "":
            param = None
        return param    
    except Exception as error:
        print(f'add_cleaner(): {error}')
        return None

def format_date_tscmsl(date, date_failed_reason=None, column = ''):
    try:
        try:
            if date == 'None' or date == '' or date == None:
                return '0000-00-00', date_failed_reason
            elif date == '0000-00-00' or date == '00-00-0000':
                return str(date), date_failed_reason
            date = datetime.strptime(date[:10], '%Y-%m-%d')
        except:
            date = datetime.strptime(date[:10], '%d-%m-%Y')
        return str(date), date_failed_reason
    except Exception as error:
        print(f'format_date_tscmsl() : {error}')
        return date, f'Invalid date format in {column} - {date}'

def create_file_log_tscmsl(session, file_log_object):
    file_log = ProcessLogs()
    file_log.file_id = "FLG"+str(int(time.time_ns() * 10))
    file_log.file_name = file_log_object["file_name"] if "file_name" in file_log_object else ""
    file_log.job_name = file_log_object["job_name"] if "job_name" in file_log_object else ""
    file_log.trigger_type = file_log_object["trigger_type"] if "trigger_type" in file_log_object else ""
    file_log.file_format = file_log_object["file_format"] if "file_format" in file_log_object else ""
    file_log.status = file_log_object["status"] if "status" in file_log_object else ""
    file_log.row_count = file_log_object["row_count"] if "row_count" in file_log_object else 0
    file_log.column_count = file_log_object["column_count"] if "column_count" in file_log_object else ""
    file_log.created_at = datetime.now()
    file_log.trigger_by = file_log_object["trigger_by"] if "trigger_by" in file_log_object else "System"
    if "status" in file_log_object and file_log_object["status"] == FAILED:
        file_log.status = FAILED
        file_log.reason = file_log_object["reason"] if "reason" in file_log_object else ""
        session.add(file_log)
        session.commit()
        send_failed_notification(file_log)
        return file_log
    session.add(file_log)
    session.commit()
    return file_log

def update_file_log_tscmsl(session, file_log=None, job_name=None, file_name=None, file_format=None, row_count=None, column_count=None, status=None, reason=None, trigger_type=None, commit=None):
    is_create = False
    if file_log is None:
        file_log = ProcessLogs()
        is_create = True
        file_log.file_id = "FLG"+str(int(time.time_ns() * 10))
    if job_name is not None:
        file_log.job_name = job_name
    if file_name is not None:
        file_log.file_name = file_name
    if file_format is not None:
        file_log.file_format = file_format
    if row_count is not None:
        file_log.row_count = row_count
    if column_count is not None:
        file_log.column_count = column_count
    if status is not None:
        file_log.status = status
    if reason is not None:
        file_log.reason = reason
    if trigger_type is not None:
        file_log.trigger_type = trigger_type
    if is_create:
        file_log.created_at = datetime.now()
        session.add(file_log)
    if commit is not None:
        session.commit()
    return file_log

def update_file_row_log_tscmsl(session, file_row_log=None, perno=None, comp_code=None, row_count=None,
                        ad_operation=None, aws_cet_operation=None,azure_cet_operation=None, edp_operation=None,
                        ad_status=None, aws_cet_status=None,azure_cet_status=None, edp_status=None,ad_reason=None,
                        aws_cet_reason=None,azure_cet_reason=None, edp_reason=None,resolved_file_id=None,
                        resolved_file_row_id=None, is_upn_update=None, file_id=None, commit=True):
    is_create = False
    if file_row_log is None:
        file_row_log = ProcessRowLogs()
        is_create = True
        file_row_log.file_row_id = "ROW" + str(int(time.time_ns() * 10))
    if perno is not None:
        file_row_log.perno = perno
    if comp_code is not None:
        file_row_log.comp_code = comp_code
    if row_count is not None:
        file_row_log.row_count = row_count
    if ad_operation is not None:
        file_row_log.ad_operation = ad_operation
    if aws_cet_operation is not None:
        file_row_log.aws_cet_operation = aws_cet_operation
    if azure_cet_operation is not None:
        file_row_log.azure_cet_operation = azure_cet_operation
    if edp_operation is not None:
        file_row_log.edp_operation = edp_operation
    if ad_status is not None:
        file_row_log.ad_status = ad_status
    if aws_cet_status is not None:
        file_row_log.aws_cet_status = aws_cet_status
    if azure_cet_status is not None:
        file_row_log.azure_cet_status = azure_cet_status
    if edp_status is not None:
        file_row_log.edp_status = edp_status
    if ad_reason is not None:
        file_row_log.ad_reason = ad_reason
    if aws_cet_reason is not None:
        file_row_log.aws_cet_reason = aws_cet_reason
    if azure_cet_reason is not None:
        file_row_log.azure_cet_reason = azure_cet_reason
    if edp_reason is not None:
        file_row_log.edp_reason = edp_reason
    if resolved_file_id is not None:
        file_row_log.resolved_file_id = resolved_file_id
    if resolved_file_row_id is not None:
        file_row_log.resolved_file_row_id = resolved_file_row_id
    if is_upn_update is not None:
        file_row_log.is_upn_update = is_upn_update
    if file_id is not None:
        file_row_log.file_id = file_id
    if is_create:
        session.add(file_row_log)
    if commit is not None:
        session.commit()

    return file_row_log

# Scheduled jobs...

def split_by_pipe(row):
    row_str = row.replace(SPECIAL_CHAR,'')  if type(row) == str else row[0].replace(SPECIAL_CHAR,'')
    row_list = list(row_str.split('|'))
    return row_list


def get_row_list(row, initial_list):
    row_list = []
    row_list = split_by_pipe(row)
    if len(initial_list) != 0 and len(row_list)!=0:
        initial_list[len(initial_list)-1] = initial_list[len(initial_list)-1]+","+ row_list[0]
        row_list = initial_list + row_list[1:]
    if type(row)==list and len(row) > 1:
        row_list = get_row_list(row[1],row_list)
    return row_list

def validate_file_tscmsl(file_log_object, file_data):
    file= []
    try:
        if file_log_object['file_format'] not in VALID_CSV_FORMATS:
            file_log_object['status'] = FAILED 
        elif not file_data:
            file_log_object['status'] = FAILED
            file_log_object['reason'] = EMPTY_FILE
        else:
            csvReader = csv.DictReader(StringIO(file_data))
            file_row =[]
            columns = []
            for row in csvReader:
                row_key = list(row.keys())
                columns = split_by_pipe(row_key)
                row_values = list(row.values())
                row = get_row_list(row_values, [])
                file_row.append(row)
            file = []
            if len(file_row) == 0:
                file_log_object['status'] = FAILED
                file_log_object['reason'] = NO_DATA_FOUND
                return False, file_log_object, file
            
            file_log_object['row_count'] = len(file_row)
            file_log_object['column_count'] = len(columns)
            
            for i in range(len(file_row)):
                if len(file_row[i]) != COLUMN_COUNT:
                    file_log_object['status'] = FAILED
                    file_log_object['reason'] = INVALID_FILE
                    return False, file_log_object, file
                data_dict = dict(zip(columns, file_row[i]))
                file.append(data_dict)
                
            if file_log_object['column_count'] != COLUMN_COUNT:
                file_log_object['status'] = FAILED
                file_log_object['reason'] = INCORRECT_COLUMN_COUNT
            elif columns != TSCMSL_FILE_ARRAY:
                file_log_object['status'] = FAILED
                file_log_object['reason'] = COLUMN_NAME_DID_NOT_MATCHED
            else:
                return True, file_log_object, file
        return False, file_log_object, file     
    except Exception as error:
        file_log_object['status'] = FAILED
        file_log_object['reason'] = str(error)[:60]
        print_error('validate_file',error)
        return False, file_log_object, file

def load_data_to_temp_tscmsl(oracle_session, file_data):
    try:
        oracle_session.query(TSCMSLTempTable).delete()
        oracle_session.commit()
        object_list = []
        for row in file_data:
            if not row['perno']:
                row['perno'] = '00000000'
            if not row['comp_code']:
                row['comp_code'] = '000'
            if not row['perno'].isdigit():
                row['perno'] = int(row['perno'])
            if not row['comp_code'].isdigit():
                row['comp_code'] = int(row['comp_code'])
            table_row = assign_value_to_table_key(table_row,TEMP_TABLE,row,TSCMSL_FILE_ARRAY)
            object_list.append(TSCMSLTempTable(**table_row))
        oracle_session.add_all(object_list)
        oracle_session.commit() 
        return True, None 
    except Exception as error:
        print_error(f'load_data_to_temp_tscmsl() : {error}')
        return False, error

def validate_fields_tscmsl(session, row):
    date_failed_reason = None
    row.perno = cleaner_perno(row.perno)
    row.comp_code = cleaner_tscmsl(row.comp_code)
    row.ccodetxt = cleaner_tscmsl(row.ccodetxt)
    row.pers_area = cleaner_tscmsl(row.pers_area)
    row.patxt = cleaner_tscmsl(row.patxt)
    row.psatxt = cleaner_tscmsl(row.psatxt)
    row.orgtx = cleaner_tscmsl(row.orgtx)
    row.p_subarea = cleaner_tscmsl(row.p_subarea)
    row.firstname = name_cleaner_tscmsl(row.firstname)
    row.last_name = name_cleaner_tscmsl(row.last_name)
    row.midnm = cleaner_tscmsl(row.midnm)
    row.reporting = cleaner_tscmsl(row.reporting)
    row.city_town = cleaner_tscmsl(row.city_town)
    row.dobsorp, date_failed_reason = format_date_tscmsl(row.dobsorp, date_failed_reason, "dobsorp") 
    row.doj, date_failed_reason = format_date_tscmsl(row.doj, date_failed_reason, "doj")
    row.dob, date_failed_reason = format_date_tscmsl(row.dob, date_failed_reason, "dob")
    row.dsvcvp, date_failed_reason = format_date_tscmsl(row.dsvcvp, date_failed_reason, "dsvcvp")
    row.dlprom, date_failed_reason = format_date_tscmsl(row.dlprom, date_failed_reason, "dlprom")
    row.dosep, date_failed_reason = format_date_tscmsl(row.dosep, date_failed_reason, "dosep")
    row.last_prom1, date_failed_reason = format_date_tscmsl(row.last_prom1, date_failed_reason, "last_prom1")
    row.last_prom2, date_failed_reason = format_date_tscmsl(row.last_prom2, date_failed_reason, "last_prom2")
    row.last_prom3, date_failed_reason = format_date_tscmsl(row.last_prom3, date_failed_reason, "last_prom3")
    row.imailid = row.imailid.lower() if row.imailid else None
    row.add1 = add_cleaner_tscmsl(row.add1)
    row.add2 = add_cleaner_tscmsl(row.add2)
    row.add3 = add_cleaner_tscmsl(row.add3)
    row.add4 = add_cleaner_tscmsl(row.add4)
    if (len(str(row.perno)) != 6) or (not row.perno.isnumeric()):
        return False, f"invalid perno - {row.perno}"
    if not row.firstname:
        return False, f"firstname is empty"
    if not session.query(SapCompanyMaster).filter(SapCompanyMaster.comp_code==row.comp_code).first():
        return False, f"invalid company_code - {row.comp_code}"
    if row.dob == '0000-00-00' or row.dob == '00-00-0000':
        return False, DOB_IS_EMPTY
    if row.doj == '0000-00-00' or row.doj == '00-00-0000':
        return False, DOJ_IS_EMPTY
    if date_failed_reason != None:
        return False, date_failed_reason
    return True, row

def create_ad_request_body_tscmsl(data):
    ad_request = {}
    if all(key in data and data[key] for key in MANDATORY_AD_FIELDS):
        ad_request = {
            "samAccountName": data["samAccountName"],
            "userPrincipalName": data["userPrincipalName"]
        }
        # LastName
        if 'sn' in data and data['sn']:
            ad_request['sn'] = data['sn'].upper()
        # FirstName
        if 'givenName' in data and data['givenName']:
            ad_request['givenName'] = data['givenName'].upper()
        # MiddleName
        if 'initials' in data and data['initials']:
            ad_request['initials'] = data['initials'].upper()
        # Employee ID
        if 'EmployeeID' in data and data['EmployeeID']:
            ad_request['EmployeeID'] = data['EmployeeID']
        # manager perno
        if 'manager' in data and data['manager']:
            ad_request['manager'] = data['manager']
        # Department
        if 'department' in data and data['department']:
            ad_request['department'] = data['department']
        # physicalDeliveryOfficeName
        if 'physicalDeliveryOfficeName' in data and data['physicalDeliveryOfficeName']:
            ad_request['physicalDeliveryOfficeName'] = data['physicalDeliveryOfficeName']
        # facsimileTelephoneNumber
        if 'facsimileTelephoneNumber' in data and data['facsimileTelephoneNumber']:
            ad_request['facsimileTelephoneNumber'] = data['facsimileTelephoneNumber']
        # pager
        if 'pager' in data and data['pager']:
            ad_request['pager'] = data['pager']
        # otherTelePhone
        if 'otherTelePhone' in data and data['otherTelePhone']:
            ad_request['otherTelePhone'] = data['otherTelePhone']
        # msExchAssistantName
        if 'msExchAssistantName' in data and data['msExchAssistantName']:
            ad_request['msExchAssistantName'] = data['msExchAssistantName']
        # telePhoneAssistant
        if 'telePhoneAssistant' in data and data['telePhoneAssistant']:
            ad_request['telePhoneAssistant'] = data['telePhoneAssistant']
        # homePhone
        if 'homePhone' in data and data['homePhone']:
            ad_request['homePhone'] = data['homePhone']
        # Notes
        if 'info' in data and data['info']:
            ad_request['info'] = data['info']
        # Position
        if 'title' in data and data['title']:
            ad_request['title'] = data['title']
        # city
        if 'l' in data and data['l']:
            ad_request['l'] = data['l']
        # Company
        if 'company' in data and data['company']:
            ad_request['company'] = data['company']
        # displayName
        if 'displayName' in data and data['displayName']:
            ad_request['displayName'] = data['displayName']
        # division
        if 'division' in data and data['division']:
            ad_request['division'] = data['division']
        # ====================== Set Region details : Start ============================ #
        # Country
        if 'co' in data and data['co']:
            ad_request['co'] = data['co']
        # State
        if 'st' in data and data['st']:
            ad_request['st'] = data['st']
        # Zipcode
        if 'postalCode' in data and data['postalCode']:
            ad_request['postalCode'] = data['postalCode']
        # Address
        if 'streetAddress' in data and data['streetAddress']:
            ad_request['streetAddress'] = data['streetAddress']
        # ====================== Set Region details : End ============================ #

        # ====================== Phone Update : Start ================================= #
        # telephoneNumber
        if 'telephoneNumber' in data and data['telephoneNumber']:
            ad_request['telephoneNumber'] = data['telephoneNumber'].split(COMMA)[0]
            
        # mobile
        if 'mobile' in data and data['mobile']:
            ad_request['mobile'] = data['mobile'].split(COMMA)[0]
        # ====================== Phone Update : End ================================= #    
        
        # ====================== Extension Attribute : Start ================================= #    
        # Employee ID
        if 'extensionAttribute1' in data and data['extensionAttribute1']:
            ad_request['extensionAttribute1'] = data['extensionAttribute1']
        # Employee Subgeroup - Grade
        if 'extensionAttribute2' in data and data['extensionAttribute2']:
            ad_request['extensionAttribute2'] = data['extensionAttribute2']
        # BCNumber
        if 'extensionAttribute3' in data and data['extensionAttribute3']:
            ad_request['extensionAttribute3'] = data['extensionAttribute3']        
        # Employee ID
        if 'extensionAttribute5' in data and data['extensionAttribute5']:
            ad_request['extensionAttribute5'] = data['extensionAttribute5']
        # Below Parameter is configured for Non-user IDs
        # if 'extensionAttribute7' in data and data['extensionAttribute7'] == "Y":
        #     # The selected ID is non-user...
        #     pass
        # Gender
        if 'extensionAttribute9' in data and data['extensionAttribute9']:
            ad_request['extensionAttribute9'] = data['extensionAttribute9']
        # Salutation
        if 'extensionAttribute10' in data and data['extensionAttribute10']:
            ad_request['extensionAttribute10'] = data['extensionAttribute10']
        # personnelarea
        if 'extensionAttribute11' in data and data['extensionAttribute11']:
            ad_request['extensionAttribute11'] = data['extensionAttribute11']
        # PersonnelSubarea
        if 'extensionAttribute12' in data and data['extensionAttribute12']:
            ad_request['extensionAttribute12'] = data['extensionAttribute12']
        # Loacation description - Site
        if 'extensionAttribute13' in data and data['extensionAttribute13']:
            ad_request['extensionAttribute13'] = data['extensionAttribute13']

        # ====================== Extension Attribute : End ================================= #    
    else:
        # Missing fields...
        print("Mandatory Fields are missing...")
    return ad_request

def get_changed_attributes_for_update_tscmsl(new_data, old_data):

    for attribute in AD_ATTRIBUTES:
        if attribute not in new_data:
            new_data[attribute] = ""

    if (
        "EmployeeID" in new_data and "EmployeeID" in old_data and 
        new_data["EmployeeID"] == old_data["EmployeeID"]
    ):
        change_ad_attributes = {
            "samAccountName"     : old_data["samAccountName"],
            "userPrincipalName"  : old_data["userPrincipalName"],
            "userAccountControl" : 544
        }

        for attribute in new_data:
            changed = False
            if new_data[attribute] != "":
                if attribute not in old_data:
                    changed = True # insert
                    print(f'insert - {attribute} - {new_data[attribute]}')
                elif attribute in old_data and old_data[attribute] != new_data[attribute]:
                    changed = True # update
                    print(f'update - {attribute} - {old_data[attribute]} to {new_data[attribute]}')
            else:
                if attribute in old_data:
                    changed = True # delete
                    print(f'delete - {attribute} - {old_data[attribute]}')

            if changed:            
                change_ad_attributes[attribute] = new_data[attribute]
        
        if change_ad_attributes.get("mail"):
            del change_ad_attributes["mail"]
        if change_ad_attributes.get("info") in (None, ''):
            del change_ad_attributes["info"]
        return change_ad_attributes
    
    print("EmployeeID doesn't matched")
    return False

#-----------------Smart City ----------------------------
def assign_value_to_table_key(table_obj,table_key,file_obj,file_key):
    range_length = len(file_key)
    for i in range(range_length):
        table_obj[table_key[i]]=file_obj[file_key[i]]
    return table_obj
        
def load_data_to_temp_for_tscmsl(oracle_session, file_data , temp_table):
    try:
        oracle_session.query(temp_table).delete()
        oracle_session.commit()
        object_list = []
        for row in file_data:
            if not row['person_no']:
                row['person_no'] = '00000000'
            if not row['company_code']:
                row['company_code'] = '000'
            if row['person_no'].isdigit():
                row['person_no'] = str(int(row['person_no']))
            if row['company_code'].isdigit():
                row['company_code'] = str(int(row['company_code']))
            table_row = {}
            table_row = assign_value_to_table_key(table_row,TEMP_TABLE,row,TSCMSL_FILE_ARRAY)
            table_row['gender']=table_row['gender'][:1]
            table_row['esubgroup']=table_row['esubgroup'].replace('.','')
            object_list.append(temp_table(**table_row))
        oracle_session.add_all(object_list)
        oracle_session.commit() 
        return True, None 
    except Exception as error:
        print_error(f'load_data_to_temp_for _tscmsl() : {error}')
        return False, error
    
def insert_sap_file_row(session,file_row_id, perno, sap_status, sap_msg):
    try :
        sap_file_row = SapFileRowLogTSCMSL()
        sap_file_row.sap_file_row_id = "SAP" + str(int(time.time_ns() * 10))
        sap_file_row.file_row_id = file_row_id
        sap_file_row.perno = perno
        sap_file_row.response_status = SUCCESS if sap_status else FAILED
        sap_file_row.response_message = sap_msg
        session.add(sap_file_row)
    except Exception as error:
        print_error(f'insert_sap_file_row() : {error}')