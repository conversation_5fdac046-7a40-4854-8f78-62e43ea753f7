# In-built imports...
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
# Custom imports...
from commons.configs import DAG6_PV_SCHEDULED_JOBS, DAG6_PV_SAP_AUTOSYNC_SCHEDULER, LOAD_TO_TEMP
from commons.models_v2 import PVTempTable
from commons.configs import  AD, AWS, AZURE, B<PERSON>UECOLLAR, EDP, LOAD_TO_AD, LOAD_TO_AWS_CET, LOAD_TO_AZURE_CET, LOAD_TO_BLUECOLLAR_KEYCLOACK, LOAD_TO_EDP_CET, LOAD_TO_NEXTGEN_CET, LOAD_TO_SAP, LOAD_TO_TEMP, NEXTGEN, SAP, UPDATE_UPN_AND_SEND_EMAIL
from scripts.scheduled_job import extract_and_load_PV, schedule_load_to_cet, update_upn_and_send_email

# DAG initialization...
with DAG(
    dag_id = DAG6_PV_SCHEDULED_JOBS,
    start_date = datetime(2023, 7, 21),
    schedule_interval = DAG6_PV_SAP_AUTOSYNC_SCHEDULER,
    catchup = False,
) as dag:
    
    # Task 1 Initialization...
    load_to_temp_ = PythonOperator(
        task_id = LOAD_TO_TEMP,
        python_callable = extract_and_load_PV,
        do_xcom_push = True    
    )
    
    # Task 2 Initialization...
    load_to_sap = PythonOperator(
        task_id = LOAD_TO_SAP,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': PVTempTable, "cet": SAP},
        do_xcom_push = True    
    )
    
    load_to_ad = PythonOperator(
        task_id = LOAD_TO_AD,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': PVTempTable, "cet": AD},
        do_xcom_push = True    
    )
    
    
    # Task 2 Initialization...
    load_to_azure_cet = PythonOperator(
        task_id = LOAD_TO_AZURE_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': PVTempTable, "cet": AZURE},
        do_xcom_push = True    
    )
    
    load_to_edp_cet = PythonOperator(
        task_id = LOAD_TO_EDP_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': PVTempTable, "cet": EDP},
        do_xcom_push = True    
    )
    
    load_to_nextgen_cet = PythonOperator(
        task_id = LOAD_TO_NEXTGEN_CET,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': PVTempTable, "cet": NEXTGEN},
        do_xcom_push = True    
    )
    
    load_to_blue_collar_keycloak = PythonOperator(
        task_id = LOAD_TO_BLUECOLLAR_KEYCLOACK,
        python_callable = schedule_load_to_cet,
        op_kwargs={'temp_table': PVTempTable, "cet": BLUECOLLAR},
        do_xcom_push = True    
    )
    
    update_upn_and_send_email_task = PythonOperator(
        task_id = UPDATE_UPN_AND_SEND_EMAIL,
        python_callable = update_upn_and_send_email,
        do_xcom_push = True    
    )
    
    # Task sequencing & call...
    load_to_temp_ >> load_to_sap >> [load_to_ad, load_to_azure_cet, load_to_edp_cet, load_to_nextgen_cet, load_to_blue_collar_keycloak] >> update_upn_and_send_email_task